from typing import Literal, Callable
import pandas as pd

from app.utils.constants import Constants as const

def detect_sustained_transition(
    df: pd.DataFrame,
    col: str,
    threshold: float,
    duration: str,
    condition: Literal["gt", "gte", "lt", "lte", "eq"],
) -> "pd.Series[bool]":
    """
    Detects sustained transitions in a time series.
    :param df: DataFrame containing the time series data
    :param col: Column name to analyze
    :param threshold: Threshold value for the transition
    :param duration: Duration for which the condition must hold
    :param condition: Condition of the transition ('gt', 'gte', 'lt', 'lte' or 'eq')
    :return: DataFrame with transition flags
    """

    if df.index.name == "index":
        df.reset_index(inplace=True)

    df.set_index("index", inplace=True)

    df = df.sort_index().copy()

    match condition:
        case "gt":
            df["transition"] = (df[col].shift(1) <= threshold) & (df[col] > threshold)
        case "gte":
            df["transition"] = (df[col].shift(1) < threshold) & (df[col] >= threshold)
        case "lt":
            df["transition"] = (df[col].shift(1) >= threshold) & (df[col] < threshold)
        case "lte":
            df["transition"] = (df[col].shift(1) > threshold) & (df[col] <= threshold)
        case "eq":
            df["transition"] = (df[col].shift(1) != threshold) & (df[col] == threshold)
        case _:
            df["transition"] = (df[col].shift(1) <= threshold) & (df[col] > threshold)

    df["event_start_end"] = False

    for timestamp in df.index[df["transition"]]:
        end_time = timestamp + pd.Timedelta(duration)

        mask = (df.index >= timestamp) & (df.index <= end_time)

        if not mask.any():
            continue

        # Check if the condition holds for the entire duration
        match condition:
            case "gt":
                if (df.loc[mask, col] > threshold).all():
                    df.at[timestamp, "event_start_end"] = True
            case "gte":
                if (df.loc[mask, col] >= threshold).all():
                    df.at[timestamp, "event_start_end"] = True
            case "lt":
                if (df.loc[mask, col] < threshold).all():
                    df.at[timestamp, "event_start_end"] = True
            case "lte":
                if (df.loc[mask, col] <= threshold).all():
                    df.at[timestamp, "event_start_end"] = True
            case "eq":
                if (df.loc[mask, col] == threshold).all():
                    df.at[timestamp, "event_start_end"] = True
            case _:
                if (df.loc[mask, col] > threshold).all():
                    df.at[timestamp, "event_start_end"] = True

    return df["event_start_end"]


def detect_sustained_transition_between_boundries(
    df: pd.DataFrame,
    col: str,
    lower_condition: Literal["gt", "gte"],
    lower_boundry: float,
    upper_condition: Literal["lt", "lte"],
    upper_boundry: float,
    direction: Literal["asc", "desc"],
    duration: str,
) -> "pd.Series[bool]":
    """
    Detects if a transition in a time series sustains inside an interval during a period of time.

    :param df: DataFrame containing the time series data
    :param col: Column name to analyze
    :param lower_condition: Condition for the lower boundry ('gt', 'gte')
    :param lower_boundry: Minimum value that the series must respect
    :param upper_condition: Condition of the upper boundry ('lt', 'lte')
    :param upper_boundry: Maximum value that the series must respect
    :param direction: Whether the transition is detected in ascending or descending order
    :param duration: Duration for which the condition must hold
    :return: Series with the transition flags
    """

    if df.index.name == "index":
        df.reset_index(inplace=True)

    df.set_index("index", inplace=True)

    df = df.sort_index().copy()

    # Determine the transition based on the direction
    if direction == "asc":
        if lower_condition == "gt":
            df["transition"] = (df[col].shift(1) <= lower_boundry) & (
                df[col] > lower_boundry
            )
        elif lower_condition == "gte":
            df["transition"] = (df[col].shift(1) < lower_boundry) & (
                df[col] >= lower_boundry
            )
    elif direction == "desc":
        if upper_condition == "lt":
            df["transition"] = (df[col].shift(1) >= upper_boundry) & (
                df[col] < upper_boundry
            )
        elif upper_condition == "lte":
            df["transition"] = (df[col].shift(1) > upper_boundry) & (
                df[col] <= upper_boundry
            )

    df["event_start_end"] = False

    for timestamp in df.index[df["transition"]]:
        end_time = timestamp + pd.Timedelta(duration)

        mask = (df.index >= timestamp) & (df.index <= end_time)

        if not mask.any():
            continue

        # Check if the condition holds for the entire duration
        if lower_condition == "gt" and upper_condition == "lt":
            gt_lower = df.loc[mask, col] > lower_boundry
            lt_upper = df.loc[mask, col] < upper_boundry
            is_valid = (gt_lower & lt_upper).all()
            if is_valid:
                df.at[timestamp, "event_start_end"] = True
        elif lower_condition == "gte" and upper_condition == "lt":
            gte_lower = df.loc[mask, col] >= lower_boundry
            lt_upper = df.loc[mask, col] < upper_boundry
            is_valid = (gte_lower & lt_upper).all()
            if is_valid:
                df.at[timestamp, "event_start_end"] = True
        elif lower_condition == "gt" and upper_condition == "lte":
            gt_lower = df.loc[mask, col] > lower_boundry
            lte_upper = df.loc[mask, col] <= upper_boundry
            is_valid = (gt_lower & lte_upper).all()
            if is_valid:
                df.at[timestamp, "event_start_end"] = True
        elif lower_condition == "gte" and upper_condition == "lte":
            gte_lower = df.loc[mask, col] >= lower_boundry
            lte_upper = df.loc[mask, col] <= upper_boundry
            is_valid = (gte_lower & lte_upper).all()
            if is_valid:
                df.at[timestamp, "event_start_end"] = True

    return df["event_start_end"]


def pair_start_end_indexes(
    data: pd.DataFrame,
    start_col: str,
    end_col: str,
    target_col: str,
    extend_unpaired_start: bool = False,
) -> pd.DataFrame:
    """
    Pairs start and end indexes and updates the target column in the DataFrame.

    :param data: DataFrame containing the data
    :param start_col: Column name for start condition
    :param end_col: Column name for end condition
    :param target_col: Column name to update with the paired range
    :param extend_unpaired_start: Whether to extend unpaired start events to the end of the DataFrame
    """
    mask = data[start_col] | data[end_col]
    df_filtered = data[mask]
    df_filtered[start_col] = keep_only_first_occurrence(df_filtered, start_col)
    df_filtered[end_col] = keep_only_first_occurrence(df_filtered, end_col)
    start_indexes = df_filtered.index[df_filtered[start_col]].tolist()
    end_indexes = df_filtered.index[df_filtered[end_col]].tolist()

    paired_indexes = []
    start_pointer, end_pointer = 0, 0

    while start_pointer < len(start_indexes) and end_pointer < len(end_indexes):
        if start_indexes[start_pointer] < end_indexes[end_pointer]:
            paired_indexes.append(
                (start_indexes[start_pointer], end_indexes[end_pointer])
            )
            start_pointer += 1
            end_pointer += 1
        else:
            end_pointer += 1

    if start_pointer < len(start_indexes) and extend_unpaired_start:
        for i in range(start_pointer, len(start_indexes)):
            paired_indexes.append((start_indexes[i], data.index[-1]))

    if paired_indexes:
        start_indexes, end_indexes = zip(*paired_indexes)
        for start_idx, end_idx in zip(start_indexes, end_indexes):
            data.loc[start_idx:end_idx, target_col] = True

    return data

def keep_only_first_occurrence(df: pd.DataFrame, col: str) -> pd.Series:
    """
    Keeps only the first occurrence of True values in the specified column.
    :param df: DataFrame containing the data
    :param col: Column name to process
    :return: DataFrame with only the first occurrence of True values kept
    """
    column = (df[col] & (~df[col].shift(fill_value=False)))
    return column

def detect_not_runnings_type_two(
    data: pd.DataFrame, 
    get_triggers: Callable,
    equal_product: bool,
    start_key: str,
    end_key: str,
) -> pd.DataFrame:
    """
    identifies the events of type II Not Running

    :param data: time series data with the status of the line
    :param get_triggers: function to get the start and end triggers
    :param equal_product: whether the product should be the same for start and end
    :param start_key: column name for start events
    :param end_key: column name for end events
    :return: data with tags which indicate the start and the end time of each type II event
    :rtype: pd.DataFrame
    """

    data = get_triggers(data, start_key, end_key)

    # filter only true booleans to get the correct events
    data = data[data[start_key] | data[end_key]]

    # correct start and end flags
    data[start_key] = data[start_key] & (data[start_key].shift(1) != True)

    data[end_key] = data[end_key] & (data[end_key].shift(1) != True)

    data = data[data[start_key] | data[end_key]]        
    if data[start_key].iloc[-1]:
        data = data.iloc[:-1]

    if equal_product:
        data[start_key] = data[start_key] & (
            data[const.PRODUCT_DESCRIPTION].fillna(0) == data[const.PRODUCT_DESCRIPTION].shift(-1).fillna(0)
        )
    else:
        data[start_key] = data[start_key] & (
            data[const.PRODUCT_DESCRIPTION].fillna(0) != data[const.PRODUCT_DESCRIPTION].shift(-1).fillna(0)
        )

    return data
