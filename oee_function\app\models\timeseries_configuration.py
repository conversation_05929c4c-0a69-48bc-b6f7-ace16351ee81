from typing import Any, Optional

from pydantic import Field

from .node import Node
from .reporting_line import ReportingLine
from .reporting_site import ReportingSite
from .reporting_unit import ReportingUnit
from .unit_of_measurement import UnitOfMeasurement


class TimeseriesConfiguration(Node):
    reporting_site: ReportingSite = Field(alias="reportingSite")
    reporting_unit: ReportingUnit = Field(alias="reportingUnit")
    reporting_line: ReportingLine = Field(alias="reportingLine")
    name: str = Field(alias="name")
    timeseries_external_id: str = Field(alias="timeseriesExternalId")
    metadata: Optional[dict[str, str]] = Field(default=None, alias="metadata")
    unit: str = Field(alias="unit")
    asset_external_id: Optional[str] = Field(
        default=None, alias="assetExternalId"
    )
    description: str = Field(alias="description")
    security_categories: Optional[list[int]] = Field(
        default=None, alias="securityCategories"
    )
    is_step: bool = Field(alias="isStep")
    is_string: bool = Field(alias="isString")
    dataset_external_id: str = Field(alias="dataSetExternalId")
    uom: UnitOfMeasurement = Field(alias="uom")

    @classmethod
    def from_cdf_response(
        cls, item: dict[str, Any]
    ) -> "TimeseriesConfiguration":
        return cls(
            externalId=item["externalId"],
            space=item["space"],
            reportingSite=ReportingSite(**item["reportingSite"]),
            reportingUnit=ReportingUnit(**item["reportingUnit"]),
            reportingLine=ReportingLine(**item["reportingLine"]),
            name=item["name"],
            timeseriesExternalId=item["timeseriesExternalId"],
            metadata=item["metadata"] if item.get("metadata") else None,
            unit=item["unit"],
            assetExternalId=item["assetExternalId"]
            if item.get("assetExternalId")
            else None,
            description=item["description"],
            securityCategories=item["securityCategories"]
            if item.get("securityCategories")
            else None,
            isStep=item["isStep"],
            isString=item["isString"],
            dataSetExternalId=item["dataSetExternalId"],
            uom=item["uom"],
        )
