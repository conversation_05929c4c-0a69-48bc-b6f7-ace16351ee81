from typing import Any, Optional

import pandas as pd
from app.models.lead_product import LeadProductBbct
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.enums.frac.prod_line_status import ProdLineStatusEnum
from app.utils.bbct_utils import get_bbct_value

class FracBatchEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        bbct_data: Optional[pd.DataFrame] = None,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._bbct_data = bbct_data
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        data['BatchID'] = data['BatchID'].astype(str)

        #fix batch id
        data['BatchID'] = data['BatchID'].shift(3)
        data['Batch'] = data['Batch'].shift(3)

        # event trigger start - ProductionLineStatus = "INACTIVE"
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] == ProdLineStatusEnum.INACTIVE.value)
            )
        )

        # event trigger end - ProductionLineStatus ="ACTIVE"
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus"] == ProdLineStatusEnum.ACTIVE.value)
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        ).assign(
            event1a_end=(
                (data["event1a_end"] == True) & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type Ib

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """

        bbct = self._bbct_data.copy()
        bbct_line = bbct[bbct["reportingLineExternalId"] == self._reporting_line_external_id]

        pi_tag_value_to_product_description = {
            "VE E950IPP": "E950ISX",
            "VE J950PP": "J950SX",
            "VE S950PP": "S950SX",
            "VE T950PP": "T950SX",
            "ZE 7000PP": "ZE7000SX",
            "ZE 7700PP": "ZE7700SX",
        }

        if self._bbct_data is not None:
            self._bbct_data.replace(
                {"productId": pi_tag_value_to_product_description}, inplace=True
            )

        # event trigger start - ProductionLineStatus == "ACTIVE"
        # AND ProductDescription == Product without BBCT
        data = data.assign(
            event1b_start=(
                (data["ProductionLineStatus"] == ProdLineStatusEnum.ACTIVE.value)
                & (~data["ProductDescription"].isin(bbct_line["productId"]))
            )
        )

        # EVENT TRIGGER END
        # (D326-PMK101-101ANSATZ = "Active" AND D326-PMK101-101REZ_PARTI == Product with BBCT)
        # or D326-PMK101-101ANSATZ =  "Inactive" or  time = 5pm or time = 5am or time = 12am

        data = data.assign(
            event1b_end=(
                (
                    (data["ProductionLineStatus"] == ProdLineStatusEnum.ACTIVE.value)
                    & (data["ProductDescription"].isin(bbct_line["productId"]))
                )
                | (data["ProductionLineStatus"] == ProdLineStatusEnum.INACTIVE.value)
            )
        )

        # correct start and end flags
        data = data.assign(
            event1b_start=(
                (data["event1b_start"] == True)
                & (data["event1b_start"].shift(1) != True)
            )
        ).assign(
            event1b_end=(
                (data["event1b_end"] == True) & (data["event1b_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIIb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type III event
        :rtype: pl.DataFrame
        """

        # correct start flag
        data = data.assign(
            event3b_start=(
                (data["ProductionLineStatus"] == ProdLineStatusEnum.ACTIVE.value) &
                ~data["ProductDescription"].str.contains(
                    "Keine|Reinigung|Spülprogramm|Stillstand|Downtime|0",
                    case=False, na=False
                )
            )
        )

        # event trigger end - ProcessOrder changes
        data = data.assign(
            event3b_end=(
                (data["ProductionLineStatus"] == ProdLineStatusEnum.INACTIVE.value)
            )
        )

        # correct end flag
        data = data.assign(
            event3b_end=(
                (data["event3b_end"] == True) & (data["event3b_end"].shift(1) != True)
            )
        )

        return data
    
    def identify_events_typeIIIc(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type III event
        :rtype: pl.DataFrame
        """

        # correct start flag
        data = data.assign(
            event3c_start=(
                (data["ProductionLineStatus"] == ProdLineStatusEnum.ACTIVE.value) &
                ~data["ProductDescription"].str.contains(
                    "Keine|Reinigung|Spülprogramm|Stillstand|Downtime|0", 
                    case=False, na=False
                )
            )
        )

        # event trigger end - ProcessOrder changes
        data = data.assign(
            event3c_end=(
                (data["ProductionLineStatus"] == ProdLineStatusEnum.INACTIVE.value)
            )
        )

        # correct end flag
        data = data.assign(
            event3c_end=(
                (data["event3c_end"] == True) & (data["event3c_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def fix_3b_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame, lead_bbct: LeadProductBbct
    ) -> pd.DataFrame:
        """
        applies the business that demands subtraction of the
        Best Batch Cycle time from every event of type 3b

        :param data: event data
        :type data: pd.DataFrame
        :return: data with the right duration of the events
        :rtype: pd.DataFrame
        """
        pi_tag_value_to_product_description = {
            "VE E950IPP": "E950ISX",
            "VE J950PP": "J950SX",
            "VE S950PP": "S950SX",
            "VE T950PP": "T950SX",
            "ZE 7000PP": "ZE7000SX",
            "ZE 7700PP": "ZE7700SX",
        }

        if bbct_data is not None:
            bbct_data.replace(
                {"productId": pi_tag_value_to_product_description}, inplace=True
            )

        # apply extraction of the BBCT value
        data["bbct"] = pd.DataFrame(
            data.apply(
                lambda row: get_bbct_value(
                    row,
                    bbct_data=bbct_data,
                    reporting_line_external_id=self._reporting_line_external_id,
                    reporting_site_configuration=self._reporting_site_configuration,
                ),
                axis=1
            ).tolist(),
            index=data.index
        )

        # Fix events 3b duration (only where event_definition == "3b")
        data.loc[data["event_definition"] == "3b", "total_duration_seconds"] -= data["bbct"]
        data.loc[(data["event_definition"] == "3b") & (data["bbct"] == 0), "total_duration_seconds"] = 0  # Explicitly set to 0

        if not lead_bbct:
            return data

        # Fix events 3c duration (apply only if leadBBCT < bbct, otherwise set to 0)
        mask_3c = (data["event_definition"] == "3c") & (data["bbct"] != (lead_bbct.best_batch_cycle_time_hr * 3600))
        data.loc[mask_3c, "total_duration_seconds"] = data["bbct"] - (lead_bbct.best_batch_cycle_time_hr * 3600)
        data.loc[~mask_3c & (data["event_definition"] == "3c"), "total_duration_seconds"] = 0  # Explicitly set to 0

        # Set total_duration_seconds to 0 when event is 3c and condition is not met
        data.loc[data["event_definition"] == "3c", "total_duration_seconds"] = data["total_duration_seconds"].fillna(0)

        data = data.query("total_duration_seconds != 0")

        # fill NaN values with 0 when data does not have BBCT
        data["total_duration_seconds"].fillna(0, inplace=True)

        # drop created column
        data.drop(columns=["bbct"], inplace=True)

        return data
