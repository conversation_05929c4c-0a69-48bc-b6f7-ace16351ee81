from typing import Any, Optional

import pandas as pd

from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.utils.constants import Constants as const
from app.utils.event_detection_utils import pair_start_end_indexes

class NanAnhydrideAceticEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        
        self._not_running = pd.Series(dtype=bool)
        
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus1 < 600 & ProductionLineStatus2 < 600
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus1"] < 600)
                & (data["ProductionLineStatus2"] < 600)
                & ((data["ProductionLineStatus1"].shift(1) >= 600)
                | (data["ProductionLineStatus2"].shift(1) >= 600))
            )
        )

        # event end - ProductionLineStatus1 >= 600 & ProductionLineStatus2 >= 600 (both coming from a state < 600)
        data = data.assign(
            event1a_end=(
              ((data["ProductionLineStatus1"] > 600)
               & (data["ProductionLineStatus2"] > 600))
               & ((data["ProductionLineStatus1"].shift(1) <= 600)
               | (data["ProductionLineStatus2"].shift(1) <= 600))
              )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus1 > 600 & ProductionLineStatus2 > 600
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus1"] > 600)
                & (data["ProductionLineStatus2"] > 600)
                & (data["ProductionLineStatus1"] <= 780)
                & (data["ProductionLineStatus2"] <= 780)
                & ((data["ProductionLineStatus1"].shift(1) <= 600)
                | (data["ProductionLineStatus2"].shift(1) <= 600))
            )
        )

        # event end - ProductionLineStatus1 > 780 & ProductionLineStatus2 > 780 (both coming from a state <= 780)
        data = data.assign(
            event2a_end=(
              ((data["ProductionLineStatus1"] > 780)
               & (data["ProductionLineStatus2"] > 780))
               & ((data["ProductionLineStatus1"].shift(1) <= 780)
               | (data["ProductionLineStatus2"].shift(1) <= 780))
              )
              | (
                  (data["ProductionLineStatus1"] < 600)
                  & (data["ProductionLineStatus2"] < 600)
              )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args):
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "last", [const.MSDP, const.SCHEDULED_RATE], -1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]

        return day_data
    
    def identify_events_typeIIIb(self, data: pd.DataFrame, **args):
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "last", [const.MSDP, const.SCHEDULED_RATE], -1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data
    
    def identify_events_typeIIIc(self, data: pd.DataFrame, **args):
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "last", [const.MSDP, const.SCHEDULED_RATE], -1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIVa(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIVb(self):
        pass
    
    def not_running_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._not_running.empty:
            return self._not_running
        
        df["not_running_start"] = (
                (df["ProductionLineStatus1"] < 600)
                & (df["ProductionLineStatus2"] < 600)
        )
        
        df["not_running_end"] = (
                (df["ProductionLineStatus1"] >= 600)
                & (df["ProductionLineStatus2"] >= 600)
        )
        
        df["not_running_start"] = df["not_running_start"] & (~df["not_running_start"].shift(1).fillna(False))
        df["not_running_end"] = df["not_running_end"] & (~df["not_running_end"].shift(1).fillna(False))
        
        df["not_running"] = False
        pair_start_end_indexes(df, "not_running_start", "not_running_end", "not_running", True)
        
        df["starting_up_start"] = df["not_running_end"]
        df["starting_up_end"] = (
            (
                (df["ProductionLineStatus1"] >= 780)
                & (df["ProductionLineStatus2"] >= 780)
            ) | (
                df["not_running_start"]
            )
        )
        
        df["starting_up_end"] = df["starting_up_end"] & (~df["starting_up_end"].shift(1).fillna(False))
        
        mask = df["not_running_start"] | df["starting_up_start"]
        df_filtered = df[mask]
        df_filtered["starting_up_start"] = (
            df_filtered["starting_up_start"] & df_filtered["not_running_start"].shift(1).fillna(False)
        )
        
        df.loc[mask, "starting_up_start"] = df_filtered["starting_up_start"]
        
        df["starting_up"] = False
        pair_start_end_indexes(df, "starting_up_start", "starting_up_end", "starting_up", True)
        
        not_running = df["not_running"] | df["starting_up"]
        
        self._not_running = not_running

        return not_running