from typing import List, Optional

from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.data_models import DataModelId

from ..models.reporting_site import ReportingSite
from ..models.reporting_site_configuration import ReportingSiteConfiguration
from .view_repository import ViewRepository


class ReportingSiteRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        view_repository: ViewRepository,
        data_model_id: DataModelId,
    ) -> None:
        self._cognite_client = cognite_client
        self._view_repository = view_repository
        self._data_model_id = data_model_id

    def get_reporting_site(
        self, reporting_site_external_id: str
    ) -> Optional[ReportingSite]:
        query_result = self._cognite_client.data_modeling.graphql.query(
            self._data_model_id,
            self._build_reporting_site_query(),
            {"reportingSite": reporting_site_external_id},
        )["listReportingSite"]["items"]

        if not query_result:
            return None

        entry = query_result[0]
        return ReportingSite.from_cognite_response(entry)

    def get_configurations(
        self, reporting_site_external_id: str,
        only_lines: List[str],
    ) -> Optional[ReportingSiteConfiguration]:
        query_result = self._cognite_client.data_modeling.graphql.query(
            self._data_model_id,
            self._build_reporting_site_configuration_query(),
            {"reportingSite": reporting_site_external_id, "reportingLines": only_lines},
        )["listOEEReportingSiteConfiguration"]["items"]

        if not query_result:
            return None

        entry = query_result[0]
        return ReportingSiteConfiguration.from_cognite_response(entry)

    def _build_reporting_site_query(self):
        return """
query QuertReportingSite($reportingSite: ID) {
  listReportingSite(first: 1, filter: {externalId: {eq: $reportingSite}}) {
    items {
      externalId
      space
      name
      siteCode
      timeZone {
          externalId
          space
          name
      }
      reportingLines {
        items {
          externalId
          space
          name
        }
      }
      reportingUnits {
        items {
          externalId
          space
          name
        }
      }
    }
  }
}
"""

    def _build_reporting_site_configuration_query(self):
        return """
query listOEEReportingSiteConfiguration($reportingSite: ID, $reportingLines: [ID!]) {
  listOEEReportingSiteConfiguration(
    first: 1
    filter: {reportingSite: {externalId: {eq: $reportingSite}}}
  ) {
    items {
      externalId
      space
      reportingSite {
        externalId
        space
        name
        siteCode
        timeZone {
          externalId
          space
          name
        }
        country {
          externalId
          space
          name
          parent {
            externalId
            space
            name
          }
        }
      }
      shifts
      regexProcessing
      extraEventProcessing
      extraTimeProcessing
      fixEventNoVal
      lineType
      fixNullValuesSubset
      inputTags(first: 1000, filter: {reportingLine: {externalId: {in: $reportingLines} }}) {
        items {
          externalId
          space
          reportingLine {
            externalId
            space
            name
            reportingUnit {
              externalId
              space
              name
            }
            processType {
              externalId
              space
              name
            }
          }
          timeSeries {
            externalId
            name
          }
          alias
          eventIdentification
          tagValueMapping
        }
      }
      eventsHierarchy(first: 1000, filter: {reportingLine: {externalId: {in: $reportingLines} }}) {
        items {
          externalId
          space
          reportingLine {
            externalId
            space
            name
          }
          eventHierarchy
          variableCategories
          eventDefinition
          subCategoryLevel1
          subCategoryLevel2
          eventCode
          metricCode
          businessRule
          workShiftRule
          usesBbct
          minorStop
          newMinorStop
          newNotRunning
          notRunningRule
          eventHierarchyShifts
        }
      }
    }
  }
}
"""

    def create_configuration(
        self, configuration: ReportingSiteConfiguration
    ) -> None:
        reporting_site_configuration_view = self._view_repository.get_view_id(
            "OEEReportingSiteConfiguration"
        )
        input_tag_configuration_view = self._view_repository.get_view_id(
            "OEEInputTagConfiguration"
        )
        event_hierarchy_configuration_view = self._view_repository.get_view_id(
            "OEEEventHierarchyConfiguration"
        )
        [nodes, edges] = configuration.convert_to_nodes_and_edges(
            reporting_site_configuration_view,
            input_tag_configuration_view,
            event_hierarchy_configuration_view,
        )

        for node in nodes:
            self._cognite_client.data_modeling.instances.apply(
                nodes=[node],
            )

        self._cognite_client.data_modeling.instances.apply(
            edges=edges,
            auto_create_direct_relations=True,
            auto_create_end_nodes=True,
            auto_create_start_nodes=True,
        )
