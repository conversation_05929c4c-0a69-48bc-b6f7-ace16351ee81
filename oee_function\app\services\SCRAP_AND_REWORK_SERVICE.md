# Índice / Index

- [Seção em Português](#seção-em-português)
- [English Section](#english-section)

# Seção em Português

## Serviço de Correção de Duração para Scrap e Rework

Este documento descreve o funcionamento do serviço `ScrapAndReworkService`, responsável por identificar e corrigir a duração de eventos relacionados a scrap (refugo) e rework (retrabalho) em diferentes tipos de processos de produção.

### Visão Geral

O serviço processa eventos de scrap e rework aplicando fórmulas específicas de correção de duração baseadas no tipo de processo:
- **Continuous**: Não implementado (serviço retorna sem modificações)
- **Compounding**: Utiliza dados horários (hourly_rate e mdr_hourly)

### Aplicação do Serviço

O serviço é aplicado **apenas** para unidades de produção (reporting units) que estão configuradas em `EventFrameConstants.UNITS_WITH_SCRAP_OR_REWORK`.

**Unidades configuradas:**
- `UNT-BISCMP`
- `UNT-NANCEL`

A verificação é realizada no `EventFrameService` antes de instanciar o `ScrapAndReworkService`:

```445:452:OEE_EventFrame/oee_function/app/services/event_frame_service.py
reporting_unit = event_frames_df["refUnitId"].iloc[0]
if reporting_unit in EventFrameConstants.UNITS_WITH_SCRAP_OR_REWORK:
      scrap_and_rework_service = ScrapAndReworkService(
         event_frames_df=event_frames_df,
         pc_type=pc_type,
         hourly_data=hourly_data,
      )
      event_frames_df = scrap_and_rework_service.fix_duration()
```

Se a unidade não estiver na lista de unidades configuradas, o serviço não é executado e os eventos não são processados.

### Identificação de Eventos Scrap/Rework

O serviço identifica eventos de scrap ou rework verificando as seguintes colunas (case-insensitive):
- `event_code`
- `subcat_level1`
- `metric_code`

Um evento é considerado scrap ou rework se qualquer uma dessas colunas contiver as palavras "scrap" ou "rework".

### Processamento para Linhas Continuous

**Status: Não Implementado**

O processamento para linhas do tipo `Continuous` ou `Continous 2 Products` não está implementado atualmente. Quando o serviço identifica que o tipo de processo é continuous, ele:

1. Registra um log informativo: `"Not available Service for Scrap and Rework for continuous lines"`
2. Retorna o DataFrame original sem modificações

O método `_fix_duration_continuous()` apenas retorna o DataFrame sem processamento.

### Processamento para Linhas Compounding

#### Preparação de Dados Horários (`_prepare_hourly_rate_for_compounding_lines`)

1. **Normalização de Colunas**:
   - Normaliza nomes de colunas comuns do `hourly_data`:
     - `startDateTime` → `start_time`
     - `hourlyRatePerH` → `hourly_rate`
     - `refUnitOfMeasurement` → `measurement_unit`
     - `runningTimeSeconds` → `running_duration` → `running_duration_hourly`
     - `MDR` → `mdr_hourly`

2. **Mesclagem com Dados Horários**:
   - Cria colunas `hourly_rate`, `measurement_unit`, `running_duration_hourly` e `mdr_hourly`
   - Mescla os valores horários apenas nas linhas onde `scrap_or_rework == True`
   - A mesclagem é feita baseada na hora do evento (normalizada ao início da hora)
   - Exemplo: Um evento com `start_time = "2025-07-12 06:26:39"` receberá os valores da hora `"2025-07-12 06:00:00"`

3. **Normalização de Timezone**:
   - Todos os timestamps são convertidos para UTC antes do processamento

#### Cálculo de Duração para Compounding

**Fórmula**:
```
new_duration = total_duration_seconds * hourly_rate / mdr_hourly
```

**Validações**:
- `mdr_hourly` não pode ser nulo ou zero
- `hourly_rate` não pode ser nulo
- `total_duration_seconds` não pode ser nulo

### Fluxo de Processamento

1. **Verificação de Unidade**: No `EventFrameService`, verifica se `reporting_unit` está em `EventFrameConstants.UNITS_WITH_SCRAP_OR_REWORK`
2. **Instanciação**: Se a unidade estiver configurada, instancia o `ScrapAndReworkService` com:
   - `event_frames_df`: DataFrame com os eventos
   - `pc_type`: Tipo de processo (Batch, Continuous, Compounding, etc.)
   - `hourly_data`: DataFrame opcional com dados horários (necessário para Compounding)
3. **Identificação**: O método `fix_duration()` identifica eventos de scrap/rework através do método `_has_scrap_or_rework_events()`
4. **Roteamento por Tipo**: Baseado no `pc_type`, direciona para o processamento adequado:
   - `Continuous` ou `Continous 2 Products` → `_fix_duration_continuous()` (não implementado, retorna sem modificações)
   - `Compounding` → `_fix_duration_compounding()` (implementado)
5. **Preparação de Dados**: Para Compounding, enriquece os eventos com dados horários através de `_prepare_hourly_rate_for_compounding_lines()`
6. **Cálculo de Duração**: Para Compounding, aplica a fórmula para recalcular `total_duration_seconds` através de `_calculate_total_duration_scrap_and_rework()`
7. **Atualização**: Atualiza `total_duration_seconds` e `original_total_duration_seconds` com o novo valor calculado

### Colunas Adicionadas/Modificadas

#### Para Linhas Continuous:
- Nenhuma coluna é adicionada ou modificada (serviço não implementado)

#### Para Linhas Compounding:
- `hourly_rate`: Taxa horária de produção
- `measurement_unit`: Unidade de medida
- `running_duration_hourly`: Duração de execução horária
- `mdr_hourly`: MDR horário
- `total_duration_seconds`: Duração recalculada
- `original_total_duration_seconds`: Duração original (atualizada com novo valor)

### Observações Importantes

- **Aplicação Condicional**: O serviço é aplicado apenas para unidades configuradas em `EventFrameConstants.UNITS_WITH_SCRAP_OR_REWORK`
- **Filtro de Eventos**: O serviço apenas processa eventos onde `scrap_or_rework == True`
- **Identificação**: Eventos são identificados verificando as colunas `event_code`, `subcat_level1` ou `metric_code` (case-insensitive) para as palavras "scrap" ou "rework"
- **Eventos Ignorados**: Eventos que não atendem aos critérios de identificação são ignorados
- **Dados Faltantes**: Se os dados necessários não estiverem disponíveis (ex: `hourly_data` vazio ou colunas faltantes), o serviço retorna o DataFrame original sem modificações
- **Timezone**: Todas as operações de data/hora são normalizadas para UTC para garantir consistência
- **Preservação de Índices**: O serviço preserva os índices originais do DataFrame durante todas as operações
- **Continuous**: O processamento para linhas Continuous não está implementado atualmente

----

# English Section

## Scrap and Rework Duration Correction Service

This document describes the operation of the `ScrapAndReworkService`, responsible for identifying and correcting the duration of events related to scrap and rework in different types of production processes.

### Overview

The service processes scrap and rework events by applying specific duration correction formulas based on the process type:
- **Continuous**: Not implemented (service returns without modifications)
- **Compounding**: Uses hourly data (hourly_rate and mdr_hourly)

### Service Application

The service is applied **only** for production units (reporting units) that are configured in `EventFrameConstants.UNITS_WITH_SCRAP_OR_REWORK`.

**Configured units:**
- `UNT-BISCMP`
- `UNT-NANCEL`

The check is performed in `EventFrameService` before instantiating `ScrapAndReworkService`:

```445:452:OEE_EventFrame/oee_function/app/services/event_frame_service.py
reporting_unit = event_frames_df["refUnitId"].iloc[0]
            if reporting_unit in EventFrameConstants.UNITS_WITH_SCRAP_OR_REWORK:
                scrap_and_rework_service = ScrapAndReworkService(
                    event_frames_df=event_frames_df,
                    pc_type=pc_type,
                    hourly_data=hourly_data,
                )
                event_frames_df = scrap_and_rework_service.fix_duration()
```

If the unit is not in the configured units list, the service is not executed and events are not processed.

### Scrap/Rework Event Identification

The service identifies scrap or rework events by checking the following columns (case-insensitive):
- `event_code`
- `subcat_level1`
- `metric_code`

An event is considered scrap or rework if any of these columns contains the words "scrap" or "rework".

### Processing for Continuous Lines

**Status: Not Implemented**

Processing for `Continuous` or `Continous 2 Products` process types is not currently implemented. When the service identifies that the process type is continuous, it:

1. Logs an informative message: `"Not available Service for Scrap and Rework for continuous lines"`
2. Returns the original DataFrame without modifications

The `_fix_duration_continuous()` method simply returns the DataFrame without processing.

### Processing for Compounding Lines

#### Hourly Data Preparation (`_prepare_hourly_rate_for_compounding_lines`)

1. **Column Normalization**:
   - Normalizes common column names from `hourly_data`:
     - `startDateTime` → `start_time`
     - `hourlyRatePerH` → `hourly_rate`
     - `refUnitOfMeasurement` → `measurement_unit`
     - `runningTimeSeconds` → `running_duration` → `running_duration_hourly`
     - `MDR` → `mdr_hourly`

2. **Merging with Hourly Data**:
   - Creates `hourly_rate`, `measurement_unit`, `running_duration_hourly` and `mdr_hourly` columns
   - Merges hourly values only in rows where `scrap_or_rework == True`
   - The merge is based on the event hour (normalized to the beginning of the hour)
   - Example: An event with `start_time = "2025-07-12 06:26:39"` will receive values from hour `"2025-07-12 06:00:00"`

3. **Timezone Normalization**:
   - All timestamps are converted to UTC before processing

#### Duration Calculation for Compounding

**Formula**:
```
new_duration = total_duration_seconds * hourly_rate / mdr_hourly
```

**Validations**:
- `mdr_hourly` cannot be null or zero
- `hourly_rate` cannot be null
- `total_duration_seconds` cannot be null

### Processing Flow

1. **Unit Verification**: In `EventFrameService`, checks if `reporting_unit` is in `EventFrameConstants.UNITS_WITH_SCRAP_OR_REWORK`
2. **Instantiation**: If the unit is configured, instantiates `ScrapAndReworkService` with:
   - `event_frames_df`: DataFrame with events
   - `pc_type`: Process type (Batch, Continuous, Compounding, etc.)
   - `hourly_data`: Optional DataFrame with hourly data (required for Compounding)
3. **Identification**: The `fix_duration()` method identifies scrap/rework events through `_has_scrap_or_rework_events()`
4. **Type-based Routing**: Based on `pc_type`, routes to appropriate processing:
   - `Continuous` or `Continous 2 Products` → `_fix_duration_continuous()` (not implemented, returns without modifications)
   - `Compounding` → `_fix_duration_compounding()` (implemented)
5. **Data Preparation**: For Compounding, enriches events with hourly data through `_prepare_hourly_rate_for_compounding_lines()`
6. **Duration Calculation**: For Compounding, applies the formula to recalculate `total_duration_seconds` through `_calculate_total_duration_scrap_and_rework()`
7. **Update**: Updates `total_duration_seconds` and `original_total_duration_seconds` with the calculated new value

### Added/Modified Columns

#### For Continuous Lines:
- No columns are added or modified (service not implemented)

#### For Compounding Lines:
- `hourly_rate`: Hourly production rate
- `measurement_unit`: Measurement unit
- `running_duration_hourly`: Hourly running duration
- `mdr_hourly`: Hourly MDR
- `total_duration_seconds`: Recalculated duration
- `original_total_duration_seconds`: Original duration (updated with new value)

### Important Notes

- **Conditional Application**: The service is applied only for units configured in `EventFrameConstants.UNITS_WITH_SCRAP_OR_REWORK`
- **Event Filtering**: The service only processes events where `scrap_or_rework == True`
- **Identification**: Events are identified by checking the `event_code`, `subcat_level1` or `metric_code` columns (case-insensitive) for the words "scrap" or "rework"
- **Ignored Events**: Events that do not meet identification criteria are ignored
- **Missing Data**: If necessary data is not available (e.g., empty `hourly_data` or missing columns), the service returns the original DataFrame without modifications
- **Timezone**: All date/time operations are normalized to UTC to ensure consistency
- **Index Preservation**: The service preserves the original DataFrame indices during all operations
- **Continuous**: Processing for Continuous lines is not currently implemented

