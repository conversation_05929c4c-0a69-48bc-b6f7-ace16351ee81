# Índice / Index

- [Seção em Português](#seção-em-português)
- [English Section](#english-section)

# Seção em Português

## Hierarchical Events Service - Documentação

### Visão Geral

O `HierarchicalEventsService` é responsável por ajustar eventos hierárquicos, garantindo que eventos de menor prioridade não sobreponham eventos de maior prioridade. Este serviço implementa uma lógica de corte inteligente que preserva a integridade temporal dos eventos de maior prioridade.

### Conceitos Fundamentais

#### Hierarquia de Prioridades

A hierarquia é definida pela ordem dos tipos de eventos na lista de prioridades:
- **Primeiro tipo** = Maior prioridade (mantido intacto)
- **Tipos subsequentes** = Menor prioridade (ajustados em relação aos anteriores)

**Padrão:** `["1", "2", "4"]`
- Tipo "1" tem maior prioridade
- Tipo "2" é ajustado em relação ao tipo "1"
- Tipo "4" é ajustado em relação aos tipos "1" e "2"

#### Tipos de Eventos

Os tipos de eventos são extraídos do `event_definition`:
- `"1a"`, `"1b"` → Tipo `"1"`
- `"2a"`, `"2b"` → Tipo `"2"`
- `"4a"`, `"4b"` → Tipo `"4"`

### Estrutura de Dados

#### DataFrame de Entrada

O DataFrame deve conter as seguintes colunas obrigatórias:

- `event_definition`: Tipo do evento (ex: "1a", "2a", "4a")
- `start_time`: Início do evento (datetime)
- `end_time`: Fim do evento (datetime)
- `total_duration_seconds`: (opcional) Duração total em segundos
- `rlt`: (opcional) Relação do evento
- Outras colunas específicas do evento

#### DataFrame de Saída

O DataFrame retornado contém os mesmos eventos, mas com ajustes temporais aplicados:
- Eventos de maior prioridade permanecem inalterados
- Eventos de menor prioridade são cortados ou removidos conforme necessário
- Eventos podem ser divididos em múltiplos eventos quando envolvem eventos de maior prioridade

### Lógica de Ajuste

O serviço implementa quatro cenários principais de ajuste:

#### 1. Evento Menor Completamente Dentro do Maior

**Situação:** O evento de menor prioridade está completamente contido dentro do evento de maior prioridade.

**Ação:** O evento de menor prioridade é **removido completamente**.

```
Antes:
┌─────────────────────────────────┐
│ Evento 1a (maior prioridade)   │
│  ┌──────────────┐              │
│  │ Evento 2a    │              │
│  └──────────────┘              │
└─────────────────────────────────┘

Depois:
┌─────────────────────────────────┐
│ Evento 1a (mantido)            │
└─────────────────────────────────┘
(Evento 2a removido)
```

#### 2. Evento Menor Começa Antes e Termina Durante

**Situação:** O evento de menor prioridade começa antes do evento de maior prioridade, mas termina durante ele.

**Ação:** O evento de menor prioridade é **cortado no início** do evento de maior prioridade.

```
Antes:
┌─────────────────────────────────┐
│ Evento 2a (menor prioridade)    │
│      ┌──────────────────────────┐
│      │ Evento 1a (maior)        │
│      └──────────────────────────┘
└─────────────────────────────────┘

Depois:
┌──────┐
│ 2a   │ (mantido até início do 1a)
└──────┘
      ┌──────────────────────────┐
      │ Evento 1a (mantido)      │
      └──────────────────────────┘
```

#### 3. Evento Menor Começa Durante e Termina Depois

**Situação:** O evento de menor prioridade começa durante o evento de maior prioridade e termina depois.

**Ação:** O evento de menor prioridade é **cortado no fim** do evento de maior prioridade.

```
Antes:
      ┌──────────────────────────┐
      │ Evento 1a (maior)        │
      └──────────────────────────┘
            ┌─────────────────────────────────┐
            │ Evento 2a (menor prioridade)   │
            └─────────────────────────────────┘

Depois:
      ┌──────────────────────────┐
      │ Evento 1a (mantido)      │
      └──────────────────────────┘
            ┌─────────────────────────────────┐
            │ Evento 2a (mantido após fim 1a)│
            └─────────────────────────────────┘
```

#### 4. Evento Menor Envolve Completamente o Maior

**Situação:** O evento de menor prioridade envolve completamente o evento de maior prioridade (começa antes e termina depois).

**Ação:** O evento de menor prioridade é **dividido em 2 eventos** separados.

```
Antes:
┌─────────────────────────────────────────────┐
│ Evento 2a (menor prioridade)                │
│      ┌──────────────────────────┐          │
│      │ Evento 1a (maior)        │          │
│      └──────────────────────────┘          │
└─────────────────────────────────────────────┘

Depois:
┌──────┐
│ 2a   │ (primeira parte)
└──────┘
      ┌──────────────────────────┐
      │ Evento 1a (mantido)      │
      └──────────────────────────┘
            ┌─────────────────────────────────┐
            │ Evento 2a (segunda parte)       │
            └─────────────────────────────────┘
```

### Exemplos de Uso

#### Exemplo 1: Corte Inicial (Initial Cut)

**Cenário:** Evento "1a" começa antes do evento "2a" e termina durante ele.

```python
import pandas as pd
from datetime import datetime, timedelta
from app.utils.hierarchical_events import HierarchicalEventsService

# Dados de entrada
data = pd.DataFrame({
    'event_definition': ['1a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 8, 0),   # 1a: 08:00
        datetime(2024, 1, 1, 9, 0)    # 2a: 09:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 10, 0),  # 1a: 10:00
        datetime(2024, 1, 1, 12, 0)   # 2a: 12:00
    ]
})

# Processamento
service = HierarchicalEventsService()
result = service.fix_hierarchical_events(data)

# Resultado esperado:
# - Evento 1a: mantido intacto (08:00 - 10:00)
# - Evento 2a: cortado para começar após o fim do 1a (10:00 - 12:00)
```

**Visualização:**
```
Antes:
08:00 ────────── 10:00 ────────── 12:00
┌──────────────┐
│ Evento 1a    │
└──────────────┘
        ┌──────────────────────┐
        │ Evento 2a            │
        └──────────────────────┘

Depois:
08:00 ────────── 10:00 ────────── 12:00
┌──────────────┐
│ Evento 1a    │ (mantido)
└──────────────┘
                ┌────────────────┐
                │ Evento 2a      │ (cortado)
                └────────────────┘
```

#### Exemplo 2: Corte no Meio (Middle Cut)

**Cenário:** Evento "1a" começa no meio do evento "2a" e termina depois.

```python
# Dados de entrada
data = pd.DataFrame({
    'event_definition': ['1a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 10, 0),  # 1a: 10:00 (meio do 2a)
        datetime(2024, 1, 1, 9, 0)    # 2a: 09:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 11, 0),  # 1a: 11:00
        datetime(2024, 1, 1, 12, 0)   # 2a: 12:00
    ]
})

# Resultado esperado:
# - Evento 1a: mantido intacto (10:00 - 11:00)
# - Evento 2a: dividido em 2 partes (09:00 - 10:00) e (11:00 - 12:00)
```

**Visualização:**
```
Antes:
09:00 ────────── 10:00 ────────── 11:00 ────────── 12:00
┌──────────────────────────────────────┐
│ Evento 2a                            │
└──────────────────────────────────────┘
        ┌──────────────┐
        │ Evento 1a    │
        └──────────────┘

Depois:
09:00 ────────── 10:00 ────────── 11:00 ────────── 12:00
┌──────────────┐
│ Evento 2a    │ (primeira parte)
└──────────────┘
                ┌──────────────┐
                │ Evento 1a    │ (mantido)
                └──────────────┘
                                ┌──────────────┐
                                │ Evento 2a    │ (segunda parte)
                                └──────────────┘
```

#### Exemplo 3: Envolvimento Completo (Wrap)

**Cenário:** Evento "1a" envolve completamente o evento "2a" (começa antes e termina depois).

```python
# Dados de entrada
data = pd.DataFrame({
    'event_definition': ['1a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 8, 0),   # 1a: 08:00 (antes do 2a)
        datetime(2024, 1, 1, 9, 0)    # 2a: 09:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 13, 0),  # 1a: 13:00 (depois do 2a)
        datetime(2024, 1, 1, 12, 0)   # 2a: 12:00
    ]
})

# Resultado esperado:
# - Evento 1a: mantido intacto (08:00 - 13:00)
# - Evento 2a: dividido em 2 partes (08:00 - 09:00) e (12:00 - 13:00)
#   NOTA: Na verdade, o 2a será completamente removido pois está dentro do 1a
```

**Visualização:**
```
Antes:
08:00 ────────── 09:00 ────────── 12:00 ────────── 13:00
┌──────────────────────────────────────────────────┐
│ Evento 1a                                        │
└──────────────────────────────────────────────────┘
        ┌──────────────────────┐
        │ Evento 2a            │
        └──────────────────────┘

Depois:
08:00 ────────── 09:00 ────────── 12:00 ────────── 13:00
┌──────────────────────────────────────────────────┐
│ Evento 1a (mantido)                              │
└──────────────────────────────────────────────────┘
(Evento 2a removido - completamente dentro do 1a)
```

#### Exemplo 4: Evento 4a Envolvendo 2a

**Cenário:** Evento "4a" envolve completamente o evento "2a".

```python
# Dados de entrada
data = pd.DataFrame({
    'event_definition': ['4a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 8, 0),   # 4a: 08:00
        datetime(2024, 1, 1, 9, 0)    # 2a: 09:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 13, 0),  # 4a: 13:00
        datetime(2024, 1, 1, 12, 0)   # 2a: 12:00
    ]
})

# Resultado esperado:
# - Evento 2a: mantido intacto (09:00 - 12:00) - maior prioridade que 4a
# - Evento 4a: dividido em 2 partes (08:00 - 09:00) e (12:00 - 13:00)
```

**Visualização:**
```
Antes:
08:00 ────────── 09:00 ────────── 12:00 ────────── 13:00
┌──────────────────────────────────────────────────┐
│ Evento 4a (menor prioridade)                    │
└──────────────────────────────────────────────────┘
        ┌──────────────────────┐
        │ Evento 2a (maior)     │
        └──────────────────────┘

Depois:
08:00 ────────── 09:00 ────────── 12:00 ────────── 13:00
┌──────────────┐
│ Evento 4a    │ (primeira parte)
└──────────────┘
        ┌──────────────────────┐
        │ Evento 2a (mantido)  │
        └──────────────────────┘
                                ┌──────────────┐
                                │ Evento 4a    │ (segunda parte)
                                └──────────────┘
```

#### Exemplo 5: Múltiplos Eventos de Maior Prioridade

**Cenário:** Evento "2a" sobrepõe múltiplos eventos "1a".

```python
# Dados de entrada
data = pd.DataFrame({
    'event_definition': ['1a', '1a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 9, 0),   # 1a: 09:00
        datetime(2024, 1, 1, 11, 0),  # 1a: 11:00
        datetime(2024, 1, 1, 8, 0)    # 2a: 08:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 10, 0),  # 1a: 10:00
        datetime(2024, 1, 1, 12, 0),  # 1a: 12:00
        datetime(2024, 1, 1, 13, 0)   # 2a: 13:00
    ]
})

# Resultado esperado:
# - Eventos 1a: mantidos intactos (09:00-10:00) e (11:00-12:00)
# - Evento 2a: dividido em 3 partes:
#   * (08:00 - 09:00) - antes do primeiro 1a
#   * (10:00 - 11:00) - entre os dois 1a
#   * (12:00 - 13:00) - após o segundo 1a
```

**Visualização:**
```
Antes:
08:00 ─── 09:00 ─── 10:00 ─── 11:00 ─── 12:00 ─── 13:00
┌──────────────────────────────────────────────────┐
│ Evento 2a                                        │
└──────────────────────────────────────────────────┘
        ┌──────┐           ┌──────┐
        │ 1a   │           │ 1a   │
        └──────┘           └──────┘

Depois:
08:00 ─── 09:00 ─── 10:00 ─── 11:00 ─── 12:00 ─── 13:00
┌──────┐
│ 2a   │ (primeira parte)
└──────┘
        ┌──────┐
        │ 1a   │ (mantido)
        └──────┘
                ┌──────┐
                │ 2a   │ (segunda parte)
                └──────┘
                        ┌──────┐
                        │ 1a   │ (mantido)
                        └──────┘
                                ┌──────┐
                                │ 2a   │ (terceira parte)
                                └──────┘
```

### Classe Reference

#### Classe `HierarchicalEventsService`

##### `__init__(event_types: List[str] = None)`

Inicializa o serviço de eventos hierárquicos.

**Parâmetros:**
- `event_types`: Lista de tipos de eventos em ordem de prioridade (primeiro = maior prioridade). Padrão: `["1", "2", "4"]`

**Exemplo:**
```python
# Usando hierarquia padrão
service = HierarchicalEventsService()

# Usando hierarquia customizada
service = HierarchicalEventsService(event_types=["1", "3", "5"])
```

##### `fix_hierarchical_events(data: pd.DataFrame) -> pd.DataFrame`

Ajusta eventos hierárquicos removendo ou cortando sobreposições entre eventos de diferentes prioridades.

**Parâmetros:**
- `data`: DataFrame contendo eventos com colunas:
  - `event_definition`: tipo do evento
  - `start_time`: início do evento
  - `end_time`: fim do evento
  - `total_duration_seconds`: (opcional) duração total em segundos
  - `rlt`: (opcional) relação do evento
  - outras colunas específicas do evento

**Retorna:**
- DataFrame com eventos ajustados, sem sobreposições indevidas entre diferentes níveis de prioridade

**Exemplo:**
```python
import pandas as pd
from app.utils.hierarchical_events import HierarchicalEventsService

# Preparar dados
data = pd.DataFrame({
    'event_definition': ['1a', '2a'],
    'start_time': [datetime(2024, 1, 1, 8, 0), datetime(2024, 1, 1, 9, 0)],
    'end_time': [datetime(2024, 1, 1, 10, 0), datetime(2024, 1, 1, 12, 0)]
})

# Processar
service = HierarchicalEventsService()
result = service.fix_hierarchical_events(data)
```

### Métodos Internos

#### `_extract_event_type(event_definition: str) -> str`

Extrai o tipo de evento do `event_definition`.

**Exemplo:**
- `"1a"` → `"1"`
- `"2b"` → `"2"`
- `"4a"` → `"4"`

#### `_events_overlap(start1, end1, start2, end2) -> bool`

Verifica se dois intervalos de tempo se sobrepõem.

#### `_adjust_event_against_higher_priority(lower_event, higher_priority_events) -> List[pd.Series]`

Ajusta um evento de menor prioridade em relação a eventos de maior prioridade.

**Retorna:**
- Lista de Séries pandas com os eventos ajustados (pode ser vazia, 1 ou 2 eventos)

#### `_adjust_event_times_by_hierarchy(data: pd.DataFrame) -> pd.DataFrame`

Ajusta os tempos dos eventos baseado na hierarquia definida.

### Regras de Negócio

1. **Preservação de Prioridade:** Eventos de maior prioridade nunca são modificados.

2. **Remoção de Intervalos Pequenos:** Intervalos menores que 1 segundo são ignorados para evitar eventos insignificantes.

3. **Merge de Intervalos Bloqueados:** Múltiplos eventos de maior prioridade que se sobrepõem são mesclados em intervalos bloqueados únicos.

4. **Preservação de Dados:** Todas as colunas originais do evento são preservadas nos eventos ajustados, exceto `start_time`, `end_time` e `total_duration_seconds` que são recalculados.

5. **Eventos Não Hierárquicos:** Eventos cujo tipo não está na lista de prioridades são mantidos intactos e adicionados de volta ao resultado final.

### Integração no Pipeline

O serviço é utilizado no `EventFrameService` após o preenchimento dos dados de detalhe OEE:

```python
# Em event_frame_service.py
hierarchical_service = HierarchicalEventsService()
event_frames_df = hierarchical_service.fix_hierarchical_events(event_frames_df)
```

---

# English Section

## Hierarchical Events Service - Documentation

### Overview

The `HierarchicalEventsService` is responsible for adjusting hierarchical events, ensuring that lower priority events do not overlap higher priority events. This service implements an intelligent cutting logic that preserves the temporal integrity of higher priority events.

### Fundamental Concepts

#### Priority Hierarchy

The hierarchy is defined by the order of event types in the priority list:
- **First type** = Highest priority (kept intact)
- **Subsequent types** = Lower priority (adjusted relative to previous ones)

**Default:** `["1", "2", "4"]`
- Type "1" has highest priority
- Type "2" is adjusted relative to type "1"
- Type "4" is adjusted relative to types "1" and "2"

#### Event Types

Event types are extracted from `event_definition`:
- `"1a"`, `"1b"` → Type `"1"`
- `"2a"`, `"2b"` → Type `"2"`
- `"4a"`, `"4b"` → Type `"4"`

### Data Structure

#### Input DataFrame

The DataFrame must contain the following required columns:

- `event_definition`: Event type (e.g., "1a", "2a", "4a")
- `start_time`: Event start (datetime)
- `end_time`: Event end (datetime)
- `total_duration_seconds`: (optional) Total duration in seconds
- `rlt`: (optional) Event relation
- Other event-specific columns

#### Output DataFrame

The returned DataFrame contains the same events, but with temporal adjustments applied:
- Higher priority events remain unchanged
- Lower priority events are cut or removed as needed
- Events may be split into multiple events when they wrap higher priority events

### Adjustment Logic

The service implements four main adjustment scenarios:

#### 1. Lower Event Completely Inside Higher Event

**Situation:** The lower priority event is completely contained within the higher priority event.

**Action:** The lower priority event is **completely removed**.

```
Before:
┌─────────────────────────────────┐
│ Event 1a (higher priority)     │
│  ┌──────────────┐              │
│  │ Event 2a     │              │
│  └──────────────┘              │
└─────────────────────────────────┘

After:
┌─────────────────────────────────┐
│ Event 1a (kept)                 │
└─────────────────────────────────┘
(Event 2a removed)
```

#### 2. Lower Event Starts Before and Ends During

**Situation:** The lower priority event starts before the higher priority event but ends during it.

**Action:** The lower priority event is **cut at the start** of the higher priority event.

```
Before:
┌─────────────────────────────────┐
│ Event 2a (lower priority)      │
│      ┌──────────────────────────┐
│      │ Event 1a (higher)        │
│      └──────────────────────────┘
└─────────────────────────────────┘

After:
┌──────┐
│ 2a   │ (kept until start of 1a)
└──────┘
      ┌──────────────────────────┐
      │ Event 1a (kept)          │
      └──────────────────────────┘
```

#### 3. Lower Event Starts During and Ends After

**Situation:** The lower priority event starts during the higher priority event and ends after it.

**Action:** The lower priority event is **cut at the end** of the higher priority event.

```
Before:
      ┌──────────────────────────┐
      │ Event 1a (higher)        │
      └──────────────────────────┘
            ┌─────────────────────────────────┐
            │ Event 2a (lower priority)     │
            └─────────────────────────────────┘

After:
      ┌──────────────────────────┐
      │ Event 1a (kept)          │
      └──────────────────────────┘
            ┌─────────────────────────────────┐
            │ Event 2a (kept after end of 1a) │
            └─────────────────────────────────┘
```

#### 4. Lower Event Completely Wraps Higher Event

**Situation:** The lower priority event completely wraps the higher priority event (starts before and ends after).

**Action:** The lower priority event is **split into 2 separate events**.

```
Before:
┌─────────────────────────────────────────────┐
│ Event 2a (lower priority)                   │
│      ┌──────────────────────────┐          │
│      │ Event 1a (higher)         │          │
│      └──────────────────────────┘          │
└─────────────────────────────────────────────┘

After:
┌──────┐
│ 2a   │ (first part)
└──────┘
      ┌──────────────────────────┐
      │ Event 1a (kept)          │
      └──────────────────────────┘
            ┌─────────────────────────────────┐
            │ Event 2a (second part)          │
            └─────────────────────────────────┘
```

### Usage Examples

#### Example 1: Initial Cut

**Scenario:** Event "1a" starts before event "2a" and ends during it.

```python
import pandas as pd
from datetime import datetime, timedelta
from app.utils.hierarchical_events import HierarchicalEventsService

# Input data
data = pd.DataFrame({
    'event_definition': ['1a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 8, 0),   # 1a: 08:00
        datetime(2024, 1, 1, 9, 0)    # 2a: 09:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 10, 0),  # 1a: 10:00
        datetime(2024, 1, 1, 12, 0)   # 2a: 12:00
    ]
})

# Processing
service = HierarchicalEventsService()
result = service.fix_hierarchical_events(data)

# Expected result:
# - Event 1a: kept intact (08:00 - 10:00)
# - Event 2a: cut to start after end of 1a (10:00 - 12:00)
```

**Visualization:**
```
Before:
08:00 ────────── 10:00 ────────── 12:00
┌──────────────┐
│ Event 1a     │
└──────────────┘
        ┌──────────────────────┐
        │ Event 2a             │
        └──────────────────────┘

After:
08:00 ────────── 10:00 ────────── 12:00
┌──────────────┐
│ Event 1a     │ (kept)
└──────────────┘
                ┌────────────────┐
                │ Event 2a       │ (cut)
                └────────────────┘
```

#### Example 2: Middle Cut

**Scenario:** Event "1a" starts in the middle of event "2a" and ends after it.

```python
# Input data
data = pd.DataFrame({
    'event_definition': ['1a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 10, 0),  # 1a: 10:00 (middle of 2a)
        datetime(2024, 1, 1, 9, 0)    # 2a: 09:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 11, 0),  # 1a: 11:00
        datetime(2024, 1, 1, 12, 0)   # 2a: 12:00
    ]
})

# Expected result:
# - Event 1a: kept intact (10:00 - 11:00)
# - Event 2a: split into 2 parts (09:00 - 10:00) and (11:00 - 12:00)
```

**Visualization:**
```
Before:
09:00 ────────── 10:00 ────────── 11:00 ────────── 12:00
┌──────────────────────────────────────┐
│ Event 2a                             │
└──────────────────────────────────────┘
        ┌──────────────┐
        │ Event 1a     │
        └──────────────┘

After:
09:00 ────────── 10:00 ────────── 11:00 ────────── 12:00
┌──────────────┐
│ Event 2a     │ (first part)
└──────────────┘
                ┌──────────────┐
                │ Event 1a     │ (kept)
                └──────────────┘
                                ┌──────────────┐
                                │ Event 2a     │ (second part)
                                └──────────────┘
```

#### Example 3: Complete Wrap

**Scenario:** Event "1a" completely wraps event "2a" (starts before and ends after).

```python
# Input data
data = pd.DataFrame({
    'event_definition': ['1a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 8, 0),   # 1a: 08:00 (before 2a)
        datetime(2024, 1, 1, 9, 0)    # 2a: 09:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 13, 0),  # 1a: 13:00 (after 2a)
        datetime(2024, 1, 1, 12, 0)   # 2a: 12:00
    ]
})

# Expected result:
# - Event 1a: kept intact (08:00 - 13:00)
# - Event 2a: split into 2 parts (08:00 - 09:00) and (12:00 - 13:00)
#   NOTE: Actually, 2a will be completely removed as it's inside 1a
```

**Visualization:**
```
Before:
08:00 ────────── 09:00 ────────── 12:00 ────────── 13:00
┌──────────────────────────────────────────────────┐
│ Event 1a                                         │
└──────────────────────────────────────────────────┘
        ┌──────────────────────┐
        │ Event 2a             │
        └──────────────────────┘

After:
08:00 ────────── 09:00 ────────── 12:00 ────────── 13:00
┌──────────────────────────────────────────────────┐
│ Event 1a (kept)                                   │
└──────────────────────────────────────────────────┘
(Event 2a removed - completely inside 1a)
```

#### Example 4: Event 4a Wrapping 2a

**Scenario:** Event "4a" completely wraps event "2a".

```python
# Input data
data = pd.DataFrame({
    'event_definition': ['4a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 8, 0),   # 4a: 08:00
        datetime(2024, 1, 1, 9, 0)    # 2a: 09:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 13, 0),  # 4a: 13:00
        datetime(2024, 1, 1, 12, 0)   # 2a: 12:00
    ]
})

# Expected result:
# - Event 2a: kept intact (09:00 - 12:00) - higher priority than 4a
# - Event 4a: split into 2 parts (08:00 - 09:00) and (12:00 - 13:00)
```

**Visualization:**
```
Before:
08:00 ────────── 09:00 ────────── 12:00 ────────── 13:00
┌──────────────────────────────────────────────────┐
│ Event 4a (lower priority)                        │
└──────────────────────────────────────────────────┘
        ┌──────────────────────┐
        │ Event 2a (higher)    │
        └──────────────────────┘

After:
08:00 ────────── 09:00 ────────── 12:00 ────────── 13:00
┌──────────────┐
│ Event 4a     │ (first part)
└──────────────┘
        ┌──────────────────────┐
        │ Event 2a (kept)      │
        └──────────────────────┘
                                ┌──────────────┐
                                │ Event 4a     │ (second part)
                                └──────────────┘
```

#### Example 5: Multiple Higher Priority Events

**Scenario:** Event "2a" overlaps multiple "1a" events.

```python
# Input data
data = pd.DataFrame({
    'event_definition': ['1a', '1a', '2a'],
    'start_time': [
        datetime(2024, 1, 1, 9, 0),   # 1a: 09:00
        datetime(2024, 1, 1, 11, 0),  # 1a: 11:00
        datetime(2024, 1, 1, 8, 0)    # 2a: 08:00
    ],
    'end_time': [
        datetime(2024, 1, 1, 10, 0),  # 1a: 10:00
        datetime(2024, 1, 1, 12, 0),  # 1a: 12:00
        datetime(2024, 1, 1, 13, 0)   # 2a: 13:00
    ]
})

# Expected result:
# - Events 1a: kept intact (09:00-10:00) and (11:00-12:00)
# - Event 2a: split into 3 parts:
#   * (08:00 - 09:00) - before first 1a
#   * (10:00 - 11:00) - between the two 1a
#   * (12:00 - 13:00) - after second 1a
```

**Visualization:**
```
Before:
08:00 ─── 09:00 ─── 10:00 ─── 11:00 ─── 12:00 ─── 13:00
┌──────────────────────────────────────────────────┐
│ Event 2a                                         │
└──────────────────────────────────────────────────┘
        ┌──────┐           ┌──────┐
        │ 1a   │           │ 1a   │
        └──────┘           └──────┘

After:
08:00 ─── 09:00 ─── 10:00 ─── 11:00 ─── 12:00 ─── 13:00
┌──────┐
│ 2a   │ (first part)
└──────┘
        ┌──────┐
        │ 1a   │ (kept)
        └──────┘
                ┌──────┐
                │ 2a   │ (second part)
                └──────┘
                        ┌──────┐
                        │ 1a   │ (kept)
                        └──────┘
                                ┌──────┐
                                │ 2a   │ (third part)
                                └──────┘
```

### Class Reference

#### Class `HierarchicalEventsService`

##### `__init__(event_types: List[str] = None)`

Initializes the hierarchical events service.

**Parameters:**
- `event_types`: List of event types in priority order (first = highest priority). Default: `["1", "2", "4"]`

**Example:**
```python
# Using default hierarchy
service = HierarchicalEventsService()

# Using custom hierarchy
service = HierarchicalEventsService(event_types=["1", "3", "5"])
```

##### `fix_hierarchical_events(data: pd.DataFrame) -> pd.DataFrame`

Adjusts hierarchical events by removing or cutting overlaps between events of different priorities.

**Parameters:**
- `data`: DataFrame containing events with columns:
  - `event_definition`: event type
  - `start_time`: event start
  - `end_time`: event end
  - `total_duration_seconds`: (optional) total duration in seconds
  - `rlt`: (optional) event relation
  - other event-specific columns

**Returns:**
- DataFrame with adjusted events, without improper overlaps between different priority levels

**Example:**
```python
import pandas as pd
from app.utils.hierarchical_events import HierarchicalEventsService

# Prepare data
data = pd.DataFrame({
    'event_definition': ['1a', '2a'],
    'start_time': [datetime(2024, 1, 1, 8, 0), datetime(2024, 1, 1, 9, 0)],
    'end_time': [datetime(2024, 1, 1, 10, 0), datetime(2024, 1, 1, 12, 0)]
})

# Process
service = HierarchicalEventsService()
result = service.fix_hierarchical_events(data)
```

### Internal Methods

#### `_extract_event_type(event_definition: str) -> str`

Extracts the event type from `event_definition`.

**Example:**
- `"1a"` → `"1"`
- `"2b"` → `"2"`
- `"4a"` → `"4"`

#### `_events_overlap(start1, end1, start2, end2) -> bool`

Checks if two time intervals overlap.

#### `_adjust_event_against_higher_priority(lower_event, higher_priority_events) -> List[pd.Series]`

Adjusts a lower priority event relative to higher priority events.

**Returns:**
- List of pandas Series with adjusted events (can be empty, 1 or 2 events)

#### `_adjust_event_times_by_hierarchy(data: pd.DataFrame) -> pd.DataFrame`

Adjusts event times based on the defined hierarchy.

### Business Rules

1. **Priority Preservation:** Higher priority events are never modified.

2. **Small Interval Removal:** Intervals smaller than 1 second are ignored to avoid insignificant events.

3. **Blocked Interval Merging:** Multiple overlapping higher priority events are merged into unique blocked intervals.

4. **Data Preservation:** All original event columns are preserved in adjusted events, except `start_time`, `end_time` and `total_duration_seconds` which are recalculated.

5. **Non-Hierarchical Events:** Events whose type is not in the priority list are kept intact and added back to the final result.

### Pipeline Integration

The service is used in `EventFrameService` after filling OEE detail data:

```python
# In event_frame_service.py
hierarchical_service = HierarchicalEventsService()
event_frames_df = hierarchical_service.fix_hierarchical_events(event_frames_df)
```
