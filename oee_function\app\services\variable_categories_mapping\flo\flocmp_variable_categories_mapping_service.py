from typing import Any

import pandas as pd


class FloCmpVariableCategoriesMappingService:
    def __init__(self) -> None:
      
        self._mapping_subcatlevel2_dict = { 
            "ShiftNSTime": "",
            "ShiftMSPMTime": "",
            "ShiftPPTime": "",
            "ShiftMPELTime": "",
            "ShiftOPTime": "",
            "ShiftVACPTime": "",
            "ShiftDPCTime": "",
            "ShiftMELETime": "",
            "ShiftEPTime": "",
            "ShiftMEZTTime": "Zone Temperatures",
            "ShiftMSFVSTime": "Side Feeder",
            "ShiftMOTime": "",
            "ShiftFPTime": "",
            "ShiftMFITime": "",
            "ShiftMCONTime": "Finished Product Conveying",
            "ShiftMPKGTime": "",
            "ShiftIDLETime": "",
            "ShiftNTSTOTime": "",
            "ShiftMAKTime": "",
            "ShiftQSFTime": "",
            "ShiftQOTime": "",
            "ShiftQCTime": "",
            "ShiftRMWOBTime": "",
            "ShiftFCOTime": "Full",
            "ShiftPCOTime": "Partial",
            "ShiftDLBCOTime": "Blow and Go - Transition on the fly",
            "ShiftRMNHTime": "",
            "ShiftRMOTime": "",
        }
      
        self._mapping_subcatlevel1_dict = {
            "ShiftNSTime": "No Demand",
            "ShiftMSPMTime": "",
            "ShiftPPTime": "Quench / Cutter / Water Removal",
            "ShiftMPELTime": "Quench / Cutter / Water Removal",
            "ShiftOPTime": "",
            "ShiftVACPTime": "Vacuum",
            "ShiftDPCTime": "Extruder",
            "ShiftMELETime": "",
            "ShiftEPTime": "Extruder",
            "ShiftMEZTTime": "Extruder",
            "ShiftMSFVSTime": "Extruder",
            "ShiftMOTime": "",
            "ShiftFPTime": "Feeding System",
            "ShiftMFITime": "Feeding System",
            "ShiftMCONTime": "Finished Product",
            "ShiftMPKGTime": "Packaging",
            "ShiftIDLETime": "Manpower",
            "ShiftNTSTOTime": "Manpower",
            "ShiftMAKTime": "Classifier / Screener",
            "ShiftQSFTime": "Waiting on Test Result",
            "ShiftQOTime": "Waiting on Test Result",
            "ShiftQCTime": "Scrap",
            "ShiftRMWOBTime": "Internal Raw Material",
            "ShiftFCOTime": "Transition",
            "ShiftPCOTime": "Transition",
            "ShiftDLBCOTime": "Transition",
            "ShiftRMNHTime": "External Raw Material",
            "ShiftRMOTime": "Raw Material Conveying",
        }

        self._mapping_eventcode_dict = {
            "ShiftNSTime": "No Demand",
            "ShiftMSPMTime": "Planned Downtime",
            "ShiftPPTime": "Reactive Downtime",
            "ShiftMPELTime": "Reactive Downtime",
            "ShiftOPTime": "Reactive Downtime",
            "ShiftVACPTime": "Reactive Downtime",
            "ShiftDPCTime": "Reactive Downtime",
            "ShiftMELETime": "Reactive Downtime",
            "ShiftEPTime": "Reactive Downtime",
            "ShiftMEZTTime": "Reactive Downtime",
            "ShiftMSFVSTime": "Reactive Downtime",
            "ShiftMOTime": "Reactive Downtime",
            "ShiftFPTime": "Reactive Downtime",
            "ShiftMFITime": "Reactive Downtime",
            "ShiftMCONTime": "Reactive Downtime",
            "ShiftMPKGTime": "Reactive Downtime",
            "ShiftIDLETime": "Reactive Downtime",
            "ShiftNTSTOTime": "Reactive Downtime",
            "ShiftMAKTime": "Reactive Downtime",
            "ShiftQSFTime": "Reactive Downtime",
            "ShiftQOTime": "Reactive Downtime",
            "ShiftQCTime": "Scrap",
            "ShiftRMWOBTime": "Reactive Downtime",
            "ShiftFCOTime": "Product And Supply Optimization",
            "ShiftPCOTime": "Product And Supply Optimization",
            "ShiftDLBCOTime": "Product And Supply Optimization",
            "ShiftRMNHTime": "Product And Supply Optimization",
            "ShiftRMOTime": "Reactive Downtime",
        }

        self._mapping_metriccode_dict = {
            "ShiftNSTime": "Loading",
            "ShiftMSPMTime": "Availability",
            "ShiftPPTime": "Availability",
            "ShiftMPELTime": "Availability",
            "ShiftOPTime": "Availability",
            "ShiftVACPTime": "Availability",
            "ShiftDPCTime": "Availability",
            "ShiftMELETime": "Availability",
            "ShiftEPTime": "Availability",
            "ShiftMEZTTime": "Availability",
            "ShiftMSFVSTime": "Availability",
            "ShiftMOTime": "Availability",
            "ShiftFPTime": "Availability",
            "ShiftMFITime": "Availability",
            "ShiftMCONTime": "Availability",
            "ShiftMPKGTime": "Availability",
            "ShiftIDLETime": "Availability",
            "ShiftNTSTOTime": "Availability",
            "ShiftMAKTime": "Availability",
            "ShiftQSFTime": "Availability",
            "ShiftQOTime": "Availability",
            "ShiftQCTime": "Quality",
            "ShiftRMWOBTime": "Availability",
            "ShiftFCOTime": "Availability",
            "ShiftPCOTime": "Availability",
            "ShiftDLBCOTime": "Availability",
            "ShiftRMNHTime": "Availability",
            "ShiftRMOTime": "Availability",
        }

    def map(self, cat_column: str, property: str) -> str:
        property_dict_mapping = {
            "subCategoryLevel2": self._mapping_subcatlevel2_dict,
            "subCategoryLevel1": self._mapping_subcatlevel1_dict,
            "eventCode": self._mapping_eventcode_dict,
            "metricCode": self._mapping_metriccode_dict,
        }
        property_dict = property_dict_mapping.get(property)
        return property_dict.get(cat_column)

    def classify_florence_event(self,
                                  row: pd.DataFrame,
                                  cat_columns: list,
                                  events_hierarchy_mapper: dict[str, dict[str, Any]]
                                  ):

      has_variable_categories = events_hierarchy_mapper.get(
            row.event_definition
        ).get("variableCategories")

      definition_event = events_hierarchy_mapper.get(row.event_definition, {}).get('eventDefinition')
      row['def'] = definition_event

      non_zero_shifts = [col for col in cat_columns if row[col] > 0]

      if len(non_zero_shifts) == 1 and has_variable_categories:
        shift_col = non_zero_shifts[0]
        row['subcat_level1'] = self.map(shift_col, 'subCategoryLevel1')
        row['event_code'] = self.map(shift_col, 'eventCode')
        row['metric_code'] = self.map(shift_col, 'metricCode')
        row['subcat_level2'] = self.map(shift_col, 'subCategoryLevel2')

      elif not has_variable_categories:
        row['subcat_level1'] = events_hierarchy_mapper.get(row.event_definition, {}).get('subCategoryLevel1')
        row['event_code'] = events_hierarchy_mapper.get(row.event_definition, {}).get('eventCode')
        row['metric_code'] = events_hierarchy_mapper.get(row.event_definition, {}).get('metricCode')
        row['subcat_level2'] = events_hierarchy_mapper.get(row.event_definition, {}).get('subCategoryLevel2')

      else:
        row['subcat_level1'] = ''
        row['event_code'] = ''
        row['metric_code'] = ''
        row['subcat_level2'] = ''

      return row
