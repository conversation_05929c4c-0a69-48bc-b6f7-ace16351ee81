from typing import Any

import numpy as np
import pandas as pd
from app.infra.logger_adapter import get_logger
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.base_service import BaseService
from app.services.tag_value_mapping.fra.frac_pom_product_description_mapping_service import (
  FracPomProductDescriptionMappingService,
)
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.services.riser_product_event_service import RiserProductEventService
from app.utils.constants import Constants as const
from app.utils.uom_conversion import kg_to_mt

log = get_logger()
class FracPomEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self.base_service = BaseService()
        
        self._day_data_riser = None
        
        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)

        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp,
            multi_product=True
        )
        
        self._riser_product_names = ["Riser product (during product change)"]

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - (ProductionLineStatus4 = 'Ein' (0) & ProductionLineStatus1 <= 1000 & ProductionLineStatus2 <= 1000 & ProductionLineStatus3 <= 1000)
        # OR (ProductionLineStatus4 = 'Aus' (1) & ProductionLineStatus1 <= 1000 & ProductionLineStatus2 <= 1000)
        data = data.assign(
            event1a_start=(
               (
                 (data["ProductionLineStatus4"] == 1) & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000) & (data["ProductionLineStatus3"] <= 1000)
               ) |
               (
                 (data["ProductionLineStatus4"] == 0) & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000)
               )
            )
        )

        # event end - (ProductionLineStatus4 = 'Ein' (0) & (ProductionLineStatus1 > 1000 or ProductionLineStatus2 > 1000 or ProductionLineStatus3 > 1000) )
        # OR (ProductionLineStatus4 = 'Aus' (1) & (ProductionLineStatus1 > 1000 or ProductionLineStatus2 > 1000))
        data = data.assign(
            event1a_end=(
               (
                 (data["ProductionLineStatus4"] == 1) & (
                    ((data["ProductionLineStatus1"] > 1000) & (data["ProductionLineStatus1"].shift(1) <= 1000)) |
                    ((data["ProductionLineStatus2"] > 1000) & (data["ProductionLineStatus2"].shift(1) <= 1000)) |
                    ((data["ProductionLineStatus3"] > 1000) & (data["ProductionLineStatus3"].shift(1) <= 1000))
                 )
               ) |
               (
                 (data["ProductionLineStatus4"] == 0) & (
                 ( (data["ProductionLineStatus1"] > 1000) & (data["ProductionLineStatus1"].shift(1) <= 1000)) |
                 ( (data["ProductionLineStatus2"] > 1000) & (data["ProductionLineStatus2"].shift(1) <= 1000))
                )
              )
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        df_events = self.__create_day_data_riser(data)

        return df_events[df_events[const.RLT] > 0].reset_index(drop=True)

    def identify_events_typeIIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        df_events = self.__create_day_data_riser(data)

        return df_events[df_events[const.RLT] < 0].reset_index(drop=True)

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE])
        
        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]

        return day_data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE])

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE])

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIVa(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIVb(self):
        pass
    
    @staticmethod
    def calculate_net_production_fn(df: pd.DataFrame) -> pd.Series:
        PER_HOUR_TO_PER_SEC_FACTOR = 1 / 3600

        # Calculate total_produced_key directly
        total_produced = kg_to_mt(
            np.where(
                df['ProductionLineStatus4'] == 1,
                (df["NetProduction1"] + df["NetProduction2"] + df["NetProduction3"]) * (df["ConversionPITag"] / 100) * df["CorrectionFactor"],
                (df["NetProduction1"] + df["NetProduction2"]) * (df["ConversionPITag"] / 100) * df["CorrectionFactor"]
            )
        )

        # Calculate net_production directly
        net_production = total_produced * PER_HOUR_TO_PER_SEC_FACTOR * df["dt"]

        return net_production

    def _split_intervals(
        self, row: pd.DataFrame, event_definition: str, shifts: list[str]
    ) -> list:
        """
        splits the event frames into intervals based on the business rules defined. An event should end (and another should start) when:
        :param row: row containing the start time and the end times
        :type row: pd.DataFrame
        :param event_definition: definition of the event we are splitting
        :type event_definition: str
        :return: list of the events within the original events
        :rtype: list
        """

        # get variables to start split analysis
        intervals = []
        start = row[f"event{event_definition}_start"]
        end = row[f"event{event_definition}_end"]
        current_time = start

        # iterate through each of the timespans within the
        # original time frame

        shifts_times: list[pd.Timestamp] = [
            pd.Timestamp(shift) for shift in shifts
        ]

        while current_time < end:
            # build a candidate list to be the next time
            candidates = []

            for shift_time in shifts_times:
                entry = current_time.replace(
                    hour=shift_time.hour,
                    minute=shift_time.minute,
                    second=0,
                    microsecond=000000,
                )  # type: ignore
                if shift_time.hour == 0 and shift_time.minute == 0:
                    entry = entry + pd.DateOffset(days=1)
                candidates.append(entry)
            candidates.append(end)

            # find the closest timestamp and ensure it is newer than
            # the present timestamp
            next_time = min(candidates)
            is_next_time_lower = next_time <= current_time

            while is_next_time_lower:
                candidates.remove(next_time)
                next_time = min(candidates)
                is_next_time_lower = next_time <= current_time

            # append to the list a tuple with the timestamp of start, the
            # selected end and the status code. Update the current time
            to_append = [current_time, next_time]
            for col in row.index:
                if col not in [
                    f"event{event_definition}_start",
                    f"event{event_definition}_end",
                ]:
                    to_append.append(row[col])
            intervals.append(tuple(to_append))
            current_time = next_time

        return intervals

    def _identify_working_shifts_transitions(
        self, data: pd.DataFrame, event_definition: str, shifts: list[str]
    ) -> pd.DataFrame:
        """
        identifies working shifts transitions during events

        :param data: events data with start and end times
        :type data: pd.DataFrame
        :param event_definition: definition of the event we are splitting
        :type event_definition: str
        :return: event data with working shifts transitions
        :rtype: pd.DataFrame
        """

        # split intervals according to business rules
        new_rows = data.apply(
            self._split_intervals,
            event_definition=event_definition,
            shifts=shifts,
            axis=1,
        ).explode()

        # create data frame
        events_data = pd.DataFrame(new_rows.tolist(), columns=data.columns)

        return events_data

    def create_dataframe_loss_1a_event(
          self, data: pd.DataFrame
      ) -> pd.DataFrame:

        # FRA POM demands creating events 1a to calculate running time
        df_losses_1a = self.identify_events_typeIa(data)

        df_losses_1a.dropna(
            subset=[f"event1a_start", f"event1a_end"],
            inplace=True,
        )

        df_losses_1a.set_index("index", inplace=True)

        df_losses_1a = self.base_service.create_event_dataframe(df_losses_1a, '1a')

        shift_frapom = ['2024-01-01 05:30:00', '2024-01-01 17:30:00', '2024-01-01 00:00:00']
        df_losses_1a = self._identify_working_shifts_transitions(df_losses_1a, '1a', shift_frapom)

        df_losses_1a = self.base_service.calculate_time_duration(df_losses_1a, 'event1a_start', 'event1a_end')

        df_losses_1a['event1a_date_loss'] = df_losses_1a['event1a_start'].dt.normalize()

        df_losses_1a.drop(columns = ['event1a_start', 'event1a_end', 'event_definition'], inplace = True)

        df_losses_1a.rename(columns = {'total_duration_seconds': 'loss_duration_seconds'}, inplace = True)

        df_losses_1a = df_losses_1a.groupby('event1a_date_loss', as_index=False).agg({'loss_duration_seconds': 'sum'})

        return df_losses_1a

    def not_running_fn(self, data: pd.DataFrame) -> pd.DataFrame:
        if not self._not_running.empty:
            return self._not_running

        # Modified running_time calculation
        not_running_condition = (
            (
                (data["ProductionLineStatus4"] == 1)
                & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000)
                & (data["ProductionLineStatus3"] <= 1000)
            )
            | (
                (data["ProductionLineStatus4"] == 0)
                & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000)
            )
        )

        self._not_running = not_running_condition

        return not_running_condition
    
    def __create_day_data_riser(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 2a and 2b

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type II included
        :rtype: pd.DataFrame
        """
        if self._day_data_riser is not None:
            return self._day_data_riser.copy()
        
        riser_service = RiserProductEventService(
            msdp=self._msdp,
            reporting_line_external_id=self._reporting_line_external_id,
            net_production_fn=self.calculate_net_production_fn
        )

        data["event2ab_start"] = (
            (
               (
                 (data["ProductionLineStatus4"] == 1) & (
                    (data["ProductionLineStatus1"] > 1000) |
                    (data["ProductionLineStatus2"] > 1000) |
                    (data["ProductionLineStatus3"] > 1000)
                 )
               ) |
               (
                 (data["ProductionLineStatus4"] == 0) & (
                    (data["ProductionLineStatus1"] > 1000) |
                    (data["ProductionLineStatus2"] > 1000)
                 )
              )
            )
            & (data["ProductDescription"].isin(self._riser_product_names))
        )
        
        data["event2ab_start"] = data["event2ab_start"] & (~data["event2ab_start"].shift(1).fillna(False))
        
        data["event2ab_end"] = (
            (
                (data["ProductionLineStatus4"] == 1) & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000) & (data["ProductionLineStatus3"] <= 1000)
            ) |
            (
                (data["ProductionLineStatus4"] == 0) & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000)
            )
            | ((data["ProductDescription"].shift(1).isin(self._riser_product_names))
            & (~data["ProductDescription"].isin(self._riser_product_names)))
        )
        
        data["event2ab_end"] = data["event2ab_end"] & (~data["event2ab_end"].shift(1).fillna(False))

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~self.not_running_fn(data))
        )

        df_events = riser_service.get_rlt_and_msdp_riser_event_blocks(data, self._riser_product_names)

        if df_events.empty:
            return df_events
        
        self._day_data_riser = df_events
        
        return df_events