from datetime import datetime
import pandas as pd
from cognite.client import CogniteClient
from cognite.client.data_classes.time_series import TimeSeriesList

class TimeSeriesRepository:
    def __init__(self, cognite_client: CogniteClient) -> None:
        self._cognite_client = cognite_client

    def retrieve_dataframe(
        self, external_ids: set[str], start_time: datetime, end_time: datetime
    ):
        ts_results = self._cognite_client.time_series.data.retrieve_dataframe(
            external_id=list(external_ids),
            start=start_time,
            end=end_time,
        )

        return ts_results

    def retrieve_multiple(self, external_ids: set[str]) -> TimeSeriesList:
        return self._cognite_client.time_series.retrieve_multiple(
            external_ids=list(external_ids),
        )

    def retrieve_multiple_as_data_frame(self, external_ids: set[str]) -> pd.DataFrame:
        return self.retrieve_multiple(external_ids).to_pandas()
