from typing import Any

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.utils.uom_conversion import lb_to_mt
from app.utils.constants import Constants as const
from app.utils.event_detection_utils import pair_start_end_indexes, keep_only_first_occurrence
from app.utils.uom_conversion import HOURS_TO_SECONDS_FACTOR

class CLKMS1EventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._day_data = None
        
        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # remove unecessary data
        identification_columns = [
            "ProductionLineStatus_1",
            "ProductionLineStatus_2",
            "V9302CrudeMeOHOutletFlow"
        ]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )

        # event trigger start - ProductionLineStatus_1 < 10
        # AND ProductionLineStatus_2 < 10
        data = data.assign(
            event1a_start=self.not_running_condition_start(data)
        )

        # event end - ProductionLineStatus_1 > 10
        # AND ProductionLineStatus_2 > 10
        # AND V9302CrudeMeOHOutletFlow < 10
        data = data.assign(
            event1a_end=self.not_running_condition_end(data)
        )

        data["event1a_start"] = keep_only_first_occurrence(data, "event1a_start")
        data["event1a_end"] = keep_only_first_occurrence(data, "event1a_end")

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """

        # remove unecessary data
        identification_columns = [
            "ProductionLineStatus_1",
            "ProductionLineStatus_2",
            "V9302CrudeMeOHOutletFlow",
        ]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )

        # event trigger start - (ProductionLineStatus_1 AND ProductionLineStatus_2) > 10
        # AND V9302CrudeMeOHOutletFlow < 10
        data = data.assign(
            event2a_start=self.not_running_condition_end(data)
        )

        # event end - (ProductionLineStatus_1 AND ProductionLineStatus_2) > 10
        # AND V9302CrudeMeOHOutletFlow > 10
        data = data.assign(
            event2a_end=self.starting_up_condition_end(data)
        )

        data["event2a_start"] = keep_only_first_occurrence(data, "event2a_start")
        data["event2a_end"] = keep_only_first_occurrence(data, "event2a_end")

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "first", [const.MSDP, const.SCHEDULED_RATE], -1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data


    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "first", [const.MSDP, const.SCHEDULED_RATE], -1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIIIc(self):
        pass

    def identify_events_typeIVa(self, data: pd.DataFrame, **args):
        """
        identifies events of type IVa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.create_day_waste(data)
        
        data["total_duration_seconds"] = data["qlt"]

        return data

    def identify_events_typeIVb(self):
        pass
    
    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced
        
        net_production = lb_to_mt(df["NetProduction"])
        
        self._total_produced = net_production

        return net_production

    def not_running_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._not_running.empty:
            return self._not_running
        
        df["not_running_start"] = self.not_running_condition_start(df)
        df["not_running_end"] = self.not_running_condition_end(df)
        
        df["not_running_start"] = keep_only_first_occurrence(df, "not_running_start")
        df["not_running_end"] = keep_only_first_occurrence(df, "not_running_end")
        
        df["starting_up_start"] = df["not_running_end"]
        df["starting_up_end"] = self.starting_up_condition_end(df)
        
        df["starting_up_end"] = keep_only_first_occurrence(df, "starting_up_end")
        
        df["not_running"] = False
        df["starting_up"] = False
        
        pair_start_end_indexes(df, "not_running_start", "not_running_end", "not_running", True)
        pair_start_end_indexes(df, "starting_up_start", "starting_up_end", "starting_up", True)
        
        not_running = df["not_running"] | df["starting_up"]
        
        self._not_running = not_running
        
        return not_running

    def __remove_unecessary_data(
        self, data: pd.DataFrame, columns_to_keep: list[str]
    ) -> pd.DataFrame:
        remove_columns = [
            col
            for col in data.columns.to_list()
            if col not in columns_to_keep + ["index"]
        ]
        return data.drop(remove_columns, axis=1).dropna(
            subset=columns_to_keep, how="all"
        )

    def not_running_condition_start(self, df: pd.DataFrame) -> pd.Series:
        not_running_start = (df["ProductionLineStatus_1"] < 10) & (df["ProductionLineStatus_2"] < 10)
        return not_running_start
        
    def not_running_condition_end(self, df: pd.DataFrame) -> pd.Series:
        not_running_end = (df["ProductionLineStatus_1"] > 10) & (df["ProductionLineStatus_2"] > 10) & (df["V9302CrudeMeOHOutletFlow"] < 10)
        return not_running_end
    
    def starting_up_condition_end(self, df: pd.DataFrame) -> pd.Series:
        starting_up_end = (df["ProductionLineStatus_1"] > 10) & (df["ProductionLineStatus_2"] > 10) & (df["V9302CrudeMeOHOutletFlow"] > 10)
        return starting_up_end

    def create_day_waste(self, data: pd.DataFrame) -> pd.DataFrame:
        # cut off days with incomplete data
        data = self._rlt._apply_cutoffs(data)
        
        # Calculate waste
        data.loc[data["WasteTotalizer"] < 0, "WasteTotalizer"] = 0
        data["WasteTotalizer"] = data["WasteTotalizer"] * 1000
        data["waste"] = data["WasteTotalizer"] / 60 * data["dt"]
        data["waste"] = lb_to_mt(data["waste"])
        
        # Running time condition
        data[const.RUNNING_TIME] = self.not_running_fn(data)
        
        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~data[const.RUNNING_TIME])
        )
        
        # Set waste to 0 when not running
        data.loc[data[const.RUNNING_TIME] == True, "waste"] = 0
        
        # Resample to daily data
        data.set_index("index", inplace=True, drop=True)
        day_data = data.resample("D").agg({"waste": "sum", "duration_in_seconds": "sum"}).reset_index()
        day_data.rename(columns={"index": "timestamp"}, inplace=True)
        
        # Calculate running time in hours
        day_data[const.RUNNING_TIME] = (
            day_data.groupby(pd.Grouper(key="timestamp", freq="D"))["duration_in_seconds"].transform("sum") / 3600
        )
        
        # Add MSDP values
        day_data = self._rlt._add_msdp_values(day_data, [const.MSDP])
        
        # Calculate quality loss time (qlt)
        day_data["qlt"] = day_data["waste"]/(day_data[const.MSDP]/24)
        day_data["qlt"] = day_data["qlt"] * HOURS_TO_SECONDS_FACTOR
        
        day_data["start_time"] = day_data["timestamp"]
        day_data["end_time"] = day_data["timestamp"] + pd.Timedelta(days=1)
        
        # Filter out days with qlt <= 0 and running time <= 0
        day_data = day_data[day_data["qlt"] > 0]
        day_data = day_data[day_data[const.RUNNING_TIME] > 0]
        
        return day_data