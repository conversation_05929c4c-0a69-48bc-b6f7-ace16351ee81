from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.utils.constants import Constants as const
from app.utils.uom_conversion import kg_to_mt
from app.utils.event_detection_utils import detect_sustained_transition, pair_start_end_indexes


class BisPARAFORMEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        
        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)
        
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp
        )
        
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # Event trigger start
        # ProductionLineStatus1 < 165 for 5 minutes
        event_start = self.not_running_start_condition_PFM(data)

        data = data.assign(
            event1a_start=event_start
        )
        
        # Event trigger end
        # ProductionLineStatus2 goes from > 300 to <= 170
        event_end = self.starting_up_start_condition_PFM(data)
        
        data = data.assign(
            event1a_end=event_end
        )

        data.reset_index(inplace=True, drop=False)

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        Identifies events type IIa

        """
        
        # Event trigger start
        # ProductionLineStatus2 goes from > 300 to <= 170
        event_start = self.starting_up_start_condition_PFM(data)

        data = data.assign(
            event2a_start=event_start
        )
        
        # Event trigger end
        # ProductionLineStatus1 > 165 for 5 minutes
        event_end = self.starting_up_end_condition_PFM(data)
        data = data.assign(
            event2a_end=event_end
        )
        
        data.reset_index(inplace=True, drop=False)

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIb(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)
        
        if data.index.name == "index":
            data.reset_index(inplace=True, drop=False)

        day_data = self._rlt.create_day_data(data, "first", [const.MSDP, const.SCHEDULED_RATE], 1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data
    
    def identify_events_typeIIIc(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)
        
        if data.index.name == "index":
            data.reset_index(inplace=True, drop=False)

        day_data = self._rlt.create_day_data(data, "first", [const.MSDP, const.SCHEDULED_RATE], 1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data
    
    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced
        
        net_production = kg_to_mt(df["NetProduction"])
        
        self._total_produced = net_production

        return net_production
    
    def not_running_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._not_running.empty:
            return self._not_running
        
        df["not_running_start"] = self.not_running_start_condition_PFM(df)
        df["not_running_end"] = self.starting_up_start_condition_PFM(df)
        
        df["not_running"] = False
        pair_start_end_indexes(df, "not_running_start", "not_running_end", "not_running", True)
        
        df["starting_up_start"] = self.starting_up_start_condition_PFM(df)
        df["starting_up_end"] = self.starting_up_end_condition_PFM(df)
        
        df["starting_up"] = False
        pair_start_end_indexes(df, "starting_up_start", "starting_up_end", "starting_up", True)
        
        not_running = df["not_running"] | df["starting_up"]
        
        self._not_running = not_running

        return not_running
    
    def not_running_start_condition_PFM(self, df: pd.DataFrame) -> pd.Series:
        not_running_start = detect_sustained_transition(df=df, col="ProductionLineStatus1", threshold=165, duration="5min", condition="lt")
        
        return not_running_start

    def starting_up_start_condition_PFM(self, df: pd.DataFrame) -> pd.Series:
        # Step 1: Identify "Not Running" periods (when Status1 < 165)
        not_running_period = df["ProductionLineStatus1"] < 165
        
        # Step 2: Create period IDs (each continuous "not running" stretch gets an ID)
        period_id = (~not_running_period).cumsum()
        
        # Step 3: Track if Status2 has been >300 during current period
        has_been_high = (
            (df["ProductionLineStatus2"] > 300)
            .groupby(period_id)
            .cummax()  # Once true, stays true for that period
        )
        
        # Step 4: Detect first crossing to <=170 after being >300
        is_low = df["ProductionLineStatus2"] <= 170
        was_high_before = has_been_high.shift(1).fillna(False)
        
        # Detect transition: was in "high" state, now crossing to "low"
        first_crossing = is_low & was_high_before & ~is_low.shift(1).fillna(False)
        
        # Step 5: Only flag during "Not Running" periods
        end_condition = first_crossing & not_running_period
        
        return end_condition
    
    def starting_up_end_condition_PFM(self, df: pd.DataFrame) -> pd.Series:
        starting_up_end = detect_sustained_transition(df=df, col="ProductionLineStatus1", threshold=165, duration="5min", condition="gt")
        
        return starting_up_end