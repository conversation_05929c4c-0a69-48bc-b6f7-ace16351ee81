from cognite.client import Cognite<PERSON>lient
from cognite.client.data_classes.data_modeling.data_models import DataModelId

from ..models.timeseries_configuration import TimeseriesConfiguration
from .view_repository import ViewRepository


class TimeseriesConfigurationRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        view_repository: ViewRepository,
        data_model_id: DataModelId,
    ) -> None:
        self._cognite_client = cognite_client
        self._view_repository = view_repository
        self._data_model_id = data_model_id

    def create_configuration(
        self, configuration: list[TimeseriesConfiguration]
    ) -> None:
        if not configuration:
            return

        if not self._view_repository:
            raise ValueError(
                "Missing View Repository when instantiating "
                "the TimeseriesConfiguration Repository."
            )

        timeseries_configuration_view = self._view_repository.get_view_id(
            "OEETimeseriesConfiguration"
        )

        nodes = [
            config.convert_to_cognite_node(timeseries_configuration_view)
            for config in configuration
        ]

        paginated_nodes = [
            nodes[1000 * i : 1000 * (i + 1)]
            for i in range(int(len(nodes) / 1000) + 1)
        ]

        for entries in paginated_nodes:
            self._cognite_client.data_modeling.instances.apply(nodes=entries)
