from datetime import time, datetime
import pandas as pd
from typing import List

class DateUtils:
    @staticmethod
    def prepare_date_to_search(date: pd.Series) -> pd.Series:
        """
        Converts a pandas Series of dates to UTC and formats them as ISO 8601 strings without microseconds.
        Args:
            date (pd.Series): A pandas Series containing datetime objects.
        Returns:
            pd.Series: A pandas Series with dates formatted as ISO 8601 strings in UTC.
        """
        return pd.to_datetime(date).dt.tz_convert('UTC').apply(lambda x: x.replace(microsecond=0).isoformat())

    @staticmethod
    def get_timestamp_shift(shifts: List[str], data: pd.DataFrame) -> pd.DataFrame:
        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None
            if first_timestamp.time() == mid_night_time
            else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            (
                (start_cutoff is not None)
                & (data["index"].dt.date > start_cutoff)
            )
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        if data.empty:
            return data
        
        shift_times = sorted([datetime.strptime(s, "%Y-%m-%d %H:%M:%S").time() for s in shifts])

        # return data to the shift start time
        def get_shift_start(ts):
            t = ts.time()
            candidates = [s for s in shift_times if (t >= s)]
            shift_time = candidates[-1] if candidates else shift_times[-1]
            return datetime.combine(ts.date(), shift_time)   

        shift_start = pd.to_datetime(data["index"].map(get_shift_start))
        if data["index"].dt.tz is None:
            shift_start = shift_start.dt.tz_localize("America/New_York")
        else:
            shift_start = shift_start.dt.tz_localize(data["index"].dt.tz).dt.tz_convert("America/New_York")

        data["group_shift"] = shift_start

        return data
