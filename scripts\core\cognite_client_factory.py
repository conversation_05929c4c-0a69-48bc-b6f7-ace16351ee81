from cognite.client import ClientConfig, CogniteClient
from cognite.client.credentials import Cred<PERSON><PERSON>rovider, OAuthClientCredentials, Token

from core.env_variables import EnvVariables


class CogniteClientFactory:
    @staticmethod
    def _create_credentials(
        env_variables: EnvVariables, from_cicd_credentials: bool
    ) -> CredentialProvider:
        auth_variables = env_variables.auth        
        return OAuthClientCredentials(
            token_url=auth_variables.token_uri,
            client_id=(
                auth_variables.client_id
                if not from_cicd_credentials
                else auth_variables.cicd_client_id
            ),
            client_secret=(
                auth_variables.secret
                if not from_cicd_credentials
                else auth_variables.cicd_secret
            ),
            scopes=auth_variables.scopes,
        )

    @staticmethod
    def _create_client_config(
        env_variables: EnvVariables, from_cicd_credentials: bool
    ) -> ClientConfig:
        cognite_variables = env_variables.cognite
        return ClientConfig(
            client_name=cognite_variables.client_name,
            project=env_variables.cognite.project,
            credentials=CogniteClientFactory._create_credentials(
                env_variables, from_cicd_credentials
            ),
            base_url=cognite_variables.base_uri,
            timeout=150,
        )

    @staticmethod
    def create(
        env_variables: EnvVariables, from_cicd_credentials: bool = True
    ) -> CogniteClient:
        return CogniteClient(
            config=CogniteClientFactory._create_client_config(
                env_variables, from_cicd_credentials
            )
        )
