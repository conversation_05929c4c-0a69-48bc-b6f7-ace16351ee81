import pandas as pd
import numpy as np
from typing import Literal, Optional, Callable
from app.models.lead_product import LeadProduct
from app.utils.data_frame import set_column_as_index
from app.utils.event_detection_utils import (
    pair_start_end_indexes,
    keep_only_first_occurrence,
)
from app.infra.logger_adapter import get_logger
import app.utils.event_detection_utils as ed_utils

log = get_logger()


class WashingtonWorksEventIdentificationService:
    """
    Base Event Identification service for Washington Works lines.
    This service contains the common methods used across the Washignton Works reporting lines.
    """

    @staticmethod
    def prepare_df(df: pd.DataFrame) -> None:
        set_column_as_index(df=df, col="index")
        df.sort_index(inplace=True)

    @staticmethod
    def get_lead_product_mdr(
        mdr: pd.DataFrame | None, lead_product: LeadProduct | None
    ) -> float:
        value: float = np.nan

        cant_proceed: bool = (
            (mdr is None) or (lead_product is None) or (lead_product.mdr is None)
        )

        if cant_proceed:
            return value

        ext_id = lead_product.mdr.external_id

        value = mdr.loc[mdr["externalId"] == ext_id, "unitAvgRate"].iloc[0]

        return value

    def extract_products_list_from_mdr(self, df: pd.DataFrame | None) -> set[str]:
        products: set[str] = set()

        if df is None:
            return products

        if "refMaterial" not in df.columns.to_list():
            log.info("refMaterial column not found in the dataset.")
            return products

        for value in df["refMaterial"]:
            if not isinstance(value, dict):
                log.info("Invalid data type in refMaterial.")
                continue

            description = value.get("description", None)

            if description is not None:
                products.add(description)

        return products

    def identify_not_running_with_transitions(
        self,
        reporting_line_ext_id: str,
        df: pd.DataFrame,
        not_running_start_fn: Callable,
        not_running_end_fn: Callable,
        products_pre_defined_set: set[str],
        product_transition: Optional[dict[str, dict[str, dict[str, int]]]],
        deadband_hours: int,
    ) -> pd.DataFrame:
        df_copy = df.copy()

        df_copy["NotRunningStart"] = not_running_start_fn(df_copy)
        df_copy["NotRunningEnd"] = not_running_end_fn(df_copy)
        df_copy["NotRunning"] = False

        self.prepare_df(df_copy)

        pair_start_end_indexes(
            df_copy, "NotRunningStart", "NotRunningEnd", "NotRunning", True
        )

        df_copy["FirstNotRunningStart"] = keep_only_first_occurrence(
            df=df_copy, col="NotRunningStart"
        )

        df_copy["ProductChange"] = (
            (df_copy["ProductDescription"].shift().isin(products_pre_defined_set))
            & (df_copy["ProductDescription"].isin(products_pre_defined_set))
            & (df_copy["ProductDescription"].shift() != df_copy["ProductDescription"])
            & (df_copy["NotRunning"])
        )

        df_copy["Transition"] = False
        deadband = pd.Timedelta(hours=deadband_hours)

        for transition_start in df_copy.index[df_copy["ProductChange"]]:
            before: pd.Series = df_copy.index < transition_start
            after: pd.Series = df_copy.index >= transition_start

            if not before.any() or not after.any():
                continue

            not_running_start: pd.Timestamp = (
                df_copy.loc[before, "FirstNotRunningStart"][::-1].eq(True).idxmax()
            )
            not_running_end: pd.Timestamp = df_copy.loc[after, "NotRunningEnd"].idxmax()

            if (transition_start - not_running_start) > deadband:
                continue

            from_product = df_copy.loc[before, "ProductDescription"].iloc[-1]
            to_product = df_copy.loc[after, "ProductDescription"].iloc[0]

            duration_in_seconds = (
                product_transition.get(reporting_line_ext_id, {})
                .get(from_product, {})
                .get(to_product, None)
            )

            if duration_in_seconds is None:
                duration = not_running_end - transition_start
            else:
                duration = pd.Timedelta(seconds=duration_in_seconds)

            transition_end = transition_start + duration

            if transition_end > not_running_end:
                transition_end = not_running_end

            transition_mask = (df_copy.index >= transition_start) & (
                df_copy.index <= transition_end
            )

            df_copy.loc[transition_mask, "Transition"] = True
            df_copy.loc[transition_mask, "NotRunning"] = False

        df_copy["event1a_start"] = (
            df_copy["NotRunning"].shift(fill_value=False) == False
        ) & (df_copy["NotRunning"] == True)

        df_copy["event1a_end"] = (df_copy["NotRunning"] == True) & (
            df_copy["NotRunning"].shift(periods=-1, fill_value=False) == False
        )

        df_copy["event1c_start"] = (
            df_copy["Transition"].shift(fill_value=False) == False
        ) & (df_copy["Transition"] == True)

        df_copy["event1c_end"] = (df_copy["Transition"] == True) & (
            df_copy["Transition"].shift(periods=-1, fill_value=False) == False
        )

        df_copy.drop(
            columns=[
                "NotRunningStart",
                "FirstNotRunningStart",
                "NotRunningEnd",
                "NotRunning",
                "ProductChange",
                "Transition",
            ],
            inplace=True,
        )

        df_copy.reset_index(inplace=True)

        return df_copy

    def identify_not_running_during_product_trial(
        self,
        df: pd.DataFrame,
        not_running_during_product_trial_start_fn: Callable[[pd.DataFrame], pd.Series],
        not_running_during_product_trial_end_fn: Callable[[pd.DataFrame], pd.Series],
    ) -> pd.DataFrame:
        """
        Identify Not Running During Product Trial and Not Running events (types IIc and IId).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each event type
        :rtype: pd.DataFrame
        """
        df_copy = df.copy()

        start_condition = not_running_during_product_trial_start_fn(df_copy)
        df_copy["StartCondition"] = (
            ~(start_condition.shift(fill_value=False)) & start_condition
        )

        end_condition = not_running_during_product_trial_end_fn(df_copy)
        df_copy["EndCondition"] = (
            ~(end_condition.shift(fill_value=False)) & end_condition
        )

        df_copy["ProductIsEqual"] = False
        df_copy["ProductIsDifferent"] = False

        self.prepare_df(df_copy)

        start_indexes = df_copy.index[df_copy["StartCondition"]].tolist()
        start_count = 0
        end_indexes = df_copy.index[df_copy["EndCondition"]].tolist()
        end_count = 0

        while start_count < len(start_indexes) and end_count < len(end_indexes):
            start_index = start_indexes[start_count]
            end_index = end_indexes[end_count]

            if start_index < end_index:
                start_product = df_copy.at[start_index, "ProductDescription"]
                end_product = df_copy.at[end_index, "ProductDescription"]

                df_copy.at[start_index, "ProductIsEqual"] = start_product == end_product
                df_copy.at[end_index, "ProductIsEqual"] = start_product == end_product

                df_copy.at[start_index, "ProductIsDifferent"] = (
                    start_product != end_product
                )
                df_copy.at[end_index, "ProductIsDifferent"] = (
                    start_product != end_product
                )

                start_count += 1
                end_count += 1
            else:
                end_count += 1

        if start_count < len(start_indexes):
            end_index = df_copy.index[-1]
            for i in range(start_count, len(start_indexes)):
                start_index = start_indexes[i]

                start_product = df_copy.at[start_index, "ProductDescription"]
                end_product = df_copy.at[end_index, "ProductDescription"]

                df_copy.at[start_index, "ProductIsEqual"] = start_product == end_product
                df_copy.at[end_index, "ProductIsEqual"] = start_product == end_product

                df_copy.at[start_index, "ProductIsDifferent"] = (
                    start_product != end_product
                )
                df_copy.at[end_index, "ProductIsDifferent"] = (
                    start_product != end_product
                )

        df_copy["event2c_start"] = df_copy["StartCondition"] & df_copy["ProductIsEqual"]
        df_copy["event2c_end"] = df_copy["EndCondition"] & df_copy["ProductIsEqual"]

        df_copy["event2d_start"] = (
            df_copy["StartCondition"] & df_copy["ProductIsDifferent"]
        )
        df_copy["event2d_end"] = df_copy["EndCondition"] & df_copy["ProductIsDifferent"]

        df_copy.drop(
            columns=[
                "StartCondition",
                "EndCondition",
                "ProductIsEqual",
                "ProductIsDifferent",
            ],
            inplace=True,
        )

        df_copy.reset_index(inplace=True)

        return df_copy

    def calculate_sum_of_scrap_equivalent_times(
        self,
        data: pd.DataFrame,
        producing_waste_tags: list[str],
        mdr: pd.DataFrame,
        lead_product: LeadProduct | None,
        freq: str,
        offset: pd.Timedelta,
    ) -> tuple[pd.Timestamp, float]:
        """
        Calculates the sum of scrap equivalent times.

        :param data: Time series data with the status of the line
        :type data: pd.DataFrame
        :param producing_waste_tags: Tags used to identify the producing waste
        :type data: list[str]
        :param mdr_df: MDR values
        :type data: pd.DataFrame
        :param reporting_line_ext_id: Reporting Line's external id
        :type data: str
        :param freq: Shift frequency used to group the data
        :type data: str
        :param offset: offset used to group the data (time of the first shift after 12am)
        :type data: pd.Timedelta
        :return: The timestamp of the first reported scrap alongside the sum of the total time tha must be discounted in seconds
        :rtype: tuple[pd.Timestamp, float]
        """
        data_copy = data.copy()

        data_copy.sort_values(by="index", ascending=True, inplace=True)

        data_copy["MaskToFilter"] = False

        agg_dict: dict[str, str] = {
            "index": "first",
            "leadMDR": "first",
        }

        for tag in producing_waste_tags:
            new_tag_col = tag + "Identification"
            data_copy[new_tag_col] = (data_copy[tag] > 0) & (
                data_copy[tag].shift(-1) <= 0
            )

            data_copy["MaskToFilter"] = (
                data_copy["MaskToFilter"] | data_copy[new_tag_col]
            )

            agg_dict[tag] = "sum"

        filtered = data_copy[data_copy["MaskToFilter"]]

        if filtered.empty:
            return (data_copy.iloc[0]["index"], 0)

        lead_mdr = self.get_lead_product_mdr(mdr, lead_product)

        filtered["leadMDR"] = lead_mdr

        filtered["GrouperKey"] = filtered["index"].dt.tz_convert(None)

        first_timestamp = data_copy.iloc[0]["index"]
        assert type(first_timestamp) is pd.Timestamp
        origin = first_timestamp.tz_localize(None).normalize()

        shift_data = filtered.groupby(
            by=pd.Grouper(
                key="GrouperKey",
                freq=freq,
                origin=origin,
                offset=offset,
            ),
            dropna=False,
        ).agg(agg_dict)

        shift_data["SumOfScrapEquivalentTimes"] = 0

        for tag in producing_waste_tags:
            shift_data["SumOfScrapEquivalentTimes"] = (
                shift_data["SumOfScrapEquivalentTimes"] + shift_data[tag]
            )

        shift_data["SumOfScrapEquivalentTimes"] = (
            shift_data["SumOfScrapEquivalentTimes"] / shift_data["leadMDR"]
        )

        shift_data.dropna(subset=["SumOfScrapEquivalentTimes"], inplace=True)

        first_index_label = shift_data.index[0]
        first_index = shift_data.loc[first_index_label, "index"]
        total_sum = (shift_data["SumOfScrapEquivalentTimes"].sum()) * 3600

        return (first_index, total_sum)

    @staticmethod
    def determines_available_time_slots(
        data: pd.DataFrame, triggers: dict[str, dict[str, Callable]]
    ) -> None:
        """
        Creates the Available column in the DataFrame.
        This column represents the points in time that does not fall under any of the provided triggers.

        :param data: Time series data with the status of the line
        :type data: pd.DataFrame
        :param triggers: Methods to determine in which condition the data is in.
        :type triggers: dict[str, dict[str, Callable]]
        """
        data["NotAvailable"] = False
        columns_to_drop: list[str] = ["NotAvailable"]

        for key, value in triggers.items():
            start_key = key + "Start"
            end_key = key + "End"

            start_fn: Callable | None = value.get("start")
            end_fn: Callable | None = value.get("end")

            if start_fn is None or end_fn is None:
                continue

            data[key] = False
            data[start_key] = start_fn(data)
            data[end_key] = end_fn(data)

            ed_utils.pair_start_end_indexes(data, start_key, end_key, key, True)

            data["NotAvailable"] = data["NotAvailable"] | data[key]

            columns_to_drop.extend([key, start_key, end_key])

        data["Available"] = ~data["NotAvailable"]

        data.drop(columns=columns_to_drop, inplace=True)

    def process_producing_waste_by_quality_control(
        self,
        df: pd.DataFrame,
        producing_waste_tags: list[str],
        mdr: pd.DataFrame,
        lead_product: LeadProduct | None,
        freq: str,
        offset: pd.Timedelta,
        triggers: dict[str, dict[str, Callable]],
        event_number_type: Literal["4b", "4c"] = "4b",
    ) -> pd.DataFrame:
        event_col = f"event{event_number_type}"
        event_col_start = f"event{event_number_type}_start"
        event_col_end = f"event{event_number_type}_end"

        default_return = df.copy()
        default_return[event_col_start] = False
        default_return[event_col_end] = False

        columns_to_drop = ["Available", "dt_shifted", event_col]

        first_index, sum_of_scrap_equivalent_times = (
            self.calculate_sum_of_scrap_equivalent_times(
                df,
                producing_waste_tags,
                mdr,
                lead_product,
                freq,
                offset,
            )
        )

        df = df[df["index"] >= first_index]

        df.sort_values(by="index", ascending=True, inplace=True)

        self.determines_available_time_slots(df, triggers)

        df["dt_shifted"] = df["dt"].shift(periods=-1, fill_value=0)

        df[event_col] = True

        filtered_data = df[df["Available"]]

        if filtered_data.empty:
            return default_return

        cum_sum = filtered_data["dt_shifted"].cumsum()

        cum_sum_mask = cum_sum >= sum_of_scrap_equivalent_times

        current_pos = filtered_data.index.get_loc(cum_sum_mask.idxmax())

        next_index_label = filtered_data.index[current_pos + 1]

        final_index = filtered_data.loc[next_index_label, "index"]

        sum_until_threshold = filtered_data.loc[
            filtered_data["index"] <= final_index, "dt_shifted"
        ].sum()

        difference = sum_until_threshold - sum_of_scrap_equivalent_times
        time_to_discount = pd.Timedelta(seconds=difference)
        new_index = final_index - time_to_discount
        new_row = {"index": new_index, event_col: True}
        new_df = pd.DataFrame([new_row])
        df = pd.concat([df, new_df], ignore_index=True)
        df.sort_values(by="index", inplace=True)
        df.ffill()
        df["dt"] = df["index"].diff().dt.total_seconds().fillna(0)

        df.loc[df["index"] > new_index, event_col] = False

        df[event_col] = df[event_col] & df["Available"]
        df[event_col_start] = (df[event_col] == True) & (
            df[event_col].shift(periods=1, fill_value=False) == False
        )
        df[event_col_end] = (df[event_col] == False) & (
            df[event_col].shift(periods=1, fill_value=False) == True
        )

        df.drop(columns=columns_to_drop, inplace=True)

        return df
