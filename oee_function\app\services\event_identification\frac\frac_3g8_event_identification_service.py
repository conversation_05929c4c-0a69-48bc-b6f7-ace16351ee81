from typing import Any

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import (
    RateLossTimeContinuous,
)
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value
from app.utils.constants import Constants as const
import app.utils.event_detection_utils as ed_utils
from app.utils.uom_conversion import kg_to_mt


class Frac3g8EventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._msdp = msdp
        self._not_running_start = pd.Series(dtype=bool)
        self._not_running_end = pd.Series(dtype=bool)
        self._starting_up_start = pd.Series(dtype=bool)
        self._starting_up_end = pd.Series(dtype=bool)
        self._shutting_down_start = pd.Series(dtype=bool)
        self._shutting_down_end = pd.Series(dtype=bool)
        self._net_production = pd.Series(dtype=float)
        self._running_time_for_rlt = pd.Series(dtype=bool)
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id, msdp=msdp
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            # ************************************************************************************************
            # CODE SUSPENSED:
            # User Story v2 209932: [FRA] [EUS] [3G8] Remove Shutting Down and Starintg Up Events
            # User Story v2 142829: Stop Events type 4 (Quality) in IPH 3G8
            # SUSPENDE EVENTS TYPE 4
            # Business Rule are flagged as false in the Event Hierarchy Configuration
            # ************************************************************************************************
            # "2a": self.identify_events_typeIIa,
            # "2b": self.identify_events_typeIIb,
            # "4a": self.identify_events_typeIVa,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies Not Running events (type Ia).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        if data.index.name is None:
            data.set_index("index", inplace=True)
        elif data.index.name != "index":
            data.reset_index(inplace=True)
            data.set_index("index", inplace=True)

        event1a_start = self.not_running_start_fn(data)
        consecutive_start = event1a_start.shift(1) & event1a_start
        event1a_start.loc[consecutive_start] = False

        event1a_end = self.not_running_end_fn(data)
        consecutive_end = event1a_end.shift(1) & event1a_end
        event1a_end.loc[consecutive_end] = False

        data = data.assign(event1a_start=event1a_start, event1a_end=event1a_end)
        data.reset_index(inplace=True)

        return data

    def identify_events_typeIIa(self, data: pd.DataFrame, **args):
        """
        Identifies Starting Up events (type IIa).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        if data.index.name is None:
            data.set_index("index", inplace=True)
        elif data.index.name != "index":
            data.reset_index(inplace=True)
            data.set_index("index", inplace=True)

        event2a_start = self.starting_up_start_fn(data)
        end_condition = self.starting_up_end_fn(data)
        end_and_not_start = end_condition & ~event2a_start
        event2a_end = (
            ~end_and_not_start.shift(periods=1, fill_value=False) & end_and_not_start
        )
        data = data.assign(event2a_start=event2a_start, event2a_end=event2a_end)
        data.reset_index(inplace=True)

        return data

    def identify_events_typeIIb(self, data: pd.DataFrame, **args):
        """
        Identifies Shutting Down events (type IIb).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        if data.index.name is None:
            data.set_index("index", inplace=True)
        elif data.index.name != "index":
            data.reset_index(inplace=True)
            data.set_index("index", inplace=True)

        event2b_start = self.shutting_down_start_fn(data)
        event2b_end = self.shutting_down_end_fn(data)
        data = data.assign(event2b_start=event2b_start, event2b_end=event2b_end)
        data.reset_index(inplace=True)

        return data

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies events of No Demand (types IIIb and IIIe).

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        if data.index.name is None:
            data.set_index("index", inplace=True)
        elif data.index.name != "index":
            data.reset_index(inplace=True)
            data.set_index("index", inplace=True)

        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_condition_for_rlt_events(data)
        data.reset_index(inplace=True)

        day_data = self._rlt.create_day_data(
            data, "sum", [const.MSDP, const.SCHEDULED_RATE], 0
        )

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]
        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]

        return day_data

    def identify_events_typeIIIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies Running Under Scheduled Rate events (type IIIa and IIId).

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        if data.index.name is None:
            data.set_index("index", inplace=True)
        elif data.index.name != "index":
            data.reset_index(inplace=True)
            data.set_index("index", inplace=True)

        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_condition_for_rlt_events(data)
        data.reset_index(inplace=True)

        day_data = self._rlt.create_day_data(
            data, "sum", [const.MSDP, const.SCHEDULED_RATE], 0
        )

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]
        day_data["total_duration_seconds"] = day_data[const.RLT]
        data.reset_index(inplace=True)

        return day_data

    def identify_events_typeIIIc(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies Running Above MDR events (type IIIc)

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        if data.index.name is None:
            data.set_index("index", inplace=True)
        elif data.index.name != "index":
            data.reset_index(inplace=True)
            data.set_index("index", inplace=True)

        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_condition_for_rlt_events(data)
        data.reset_index(inplace=True)

        day_data = self._rlt.create_day_data(
            data, "sum", [const.MSDP, const.SCHEDULED_RATE], 0
        )

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]
        day_data["total_duration_seconds"] = day_data[const.RLT]
        data.reset_index(inplace=True)

        return day_data

    def identify_events_typeIVa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies events of type IVa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_waste_data(data)

        data = data.assign(
            event4a_end=(
                (data["WasteTotalizerPI"] - data["WasteTotalizerPI"].shift(1) < 0)
                & (data["is_midnight"])
            )
        )

        data = data.assign(event4a_start=(data["event4a_end"].shift(1) == True))

        data = data.assign(total_duration_seconds=data["WasteProduced"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        return msdp_util_get_msdp_value(row, msdp_data, data_aux)

    def __create_day_waste_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support event 4a

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """
        TONNES_MULTIPLICATION_FACTOR = 0.00099

        data["WasteTotalizerPI"] = (
            data["WasteTotalizerPI"] * TONNES_MULTIPLICATION_FACTOR
        )

        data["WasteMass"] = (data["WasteTotalizerPI"] / 3600) * data["dt"]
        data.set_index("index", inplace=True)
        data["TotalWaste"] = data["WasteMass"].rolling("24h").sum()
        data.reset_index(inplace=True)

        data["is_midnight"] = (
            (data["index"].dt.hour == 0)
            & (data["index"].dt.minute == 0)
            & (data["index"].dt.second == 0)
            & (data["index"].dt.microsecond == 0)
        )

        if data["is_midnight"].any():
            data = data[data["is_midnight"]]

        # find the year that is in accordance with the indexed date
        data["Year"] = data["index"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["index"].dt.month
        data["Day"] = data["index"].dt.day

        filter_msdp = (
            self._msdp["reportingLineExternalId"] == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_data["msdp"] = msdp_data.apply(
            lambda x: x["scheduledRate"] if x["scheduledRate"] > 0 else x["msdp"],
            axis=1,
        )

        data["MSDP"] = data.apply(
            self.get_msdp_value, msdp_data=msdp_data, data_aux="msdp", axis=1
        )

        data["WasteProduced"] = (data["TotalWaste"]) / (data["MSDP"] / 24)

        return data

    def not_running_start_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._not_running_start.empty:
            return self._not_running_start.copy()

        # Start Trigger
        # Production Line Status < 50 AND (Rate Totalizer PI 1 < 10 OR Rate Totalizer PI 2 < 10)
        not_running_start = (df["ProductionLineStatus"] < 50) & (
            (df["RateTotalizerPI1"] < 10) | (df["RateTotalizerPI2"] < 10)
        )

        self._not_running_start = not_running_start

        return not_running_start.copy()

    def not_running_end_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._not_running_end.empty:
            return self._not_running_end.copy()

        # End Trigger
        # Production Line Status > 50
        not_running_end = df["ProductionLineStatus"] > 50

        self._not_running_end = not_running_end

        return not_running_end.copy()

    def starting_up_start_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._starting_up_start.empty:
            return self._starting_up_start

        not_running_start = self.not_running_start_fn(df)

        # Start Trigger
        # Production Line Status goes from less than 50 to greater than 50 and
        # Rate Totalizer PI 1 > 10 or Rate Totalizer PI 2 > 10
        starting_up_start = (
            (df["ProductionLineStatus"].shift(1) < 50)
            & (df["ProductionLineStatus"] > 50)
            & ((df["RateTotalizerPI1"] > 10) | (df["RateTotalizerPI2"] > 10))
            & ~not_running_start
        )

        self._starting_up_start = starting_up_start

        return starting_up_start

    def starting_up_end_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._starting_up_end.empty:
            return self._starting_up_end

        not_running_start = self.not_running_start_fn(df)

        # End Trigger
        # Rate Totalizer PI 1 > 20 or Rate Totalizer PI 2 > 20
        starting_up_end = (
            (df["RateTotalizerPI1"] > 20)
            | (df["RateTotalizerPI2"] > 20)
            | not_running_start
        )

        self._starting_up_end = starting_up_end

        return starting_up_end

    def shutting_down_start_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._shutting_down_start.empty:
            return self._shutting_down_start

        not_running_start = self.not_running_start_fn(df)

        # Start Trigger
        # Production Line Status goes from greater than 50 to less than 20
        shutting_down_start = (
            (df["ProductionLineStatus"].shift(1) > 50)
            & (df["ProductionLineStatus"] < 20)
            & (~not_running_start)
        )

        self._shutting_down_start = shutting_down_start

        return shutting_down_start

    def shutting_down_end_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._shutting_down_end.empty:
            return self._shutting_down_end

        not_running_start = self.not_running_start_fn(df)

        # End Trigger
        # # Rate Totalizer PI 1 < 10 or Rate Totalizer PI 2 < 10
        shutting_down_end = (
            (df["RateTotalizerPI1"] < 10)
            | (df["RateTotalizerPI2"] < 10)
            | not_running_start
        )

        self._shutting_down_end = shutting_down_end

        return shutting_down_end

    def calculate_net_production_fn(self, df: pd.DataFrame) -> "pd.Series[float]":
        if not self._net_production.empty:
            return self._net_production

        rate_totalizer = kg_to_mt((df["RateTotalizerPI1"] + df["RateTotalizerPI2"]))

        net_production = (rate_totalizer / 3600) * df["dt"]

        self._net_production = net_production

        return net_production

    def not_running_condition_for_rlt_events(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._running_time_for_rlt.empty:
            return self._running_time_for_rlt

        df_work = df.copy()

        df_work["not_running_start"] = self.not_running_start_fn(df_work)
        df_work["not_running_end"] = self.not_running_end_fn(df_work)

        # ***************************CODE SUSPENSED***************************
        # df_work["starting_up_start"] = self.starting_up_start_fn(df_work)
        # df_work["starting_up_end"] = self.starting_up_end_fn(df_work)
        # df_work["starting_up_end"] = (
        #     df_work["starting_up_end"] & ~df_work["starting_up_start"]
        # )
        # df_work["shutting_down_start"] = self.shutting_down_start_fn(df_work)
        # df_work["shutting_down_end"] = self.shutting_down_end_fn(df_work)

        df_work = self.__process_event_conditions(df_work)

        running_time_for_rlt = df_work["not_running"]

        # ********************************CODE SUSPENSED********************************
        # running_time_for_rlt = (
        #     df_work["not_running"] | df_work["starting_up"] | df_work["shutting_down"]
        # )

        self._running_time_for_rlt = running_time_for_rlt

        return running_time_for_rlt

    def __process_event_conditions(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Processes event conditions for 'not_running'.

        :param df: DataFrame containing the data
        """
        # Initialize columns
        df["not_running"] = False

        # *******CODE SUSPENSED*******
        # df["starting_up"] = False
        # df["shutting_down"] = False

        ed_utils.pair_start_end_indexes(
            df, "not_running_start", "not_running_end", "not_running", True
        )

        # ******************************CODE SUSPENSED******************************
        # ed_utils.pair_start_end_indexes(
        #     df, "starting_up_start", "starting_up_end", "starting_up", True
        # )
        # ed_utils.pair_start_end_indexes(
        #     df, "shutting_down_start", "shutting_down_end", "shutting_down", True
        # )

        return df
