import pandas as pd


class WasEpceProductMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            # PCCP6
            "408 BK010": "RYN408 NC010 (US)",
            "408HS BK009": "ZYT408HS BK009 (US)",
            "408HS BK010": "ZYT408HS NC010 (US)",
            "444AHS BK152": "ZYT444AHS BK152 (US)",
            "450HSL BK152": "ZYT450HSL BK152 (CN)",
            "FE4162HSL BK152": "ZYTFE4162HSL BK152 (US)",
            "FE4200 BK136": "ZYTFE4200 BK136 (US)",
            "FE8217HS BK010": "ZYTFE8217HS BK010 (US)",
            "MT409AHS BK010": "ZYTMT409AHS BK010 (US)",
            "ST800HSL BK152": "ZYTST800HSL BK152 (US)",
            "ST801 BK010": "ZYTST801 BK010 (US)",
            "ST801AHS BK010": "ZYTST801AHS BK010 (US)",
            "ST801AW BK195": "ZYTST801AW BK195 (US)",
            "ST801HS BK010": "ZYTST801HS BK010 (US)",
            "ST801W BK195": "ZZDELZYTST801W NC010 (US)",

            # PCCP7
            "70G33HS1L BK031": "ZYT70G33HS1L BK031 (US)",
            "CRACE2065 BK507": "CRACE2065 BK507 (US)",
            "CRACE2560 BK503": "CRACE2560 BK503 (US)",
            "CRAHR5315HF BK503": "CRAHR5315HF BK503 (US)",
            "CRAHR5330HF BK503": "CRAHR5330HF BK503 (US)",
            "RYN408 BK515": "RYN408 BK515 (US)",
            "RYN415HP BK503": "RYN415HP BK503 (US)",
            "RYN530 BK503": "RYN530 BK503 (US)",
            "RYN545 BK504": "RYN545 BK504 (US)",
            "RYNFR515 BK507": "RYNFR515 BK507 (US)",
            "RYNFR530 BK507": "RYNFR530 BK507 (US)",
            "RYNRE5253 BK504": "RYNRE5253 BK504 (US)",
            "FR50 BK505": "ZYTFR50 BK505 (US)",
            "70G50HSLR BK509": "ZYT70G50HSLR BK509 (US)",
            "INDFE370073 BK001": "INDFE370073 BK001 (US)",

            # PCCP8
            "70G43L BK031": "ZYT70G43L BK031 (US)",
            "70G30HSLR BK099": "ZYT70G30HSLR BK099 (US)",
            "70G35HSLX BK357": "ZYT70G35HSLX BK357 (US)",
            "80G43HS1L BK104": "ZYT80G43HS1L BK104 (US)",
            "77G33EFT BK276": "ZYT77G33EFT BK276 (US)",
            "70G13HS1L BK031": "ZYT70G13HS1L BK031 (US)",
            "70G25HSLR BK099": "ZYT70G25HSLR BK099 (US)",
            "70G33HS1L BK031R": "ZYT70G33HS1L BK031R (US)",
            "70G33L BK031": "ZYT70G33L BK031 (US)",
            "70G35EF BK538": "ZYT70G35EF BK538 (US)",
            "70G35HSLR BK267": "ZYT70G35HSLRA4 BK267 (US)",
            "70G43HSLA BK099": "ZYT70G43HSLA BK099 (US)",
            "71G13L BK031": "ZYT71G13L BK031 (US)",
            "77G33HS1L BK031": "ZYT77G33HS1L BK031 (US)",
            "77G33L BK031": "ZYT77G33L BK031 (US)",
            "77G43L BK031": "ZYT77G43L BK031 (US)",
            "80G14AHS BK099": "ZYT80G14AHS BK099 (US)",
            "80G33HS1L BK104": "ZYT80G33HS1L BK104 (US)",
            "FE5105 BK083": "ZYTFE5105 BK083 (US)",
            "FE5117 BK077": "PBLAF ZFE5117 BK077 KG BIN (US)",
            "FE5312 BK032": "ZYTFE5312 BK032 (US)",
            "FE5382 BK276": "ZYTFE5382 BK276 (US)",
            "FE5407 BK083J": "ZYTFE5407 BK083J (I1)",
            "FE5420 BK031": "ZYTFE5420 BK031 (US)",
            "FE5422 BK275": "ZYTFE5422 BK275 (US)",
            "FE5480HS BK032N": "ZYTFE5480HS BK032N (US)",
            "IND51HSL BK001": "IND51HSL BK001 (US)",
            "RC70G35HS BK382": "ZYTRC70G35HSL BK382 (US)",
            "RS78G33FH BK083": "ZYTRS78G33FHS BK083 (US)",

            # PCCP9 (itens novos)
            "73G15HSL BK363": "ZYT73G15HSL BK363 (US)",
            "73G30HSL BK416": "ZYT73G30HSL BK416 (US)",
            "73G35HSLA BK416LM": "ZYT73G35HSLA BK416LM (US)",
            "73G35THS BK511": "ZYT73G35THS BK511 EP (US)",
            "FE5510 BK512J": "ZYTFE5510 BK512J (US)",
            "73G30T BK261": "ZYT73G30T BK261 (US)",
            "73G40HSL BK416LM": "ZYT73G40HSL BK416LM (US)",
            "73G40T BK416": "ZYT73G40T BK416 (US)",
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})
