from datetime import time
from typing import Any, Optional

import pandas as pd
import numpy as np

from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.variable_categories_mapping.was.was_fill_variable_mapping_service import WASFillVariableMappingService


class WASFILSettings:
    """
    Settings for WASFIL event identification.
    """

    def __init__(self, reporting_line_external_id: str):
        self.reporting_line_external_id = reporting_line_external_id
        self._var_mapping = WASFillVariableMappingService

    def not_running(self, data: pd.DataFrame) -> pd.DataFrame:
        
        thresh_speed = self._var_mapping.get(self.reporting_line_external_id, "ExtruderScrewSpeedActual")
        thresh_load = self._var_mapping.get(self.reporting_line_external_id, "ExtruderActualLoad")

        data = data.assign(
            event1a_start=(
                (data["ExtruderScrewSpeedActual"] < thresh_speed) |
                (data["ExtruderActualLoad"] < thresh_load)
            ),
            event1a_end=(
                (data["ExtruderScrewSpeedActual"] > thresh_speed) &
                (data["ExtruderActualLoad"] > thresh_load)
            )
        )

        starts = data["event1a_start"].astype(int)
        ends_shift = data["event1a_end"].shift(1, fill_value=False).astype(int)
        delta = starts - ends_shift

        data["isNotRunning"] = np.where(delta.cumsum() > 0, True, False)

        return data


    def producing_waste(self, data: pd.DataFrame) -> pd.DataFrame:
        
        # thresholds
        thresh_clutch = self._var_mapping.get(
            self.reporting_line_external_id, "ClutchDrawRatio"
        )
        thresh_speed = self._var_mapping.get(
            self.reporting_line_external_id, "ExtruderScrewSpeedActual"
        )
        thresh_load = self._var_mapping.get(
            self.reporting_line_external_id, "ExtruderActualLoad"
        )

        cond_extruder_bad = (
            (data["ExtruderScrewSpeedActual"] < thresh_speed)
            | (data["ExtruderActualLoad"] < thresh_load)
        )

        cond_clutch_low = data["ClutchDrawRatio"] < thresh_clutch

        cond_start = cond_clutch_low ^ cond_extruder_bad

        cond_end = (
            (data["ClutchDrawRatio"] > thresh_clutch)
            | cond_extruder_bad
        )

        data = data.assign(
            event4a_start=cond_start & ~cond_start.shift(fill_value=False),
            event4a_end=cond_end & ~cond_end.shift(fill_value=False),
        )
        
      
        starts = data["event4a_start"].astype(int)
        ends_shift = data["event4a_end"].shift(1, fill_value=False).astype(int)
        delta = starts - ends_shift

        data["isProducingWaste"] = np.where(delta.cumsum() > 0, True, False)

        return data

    def prepare_hourly_data(
        self, data: pd.DataFrame
    ) -> pd.DataFrame:
        data = self.not_running(data)
        data = self.producing_waste(data)
        data["isRunning"] = (~data["isNotRunning"]) & (~data["isProducingWaste"])

        return data

    def create_total_feed_column(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Creates a total feed column based on the reporting line external ID.
        """
                
        conversion_factor = self._var_mapping.get(
            self.reporting_line_external_id, "conversionFactor"
        )
        
        data["TotalFeed"] = (
            data["FastRollSpeedActual(fpm)"]
            * conversion_factor
        )
        
        return data


class WASFILEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
    ) -> None:
        self._mdr = mdr
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._var_mapping = WASFillVariableMappingService
        self._day_data = None
        self._service_settings = WASFILSettings(reporting_line_external_id)

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
       
        data = self._service_settings.not_running(
            data, self._reporting_line_external_id
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass
    
    def identify_events_typeIIb(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIIc(self):
        pass

    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
    
        data = self._service_settings.producing_waste(
            data, self._reporting_line_external_id
        )
        return data



    def identify_events_typeIVb(self):
        pass