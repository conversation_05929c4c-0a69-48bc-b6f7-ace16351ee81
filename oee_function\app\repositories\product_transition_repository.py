from typing import Any, Optional
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.data_models import DataModelId

from .view_repository import ViewRepository
from ..models.product_transition import ProductTransition
from ..utils.graphql import generate_query, query_all


class ProductTransitionRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        data_model_id: DataModelId,
        view_repository: Optional[ViewRepository] = None,
    ) -> None:
        self._cognite_client = cognite_client
        self._data_model_id = data_model_id
        self._view_repository = view_repository
        self._list_name = "listOEEProductTransition"

    def get(self, filter: dict[str, Any] | None = None) -> list[ProductTransition]:
        """
        Returns a list of product transitions according to the given filter.

        :param: desired filter to query product transitions.
        :return: list of Product Transitions
        :rtype: list[ProductTransitions]
        """

        selected_items = """
            externalId
            space
            refReportingLine {
                externalId
                space
                name
            }
            fromProduct {
                externalId
                space
                name
                description
            }
            toProduct {
                externalId
                space
                name
                description
            }
            duration
        """

        query_result = query_all(
            client=self._cognite_client,
            data_model_id=self._data_model_id,
            list_name=self._list_name,
            query=generate_query(self._list_name, selected_items),
            filter=filter,
        )

        return [
            ProductTransition.from_cognite_response(result) for result in query_result
        ]

    def get_by_reporting_lines_as_dict(
        self, reporting_lines: list[str]
    ) -> dict[str, dict[str, dict[str, int]]]:
        """
        Retrieves from cognite the product transition of the given lines.

        :param reporting_lines: list with the reporting lines external ids.
        :type reporting_lines: list[str]
        :return: list of product transitions of the given lines.
        :rtype: list[ProductTransition]
        """
        transition_dict: dict[str, dict[str, dict[str, int]]] = {}
        query_filter = {"refReportingLine": {"externalId": {"in": reporting_lines}}}
        transitions = self.get(query_filter)

        for transition in transitions:
            if (
                transition_dict.get(transition.ref_reporting_line.external_id, None)
                is not None
            ):
                if (
                    transition_dict.get(
                        transition.ref_reporting_line.external_id, {}
                    ).get(transition.from_product.description, None)
                    is not None
                ):
                    if (
                        transition_dict.get(
                            transition.ref_reporting_line.external_id, {}
                        )
                        .get(transition.from_product.description, {})
                        .get(transition.to_product.description, None)
                        is not None
                    ):
                        transition_dict[transition.ref_reporting_line.external_id][
                            transition.from_product.description
                        ].update(
                            {transition.to_product.description: transition.duration}
                        )
                    else:
                        transition_dict[transition.ref_reporting_line.external_id][
                            transition.from_product.description
                        ][transition.to_product.description] = transition.duration
                else:
                    transition_dict[transition.ref_reporting_line.external_id].update(
                        {
                            transition.from_product.description: {
                                transition.to_product.description: transition.duration
                            }
                        }
                    )
            else:
                transition_dict[transition.ref_reporting_line.external_id] = {
                    transition.from_product.description: {
                        transition.to_product.description: transition.duration
                    }
                }

        return transition_dict

    def create_product_transition(
        self, product_transitions: list[ProductTransition]
    ) -> None:
        """
        Creates product transitions in CDF.

        :param product_transitions: list of ProductTransition to be created.
        :type product_transitions: list[ProductTransition]
        """
        if not product_transitions:
            return
        
        if not self._view_repository:
            raise ValueError(
                "Missing View Repository when instantiating the Product Transition Repository."
            )
            
        view = self._view_repository.get_view_id("OEEProductTransition")
        
        nodes = []
        for product_transition in product_transitions:
            nodes.append(
                product_transition.convert_to_cognite_node(view)
            )
            
        paginated_nodes = [
            nodes[1000 * i : 1000 * (i + 1)]
            for i in range(int(len(nodes) / 1000) + 1)
        ]

        for entries in paginated_nodes:
            self._cognite_client.data_modeling.instances.apply(nodes=entries)