from typing import Any, List, Optional

import pandas as pd
from cognite.client import Cognite<PERSON>lient
from cognite.client.data_classes.data_modeling import NodeId
from cognite.client.data_classes.data_modeling.data_models import DataModelId

from .view_repository import ViewRepository

class EventHierarchyConfigurationRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        data_model_id: DataModelId,
        view_repository: Optional[ViewRepository] = None,
    ) -> None:
        self._cognite_client = cognite_client
        self._data_model_id = data_model_id
        self._view_repository = view_repository

    def list_all(self, query: str, variables: dict[str, Any] | None = None) -> List[dict]:
        all_results = []
        cursor = None
        variables = variables or {}

        while True:
            variables["after"] = cursor

            query_result = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                query,
                variables,
            )["listOEEEventHierarchyConfiguration"]

            items = query_result["items"]
            all_results.extend(
                {
                    "externalId": entry["externalId"],
                    "space": entry["space"],
                    "eventHierarchy": entry.get("eventHierarchy"),
                    "eventDefinition": entry.get("eventDefinition"),
                    "reportingLine": entry.get("reportingLine", {}).get("externalId") if entry.get("reportingLine") else None,
                }
                for entry in items
                if entry.get("externalId") and entry.get("space")
            )

            page_info = query_result["pageInfo"]
            if not page_info["hasNextPage"]:
                break

            cursor = page_info["endCursor"]

        return all_results