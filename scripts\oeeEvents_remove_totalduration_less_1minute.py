from datetime import datetime
import time
import logging
import os
import sys
import pandas as pd
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables
from cognite.client.data_classes.data_modeling import NodeId

"""
    ATENÇÃO: Este script deverá excluir da base todos os eventos com durações menores que 60 segundos.
"""

# Definindo o caminho do script para encontrar os pacotes corretamente
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from oee_function.app.repositories.OEEEvent_repository import OEEEventRepository
from oee_function.app.infra.logger_adapter import get_logger
from oee_function.app.repositories.process_repository import ProcessRepository
from oee_function.app.repositories.view_repository import ViewRepository

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def require_current_day():
    """
    Solicita ao usuário que insira o dia atual para liberar a execução do script.
    """
    today = datetime.now().strftime("%d")
    print("\nAtenção: Este processo é irreversivel para continuar, digite o dia atual (somente número): ")
    
    user_input = input("Dia atual: ").strip()
    
    if user_input != today:
        logger.error("Dia inserido incorreto. Execução abortada.")
        sys.exit(1)  # Encerra o programa com código de erro

    logger.info("Dia confirmado corretamente. Continuando execução.")

def create_cognite_client(variables: EnvVariables):
    """Cria uma instância do cliente Cognite usando as variáveis de ambiente."""
    return CogniteClientFactory.create(variables)

def create_repositories(variables: EnvVariables):
    """Inicializa os repositórios necessários para acessar os dados."""
    cognite_client = create_cognite_client(variables)
    data_model_id = DataModelId(
        variables.cognite.data_model_space,
        variables.cognite.data_model_external_id,
        variables.cognite.data_model_version,
    )
    return {
        "oee_event": OEEEventRepository(
            cognite_client,
            data_model_id,
            view_repository=ViewRepository(cognite_client, data_model_id),
        ),
    }


def get_all_oee_event_data(variables: EnvVariables) -> pd.DataFrame:
    """Executa a query para buscar todos os dados de eventos OEE e retorna como DataFrame."""
    repositories = create_repositories(variables)
    oee_event_repository = repositories.get("oee_event")
    if oee_event_repository is None:
        logger.error("OEEEventRepository não encontrado.")
        return pd.DataFrame()

    data = oee_event_repository.query_to_dataframe(query=query_total_duration_less_than_1minute(), variables={"after": None})
    return data


def query_total_duration_less_than_1minute():
    return """
        query MyQuery( $after: String) {
          listOEEEvent(
            first: 1000
            filter: {
              and: [
                { totalDuration: { lte: 60 } },
                { totalDuration: { gte: -60 } },
              ]
            }
            after: $after
          ) {
            items {
              externalId
              space
              totalDuration
            }
            pageInfo {
              endCursor
              hasNextPage
            }
          }
        }
        """

def delete_oee_events_in_batch(variables: EnvVariables, oee_event_data: pd.DataFrame, batch_size: int = 100) -> None:
    """Exclui eventos OEE listados em oee_event_data em lotes."""
    if oee_event_data.empty:
        logger.info("Nenhum evento para deletar.")
        return

    repositories = create_repositories(variables)
    oee_event_repository = repositories.get("oee_event")

    # Prepara a lista de NodeIds
    nodes_to_delete = [
        NodeId(external_id=row["externalId"], space=row["space"])
        for _, row in oee_event_data.iterrows()
    ]

    try:
        # Divide a lista em lotes
        for i in range(0, len(nodes_to_delete), batch_size):
            batch = nodes_to_delete[i:i + batch_size]
            oee_event_repository._cognite_client.data_modeling.instances.delete(nodes=batch)
            logger.info(f"Lote de {len(batch)} eventos excluído com sucesso.")
    except Exception as e:
        logger.error(f"Erro ao excluir eventos em lote: {e}")


def delete_oee_events(variables: EnvVariables, oee_event_data: pd.DataFrame) -> None:
    """Exclui eventos OEE listados em oee_event_data."""
    if oee_event_data.empty:
        logger.info("Nenhum evento para deletar.")
        return

    repositories = create_repositories(variables)
    oee_event_repository = repositories.get("oee_event")

    for _, row in oee_event_data.iterrows():
        external_id = row["externalId"]
        space = row["space"]
        try:
            oee_event_repository.delete_event(external_id, space)
            logger.info(f"Evento com externalId '{external_id}' e space '{space}' deletado com sucesso. ({_})")
        except Exception as e:
            logger.error(f"Erro ao deletar o evento com externalId '{external_id}' e space '{space}': {e}")


def run():
    """
    ATENÇÃO: Este script deverá excluir da base todos os eventos com durações menores que 60 segundos.
    """
    
    require_current_day()
    
    variables = EnvVariables()    
    log = get_logger()
    
    log.info("Coletando eventos com menos de 1 minuto de duraçao...")
    oee_events = get_all_oee_event_data(variables)
    
    if oee_events.empty:
        logger.info("Nenhum dado encontrado para os parâmetros fornecidos.")
    else:
        logger.info(f"Dados de eventos OEE encontrados:\n{oee_events}")

        # Chama a função para deletar os eventos encontrados
        log.info("Iniciando exclusão...")

        delete_oee_events_in_batch(variables, oee_events, batch_size=100)
        
    logger.info("Script concluído com sucesso. Eventos com menos de 1 minuto excluidos com sucesso...")


if __name__ == "__main__":
    run()
