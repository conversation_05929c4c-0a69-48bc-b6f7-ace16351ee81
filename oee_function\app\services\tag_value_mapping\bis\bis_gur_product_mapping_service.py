import pandas as pd


class BisGurProductMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            1: "GUR 4120 - NATURAL (B)",
            2: "GUR 4120 A - NATURAL (B)",
            3: "GUR 4130 C - NATURAL (B)",
            4: "GUR 4130 - NATURAL (B)",
            5: "GUR 4130 B - NATURAL (B)",
            6: "GUR 4130 Q - NATURAL (B)",
            7: "GUR 4150 - NATURAL (B)",
            8: "GUR 4150 A - NATURAL (B)",
            9: "GUR 4150 O - NATURAL (B)",
            10: "ZZDEL GUR 4150 R - NATURAL (B)",
            11: "ZZDEL GUR 4150 B V",
            12: "GUR 4170 NATURAL (B)",
            13: "AB150",
            14: "GUR 4152 - NATURAL (B)",
            15: "GUR 4152 Y - NATURAL (B)",
            16: "zzDEL GUR X 156 B",
            17: "GHR 8110 - NATURAL (B)",
            18: "zzDEL GUR X 189 B",
            19: "GUR DEV X 198 - NATURAL (B)",
            20: "ZZDEL GUR 4150-5 - NATURAL (B)",
            21: "zzDEL GUR X 168 O",
            22: "GUR DEV X 192 - NATURAL (B)",
            23: "GUR 4150 C - NATURAL (B)",
            24: "4022",
            25: "GUR DEV 4022 NATURAL (B)",
            26: "GUR DEV 4022 S NATURAL (B)",
            27: "4032",
            28: "GUR DEV 4032 - NATURAL (B)",
            29: "GUR DEV 4130 A NATURAL B",
            30: "GUR DEV 4530 CR (B) NAT",
            31: "GUR 4130 CR NATURAL (B)",
            32: "Trial 6",
            33: "Trial 12",
            34: "Trial Max"
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})
