from typing import Any

import pandas as pd

from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.hourly_data_compounding import (
    HourlyDataCompounding,
)


class WinCompoundingFirstEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
    ) -> None:
        super().__init__()
        if mdr is not None:
            self._valid_prod_ids = list(mdr.productId.unique())
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # 01870_STOP_ID =  ProductionLineStatus (0 = Running, 1 = Not Running)
        # Line 1.Production Status = ProductTrial
        # event trigger start - 01870_STOP_ID != "Running" AND Line 1.Production Status = "Production"
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] != 0)
                & (data["ProductionLineStatus"].shift(1) == 0)
                & (data["ProductTrial"] == "Production")
            )
        )

        # event trigger end   -01870_STOP_ID = "Running" or Line 1.Production Status = "Trial" or time = 6pm or time = 6am or time = 12am 
        data = data.assign(
            event1a_end=(
                (
                    (data["ProductionLineStatus"] == 0)
                    & (data["ProductionLineStatus"].shift(1) != 0)
                )
                | (
                    (data["ProductTrial"] == "Trial")
                )
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIa event
        :rtype: pd.DataFrame
        """

        # 01870_STOP_ID =  ProductionLineStatus (0 = Running, 1 = Not Running)
        # Line 1.Production Status = ProductTrial
        # event trigger start - 01870_STOP_ID = "Running" AND Line 1.Production Status =  "Trial"
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus"] == 0)
                & (data["ProductTrial"] == "Trial")
            )
        )

        # event trigger end   - 01870_STOP_ID != "Running" or Line 1.Production Status =  "Production" or time = 6pm or time = 6am or time = 12am
        data = data.assign(
            event2a_end=(
                (
                    (data["ProductionLineStatus"] != 0)
                    | (data["ProductTrial"] == "Production")
                )
            )
        )


        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIc event
        :rtype: pd.DataFrame
        """

        # 01870_STOP_ID =  ProductionLineStatus (0 = Running, 1 = Not Running)
        # Line 1.Production Status = ProductTrial
        # event trigger start -01870_STOP_ID != "Running" AND Line 1.Production Status =  "Trial"
        data = data.assign(
            event2c_start=(
                (data["ProductionLineStatus"] != 0)
                & (data["ProductTrial"] == "Trial")
            )
        )

        # event trigger end - 
        # (01870_STOP_ID = "Running" AND Product Description == Product Description at start of the Event) 
        # or (01870_STOP_ID = "Running" AND Product Description != Product Description at start of the Event) or Line 1.Production Status =  "Production"
        data = data.assign(
            event2c_end=(
                ((data["ProductionLineStatus"] == 0 ) & (data["ProductionLineStatus"].shift(1) != 0))
                | ((data["ProductTrial"] == "Production") & (data["ProductTrial"].shift(1) != "Production"))
            )
        )


        # filter only true booleans to get the correct events
        data = data[
            (data["event2c_start"] == True) | (data["event2c_end"] == True)
        ]
        
        data = data.assign(
            event2c_end=(
                (data["event2c_start"].shift(1) == True)
            )
        )

        data = data[
            (data["event2c_start"] == True) | (data["event2c_end"] == True)
        ]

        data = data.assign(
            event2c_start=(
                (data["event2c_start"] == True)
                & (data["ProductDescription"] == data["ProductDescription"].shift(-1))
            )
        )

        data = data.assign(
            event2c_end=(
                (data["event2c_end"] == True)
                & (data["ProductDescription"] == data["ProductDescription"].shift(1))
            )
        )


        return data

    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IId

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IId event
        :rtype: pd.DataFrame
        """

        # 01870_STOP_ID =  ProductionLineStatus (0 = Running, 1 = Not Running)
        # Line 1.Production Status = ProductTrial
        # event trigger start -01870_STOP_ID != "Running" AND Line 1.Production Status =  "Trial"
        data = data.assign(
            event2d_start=(
                (data["ProductionLineStatus"] != 0)
                & (data["ProductTrial"] == "Trial")
            )
        )

        # event trigger end - 
        # (01870_STOP_ID = "Running" AND Product Description == Product Description at start of the Event) 
        # or (01870_STOP_ID = "Running" AND Product Description != Product Description at start of the Event) or Line 1.Production Status =  "Production"
        data = data.assign(
            event2d_end=(
                ((data["ProductionLineStatus"] == 0 ) & (data["ProductionLineStatus"].shift(1) != 0))
                | ((data["ProductTrial"] == "Production") & (data["ProductTrial"].shift(1) != "Production"))
            )
        )


        # filter only true booleans to get the correct events
        data = data[
            (data["event2d_start"] == True) | (data["event2d_end"] == True)
        ]
        
        data = data.assign(
            event2c_end=(
                (data["event2d_start"].shift(1) == True)
            )
        )

        data = data[
            (data["event2d_start"] == True) | (data["event2d_end"] == True)
        ]

        data = data.assign(
            event2c_start=(
                (data["event2d_start"] == True)
                & (data["ProductDescription"] == data["ProductDescription"].shift(-1))
            )
        )

        data = data.assign(
            event2c_end=(
                (data["event2d_end"] == True)
                & (data["ProductDescription"] == data["ProductDescription"].shift(1))
            )
        )


        return data


    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self):
        pass

    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IVa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IVa event
        :rtype: pd.DataFrame
        """

        if "ProducingWaste" not in data or data["ProducingWaste"].empty:
            data["event4a_start"] = False
            data["event4a_end"] = False
            return data

        # 01870_STOP_ID =  ProductionLineStatus (0 = Running, 1 = Not Running)
        # Line 1.Production Status = ProductTrial
        # event trigger start -01870_STOP_ID = "Running" AND Line 1.Splice Output > 10
        data = data.assign(
            event4a_start=(
                (data["ProductionLineStatus"] == 0)
                & (data["ProducingWaste"] > 10)
            )
        )

        # event trigger end - 
        # 01870_STOP_ID = "Running" AND Line 1.Splice Output less than or equal to 10

        data = data.assign(
            event4a_end=(
                (
                    (data["ProductionLineStatus"] != 0)
                    & (data["ProductionLineStatus"].shift(1) == 0)
                )
                & (data["ProducingWaste"] <= 10)
            )
        )

        return data

    def identify_events_typeIVb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IVb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IVb event
        :rtype: pd.DataFrame
        """
        pass
