import math
from datetime import datetime, timedelta
from typing import Union, List, Optional

import numpy as np
import pandas as pd

from app.models.lead_product import LeadProductBbct, LeadProduct
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.repositories.lead_product_repository import LeadProductRepository
from app.services.event_identification.hourly_data_compounding import (
    HourlyDataCompounding,
)
from app.utils.constants import EventFrameConstants as const
from app.utils.msdp_utils import get_msdp_product_filter, get_msdp_value as msdp_util_get_msdp_value
from app.utils.rlt_utils import RLT_DEFINITIONS
from app.utils.uom_conversion import HOURS_TO_SECONDS_FACTOR, kg_to_mt, lb_to_mt, mt_to_lb


class EventFixingService():
    # Used to delete columns with suffixes after product and events data merge
    COLUMN_PRODUCT = [
        "NetProduction",
        "TotalLoss",
        "TotalFeed",
        "production",
        "total_produced",
        "TotalNetProduction",
        "total_gur_produced",
        "VAMProduced",
        "net_production",
        "material_produced",
        "msdp",
        "MSDP",
        "ScheduledRate",
        "ScheduleRate",
        "scheduledRate",
        "SCHR",
        "ScheduledRateYield",
        "running_time"
    ]

    @staticmethod
    def fix_events_before_save(
        event_frames_df: pd.DataFrame,
        pc_type: str,
        is_historical_data: bool,
        msdp_data: pd.DataFrame,
        mdr_data: pd.DataFrame,
        bbct_data: pd.DataFrame,
        reporting_site_configuration: ReportingSiteConfiguration,
        timeseries_configuration: pd.DataFrame,
        lead_product_repository: Optional[LeadProductRepository],
        reporting_line: str,
    ) -> pd.DataFrame:
        """
        Applies necessary fixes before saving events, ensuring that specific columns
        are set to None for certain event definitions.
        """
        event_frames_df = EventFixingService._set_calculates_fields(event_frames_df, msdp_data, mdr_data, timeseries_configuration, reporting_line)
        event_frames_df = EventFixingService._set_none_values_for_conditions(event_frames_df)
        event_frames_df = EventFixingService._fix_duration_by_process_type(event_frames_df, pc_type)
        event_frames_df = EventFixingService._remove_events_in_future(event_frames_df)
        event_frames_df = EventFixingService._adjust_decimal_places(event_frames_df)
        event_frames_df = EventFixingService._convert_running_time_to_seconds(event_frames_df)
        
        event_frames_df = EventFixingService._set_bbct_reference(
            event_frames_df,
            bbct_data,
            reporting_site_configuration,
            lead_product_repository,
            reporting_line,
        )
        
        # if not historical data, remove events older than 30 days
        if not is_historical_data:
            event_frames_df = EventFixingService._keep_only_last_30_days_events(event_frames_df)


        event_frames_df = EventFixingService._order_columns(event_frames_df, pc_type)

        return event_frames_df

    @staticmethod
    def _set_calculates_fields(
        event_frames_df: pd.DataFrame,
        msdp_data: pd.DataFrame,
        mdr_data: pd.DataFrame,
        timeseries_configuration: pd.DataFrame,
        reporting_line_id: str,
    ) -> pd.DataFrame:
        """
        Sets calculated fields based on the process type.
        """
        if msdp_data is not None and not msdp_data.empty and reporting_line_id in msdp_data["reportingLineExternalId"].values:
            event_frames_df = EventFixingService._calculate_msdp_fields(event_frames_df, msdp_data)

        elif mdr_data is not None and not mdr_data.empty and reporting_line_id in mdr_data["reportingLineExternalId"].values:
            event_frames_df = EventFixingService._calculate_mdr_fields(event_frames_df, mdr_data, timeseries_configuration)

        # Aplica net_production_lbs se NetProduction existir
        if "NetProduction" in event_frames_df.columns:
            event_frames_df["net_production_lbs"] = mt_to_lb(event_frames_df["NetProduction"]).round(1)

        return event_frames_df
    
    @staticmethod
    def _calculate_msdp_fields(
        event_frames_df: pd.DataFrame,
        msdp_data: pd.DataFrame
    ):
        if msdp_data is None or msdp_data.empty or "ScheduledRate" not in event_frames_df.columns:
            return event_frames_df
        
        line_filter = msdp_data["reportingLineExternalId"] == event_frames_df["refReportingLineId"].iloc[0]
        msdp_data = msdp_data[line_filter]
        
        if "MSDP" not in event_frames_df.columns:
            event_frames_df["MSDP"] = None
        
        condition_msdp = event_frames_df["MSDP"].notna() & (event_frames_df["MSDP"] > 0)
        condition_sr = event_frames_df["def"].str.endswith(("(Riser)"))
        
        event_frames_df["MSDP"] = event_frames_df.apply(
            lambda row: EventFixingService._get_msdp_value(
                row,
                msdp_data=msdp_data,
                data_aux="msdp",
            ) if not condition_msdp.loc[row.name] and not condition_sr.loc[row.name] else row["MSDP"],
            axis=1,
            result_type="expand"
        )
        
        event_frames_df["lostProductionMT"] = np.where(
            condition_sr,
            (event_frames_df["total_duration_seconds"] / 3600) * event_frames_df["ScheduledRate"] / 24,
            (event_frames_df["total_duration_seconds"] / 3600) * event_frames_df["MSDP"] / 24,
        )
        
        return event_frames_df
    
    @staticmethod
    def _calculate_mdr_fields(
        event_frames_df: pd.DataFrame, 
        mdr_data: pd.DataFrame,
        timeseries_configuration: pd.DataFrame
    ):
        """
        EN: Calculates production metrics for compounding process events, including maximum production capacity,
        lost production, and proper unit conversions. This method ensures accurate RLT (Rate Loss Time) calculations
        by using actual running time instead of total event duration.
        
        PT-BR: Calcula métricas de produção para eventos de processo de compounding, incluindo capacidade máxima de
        produção, produção perdida e conversões de unidades adequadas. Este método garante cálculos precisos de RLT
        (Rate Loss Time) usando tempo de operação real em vez da duração total do evento.
        
        Args / Argumentos:
            EN: event_frames_df (pd.DataFrame): DataFrame containing event frames with production data.
            PT-BR: event_frames_df (pd.DataFrame): DataFrame contendo frames de eventos com dados de produção.
            
            EN: mdr_data (pd.DataFrame): DataFrame containing MDR (Maximum Daily Rate) data for products.
            PT-BR: mdr_data (pd.DataFrame): DataFrame contendo dados de MDR (Taxa Máxima Diária) para produtos.
            
            EN: timeseries_configuration (pd.Dataframe): Configuration dataframe for time series data processing.
            PT-BR: timeseries_configuration (pd.Dataframe): Dataframe de configuração para processamento de dados de séries temporais.
            
        Key Variables / Variáveis-Chave:
            EN: MDR: Maximum Daily Rate
            PT-BR: MDR: Taxa Máxima Diária de produção
            
            EN: running_time: Actual operation time in hours
            PT-BR: running_time: Tempo real de operação em horas
            
            EN: maxProductionMT: Maximum possible production in metric tons - Used to assist in lostProductionMT calculation
            PT-BR: maxProductionMT: Produção máxima possível em toneladas - Utilizado para auxilio no calculo de lostProductionMT
            
            EN: lostProductionMT: Lost production calculated as maxProduction - netProduction
            PT-BR: lostProductionMT: Produção perdida calculada como maxProduction - netProduction
            
            EN: NetProduction: Actual net production achieved
            PT-BR: NetProduction: Produção líquida real alcançada
            
        Important Notes / Notas Importantes:
            EN: NetProduction conversion must happen BEFORE lostProductionMT calculation to ensure accuracy
            PT-BR: A conversão de NetProduction deve acontecer ANTES do cálculo de lostProductionMT para garantir precisão
            
            EN: Uses running_time (actual operation time) instead of total_duration_seconds for production calculations
            PT-BR: Usa running_time (tempo de operação real) em vez de total_duration_seconds para cálculos de produção
            
            EN: Handles both kg/h and lb/h MDR units with proper conversions
            PT-BR: Trata unidades MDR de kg/h e lb/h com conversões adequadas
            
        Returns / Retorna:
            EN: pd.DataFrame: Updated event frames DataFrame with calculated production metrics.
            PT-BR: pd.DataFrame: DataFrame de frames de eventos atualizado com métricas de produção calculadas.
        """
        
        if "MdrTag" in event_frames_df:
            unit_value = timeseries_configuration.loc[
                timeseries_configuration["alias"].str.contains("MdrTag", na=False),
                "unit"
            ].iloc[0]
            event_frames_df["mdr_unit"] = unit_value if unit_value is not None else "kg/h"
            event_frames_df["MDR"] = np.where(
                event_frames_df["MdrTag"].notna() & event_frames_df["MDR"].isna(),
                event_frames_df["MdrTag"],
                event_frames_df["MDR"]
            )
        else:
            filtered_mdr = mdr_data[["productId"]].rename(columns={"productId": "Product"})
            filtered_mdr["mdr_unit"] = mdr_data["refUnitOfMeasurement"].apply(lambda x: x.get("symbol") if isinstance(x, dict) else None)
            event_frames_df["mdr_unit"] = event_frames_df.merge(filtered_mdr, on="Product", how="left")["mdr_unit"].fillna(filtered_mdr["mdr_unit"].values[0])

            # verify if MDR column exists, if not create it
            if "MDR" not in event_frames_df.columns:
                event_frames_df["MDR"] = np.nan

            # Determine MDR columns and data_aux based on scheduledRate presence
            if mdr_data["scheduledRate"].isnull().all():
                mdr_columns, data_aux = ["MDR"], ["unitAvgRate"]
            else:
                mdr_columns, data_aux = ["MDR", "ScheduledRate"], ["scheduledRate", "unitAvgRate"]

            event_frames_df[mdr_columns] = event_frames_df.apply(
                lambda row: EventFixingService._get_mdr_value(
                    row,
                    mdr_data=mdr_data,
                    data_aux=data_aux,
                ) if pd.isna(row["MDR"]) else row["MDR"],
                axis=1,
                result_type="expand"
            )

        if {"MDR", "total_duration_seconds", "mdr_unit"}.issubset(event_frames_df.columns):
            event_frames_df["lostProductionMT"] = np.where(
                event_frames_df["mdr_unit"] == "kg/h",
                (event_frames_df["total_duration_seconds"] / 3600) * kg_to_mt(event_frames_df["MDR"]),
                (event_frames_df["total_duration_seconds"] / 3600) * lb_to_mt(event_frames_df["MDR"])
            )

        if {"NetProduction", "total_feed_unit"}.issubset(event_frames_df.columns):
            event_frames_df["NetProduction"] = np.where(
                event_frames_df["total_feed_unit"].str.lower().str.startswith("kg", na=False),
                kg_to_mt(event_frames_df["NetProduction"]),
                lb_to_mt(event_frames_df["NetProduction"])
            )
            
        return event_frames_df
    
    @staticmethod
    def _get_msdp_value(
        row: pd.Series,
        msdp_data: pd.DataFrame,
        data_aux: str,
    ) -> float:
        
        row["Year"] = row["start_time"].year
        row["Month"] = row["start_time"].month
        row["Day"] = row["start_time"].day
        
        product_filter = None
        if row["refReportingLineId"] in const.HAS_PRODUCT_FILTER:
            return get_msdp_product_filter(row, row[const.PRODUCT], msdp_data, data_aux)

        return msdp_util_get_msdp_value(row, msdp_data, data_aux, product_filter)
    
    @staticmethod
    def _get_mdr_value(
        row: pd.Series,
        mdr_data: pd.DataFrame,
        data_aux: List[str],
    ) -> Union[float, pd.Series]:
        
        return HourlyDataCompounding.get_mdr_value(
            row["refReportingLineId"], 
            row=row, 
            mdr_data=mdr_data,
            data_aux=data_aux,
        )

    @staticmethod
    def _set_bbct_reference(
        event_frames_df: pd.DataFrame,
        bbct_data: pd.DataFrame,
        reporting_site_configuration: ReportingSiteConfiguration,
        lead_product_repository: Optional[LeadProductRepository],
        reporting_line_id: str,
    ) -> pd.DataFrame:
        """
        Create BBCT reference for each event frame based on the productId, reportingLineExternalId and timestamp.
        This method checks if the event definition and reporting line depend on BBCT, filters the BBCT data accordingly,
        and assigns the most recent externalId and space to the event frame.

        For each line in the event frames, it checks if the event definition is in the list of event definitions that depend on BBCT.
        If it does, it filters the BBCT data based on the productId, reportingLineExternalId, and dateSet.
        If a match is found, it assigns the externalId and space to the refOEEBBCT column in the event frames DataFrame.
        Args:
            event_frames_df (pd.DataFrame): DataFrame containing event frames.
            bbct_data (pd.DataFrame): DataFrame containing BBCT data.
            reporting_site_configuration (ReportingSiteConfiguration): Configuration object for the reporting site.
        Returns:
            pd.DataFrame: Updated event frames DataFrame with refOEEBBCT column set.
        Note:
            - The refOEEBBCT column is initialized to None and will be updated with the externalId and space from the BBCT data.
            - The method assumes that the bbct_data DataFrame contains columns: productId, reportingLineExternalId, dateSet, externalId, and space.
            - The event_frames_df DataFrame is expected to have columns: event_definition, Product, ProductDescription, refReportingLineId, start_time.
        """

        event_frames_df["refOEEBBCT"] = None  # Initialize the column to None
        
        if bbct_data is None or bbct_data.empty:
            return event_frames_df
        
        bbct_data["dateSet"] = pd.to_datetime(bbct_data["dateSet"], errors="coerce")

        lead_bbct = EventFixingService._get_lead_bbct_value(
            reporting_line_id=reporting_line_id,
            lead_product_repository=lead_product_repository,
        )

        for index, row in event_frames_df.iterrows():
            event_definition = row.get("event_definition")
            product_id = row.get("Product")
            product_description = row.get("ProductDescription")
            reporting_line = row.get("refReportingLineId")
            start_time = row.get("start_time")
            loss_time = row.get("total_duration_seconds", None)
            bbct = lead_bbct.model_copy() if lead_bbct else None

            if pd.isna(product_id) or pd.isna(event_definition) or pd.isna(start_time):
                continue

            # Check if the event definition is in the list of event definitions that depend on BBCT
            if not reporting_site_configuration.event_dependends_on_bbct(
                reporting_line, event_definition
            ):
                continue

            if row.get("def") != const.BATCH_IDLE:
                # 🔍 Find de BBCT register
                filter_bbct = (
                    (
                        (bbct_data["productId"] == product_id) |  
                        (bbct_data["productId"] == product_description) | 
                        (bbct_data["productId"] == product_description.lower() if product_description else False) | 
                        (bbct_data["productId"] == product_description.upper() if product_description else False) 
                    ) &
                    (bbct_data["reportingLineExternalId"] == reporting_line) &
                    (bbct_data["dateSet"] <= start_time)
                )

                aux = bbct_data.loc[filter_bbct].copy()

                if not aux.empty:
                    # Sort by dateSet to get the most recent entry
                    aux.sort_values(by="timestamp", ascending=False, inplace=True)
                    bbct = LeadProductBbct(
                        externalId=aux.iloc[0]["externalId"],
                        space=aux.iloc[0]["space"],
                        bestBatchCycleTimeMT=aux.iloc[0].get("bestBatchCycleTimeMT"),
                        bestBatchCycleTimeHr=aux.iloc[0].get("bestBatchCycleTimeHr"),
                    )

            if not bbct or not bbct.external_id:
                continue

            # Create Column refOEEBBCT with the externalId and space in Dataframe
            event_frames_df.at[index, "refOEEBBCT"] = {
                "externalId": bbct.external_id,
                "space": bbct.space
            }
            
            if loss_time is not None:
                event_frames_df.at[index, "lostProductionMT"] = (loss_time / 60 / 60) * bbct.best_batch_cycle_time_mt / bbct.best_batch_cycle_time_hr

        return event_frames_df


    @staticmethod
    def _order_columns(event_frames_df: pd.DataFrame, pc_type: str) -> pd.DataFrame:
        """
        Orders the columns of the DataFrame to a predefined order.
        """
        # Define the desired column order
        desired_order = []
        if pc_type == "Batch":
            desired_order = [
                "externalId", "refReportingLineId", "event_definition", "start_time", "end_time", "def",
                "running_time", "total_duration_seconds", "refOEEBBCT", "NetProduction", "net_production_lbs",
                "lostProductionMT", "status", "isOpen", "isManual", "isDeleted",
                "MSDP", "ScheduledRate",
            ]
        elif pc_type == "Compounding":
            desired_order = [
                "externalId", "refReportingLineId", "event_definition", "start_time", "end_time", "def",
                "running_time", "total_duration_seconds", "lostProductionMT", "MDR", "mdr_unit",
                "NetProduction", "net_production_lbs",
                "status", "isOpen", "isManual", "isDeleted", "MSDP", "ScheduledRate", "refOEEBBCT",
            ]
        else:
            desired_order = [
                "externalId", "refReportingLineId", "event_definition", "start_time", "end_time", "def",
                "NetProduction", "net_production_lbs", "MSDP", "ScheduledRate", "running_time", 
                "total_duration_seconds", "status", "isOpen", "isManual", "isDeleted", 
                "refOEEBBCT", "lostProductionMT"
            ]
        
        desired_existing = [col for col in desired_order if col in event_frames_df.columns]
        other_cols = [c for c in event_frames_df.columns if c not in desired_existing]
        ordered_cols = desired_existing + other_cols

        event_frames_df = event_frames_df[ordered_cols]

        return event_frames_df

    @staticmethod
    def _set_none_values_for_conditions(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Sets specific columns to None based on event definitions that are NOT in the exclusion list.
        Special exception: "Producing Waste" events nullify production columns but keep running_time.
        """

        # Columns to set as None when condition is met
        columns_to_nullify = ["NetProduction", "net_production_lbs", "ScheduledRate", "running_time"]
        
        # Special handling for "Producing Waste" - nullify production columns but keep running_time
        producing_waste_mask = event_frames_df["def"] == "Producing Waste"
        producing_waste_columns = ["NetProduction", "net_production_lbs", "ScheduledRate"]
        event_frames_df.loc[producing_waste_mask, producing_waste_columns] = None

        # Create mask for rows where 'def' is NOT in the exclusion list AND not "Producing Waste"
        mask = (~event_frames_df["def"].isin(RLT_DEFINITIONS)) & (~producing_waste_mask)

        # Apply None values to the selected columns
        event_frames_df.loc[mask, columns_to_nullify] = None

        return event_frames_df

    @staticmethod
    def _fix_duration_by_process_type(event_frames_df: pd.DataFrame, pc_type: str) -> pd.DataFrame:
        """
        Fixes the duration of events based on the process type.
        """
        if pc_type == "Continuous":
            return EventFixingService._fix_duration_for_continuous_process(event_frames_df)
       
        return event_frames_df

    @staticmethod
    def _fix_duration_for_continuous_process(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Fixes the duration of events for continuous processes.
        """
        
        event_frames_df["start_time"] = pd.to_datetime(event_frames_df["start_time"])
        event_frames_df["end_time"] = pd.to_datetime(event_frames_df["end_time"])

        # If endtime - starttime is more than 1 day, set it to 1 day
        mask = (event_frames_df["end_time"] - event_frames_df["start_time"]).dt.days > 0
        event_frames_df.loc[mask, "end_time"] = event_frames_df.loc[mask, "start_time"] + pd.Timedelta(days=1)

        return event_frames_df
    
    @staticmethod
    def event_frame_column_renaming_mapper(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardizes column names by mapping various used aliases 
        to a set of predefined column names.
        """

        net_production_column_alias = {
            "NetProduction": "NetProduction",
            "TotalFeed": "NetProduction",
            "production": "NetProduction",
            "total_produced": "NetProduction",
            "TotalNetProduction": "NetProduction",
            "total_gur_produced": "NetProduction",
            "VAMProduced": "NetProduction",
            "net_production": "NetProduction",
            "material_produced": "NetProduction",
            "FeedFlow": "NetProduction"
        }

        msdp_column_alias = {
            "MSDP": "MSDP",
            "msdp": "MSDP",
        }

        sr_column_alias = {
            "ScheduledRate": "ScheduledRate",
            "ScheduleRate": "ScheduledRate",
            "scheduledRate": "ScheduledRate",
            "SCHR": "ScheduledRate",
            "ScheduledRateYield": "ScheduledRate"
        }

        running_time_column_alias = {
            "running_time": "running_time"
        }

        rename_mapper = {**net_production_column_alias, **msdp_column_alias, **sr_column_alias, **running_time_column_alias}

        event_frames_df.rename(columns=rename_mapper, inplace=True)

        return event_frames_df

    @staticmethod
    def fix_minor_stops_within_starting_up(df):
        # PT-BR: Agrupa o DataFrame por refReportingLineId
        # EN: Group the DataFrame by refReportingLineId
        grouped = df.groupby('refReportingLineId')

        processed_dfs = []

        # PT-BR: Itera sobre os grupos
        # EN: Iterates over the groups
        for line_id, line_df in grouped:
            # PT-BR: Filtra os registros "Starting up" para a linha atual
            # EN: Filters the "Starting up" records for the current line
            starting_up = line_df[line_df['def'] == 'Starting Up']

            # PT-BR: Função para verificar se um "Minor Stops" está dentro de algum "Starting up"
            # EN: Function to check if a "Minor Stops" is within any "Starting up"
            def is_within_starting_up(row):
                for _, su_row in starting_up.iterrows():
                    if su_row['start_time'] <= row['start_time'] <= su_row['end_time'] or \
                        su_row['start_time'] < row['end_time'] <= su_row['end_time'] :
                        return True
                return False

            mask = line_df['def'] == 'Minor Stops'
            line_df = line_df[~(mask & line_df.apply(is_within_starting_up, axis=1))]
            processed_dfs.append(line_df)

        df = pd.concat(processed_dfs, ignore_index=True)
        return df

    @staticmethod
    def adjust_batch_loss_for_batch_hold(df: pd.DataFrame, event_hierarchy: list):
        """
        For each 'Batch Loss Time' event, subtracts the overlapping duration of any 'Batch Hold' events.
        Uses event_hierarchy to determine which event codes to use.
        """
        df = df.copy()
        df["batch_hold_overlap_seconds"] = 0.0

        # Find event codes for Batch Loss Time and Batch Hold
        batch_loss_codes = [e.event_hierarchy for e in event_hierarchy if e.event_definition == "Batch Loss Time"]
        batch_hold_codes = [e.event_hierarchy for e in event_hierarchy if e.event_definition == "Batch Hold"]

        batch_loss_events = df[df['event_definition'].isin(batch_loss_codes)].copy()
        batch_hold_events = df[df['event_definition'].isin(batch_hold_codes)].copy()

        # Iterate over each batch loss event
        for bl_idx, bl_row in batch_loss_events.iterrows():
            bl_start = bl_row['start_time']
            bl_end = bl_row['end_time']
            overlap = 0.0

            # Iterate over each batch hold and filter events that overlap with the current batch loss event
            for _, bh_row in batch_hold_events.iterrows():
                bh_start = bh_row['start_time']
                bh_end = bh_row['end_time']

                # Calculate overlap
                latest_start = max(bl_start, bh_start)
                earliest_end = min(bl_end, bh_end)
                delta = (earliest_end - latest_start).total_seconds()
                if delta > 0:
                    overlap += delta

            # Update the batch_loss row
            batch_loss_events.at[bl_idx, "batch_hold_overlap_seconds"] = overlap
            if "total_duration_seconds" in batch_loss_events.columns:
                orig_duration = batch_loss_events.at[bl_idx, "total_duration_seconds"]
                batch_loss_events.at[bl_idx, "total_duration_seconds"] = orig_duration - overlap

        df.drop(columns=["batch_hold_overlap_seconds"], inplace=True)

        df.update(batch_loss_events)
        return df

    @staticmethod
    def _remove_events_in_future(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Removes events when event in the future.
        """
        end_time_tz = event_frames_df["end_time"].dt.tz

        mask = event_frames_df["end_time"] > pd.Timestamp.now().tz_localize("UTC").tz_convert(end_time_tz)
        event_frames_df = event_frames_df[~mask]
        
        mask = event_frames_df["start_time"] > pd.Timestamp.now().tz_localize("UTC").tz_convert(end_time_tz)
        event_frames_df = event_frames_df[~mask]
        
        return event_frames_df



    @staticmethod
    def _keep_only_last_30_days_events(event_frame_df: pd.DataFrame) -> pd.DataFrame:
        """
        Keeps only events whose start_time is within the last 30 days.
        """
        # Detecte the timezone of the start_time column
        tz = event_frame_df["start_time"].dt.tz

        # Apply the same timezone to the cutoff_date
        cutoff_date = pd.Timestamp(datetime.now(), tz=tz) - pd.Timedelta(days=30)

        return event_frame_df[event_frame_df["start_time"] >= cutoff_date].copy()

    @staticmethod
    def _adjust_decimal_places(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Adjusts DataFrame columns decimal places.
        """
        adjust_columns = ["MSDP", "ScheduledRate", "running_time", "NetProduction", "maxProductionMT", "lostProductionMT", "net_production_lbs"]
        decimal_columns = {
            "decimal": {
                "MSDP": 1,
                "ScheduledRate": 1,
                "NetProduction": 2,
                "maxProductionMT": 2,
                "net_production_lbs": 2,
                "running_time": 2,
                "lostProductionMT": 2
            },
            "round": {
                "MSDP": False,
                "ScheduledRate": False,
                "NetProduction": False,
                "maxProductionMT": True,
                "net_production_lbs": False,
                "running_time": True,
                "lostProductionMT": True
            }
        }
        
        event_frames_df = event_frames_df[~event_frames_df.isin([np.inf, -np.inf]).any(axis=1)]
        
        for col in event_frames_df.columns:
            if col in adjust_columns:
                decimal_places = decimal_columns["decimal"].get(col, 2)
                should_round = decimal_columns["round"].get(col, False)
                
                if should_round:
                    factor = 10 ** decimal_places
                    event_frames_df[col] = event_frames_df[col].apply(
                        lambda x: math.ceil(x * factor) / factor if pd.notna(x) and (x * factor) % 1 >= 0.1 else round(x, decimal_places) if pd.notna(x) else x
                    )
                else:
                    factor = 10 ** decimal_places
                    event_frames_df[col] = event_frames_df[col].apply(lambda x: math.trunc(x * factor) / factor if pd.notna(x) else x)
                    
        event_frames_df.loc[event_frames_df["running_time"] > 24, "running_time"] = 24

        event_frames_df = EventFixingService._convert_batch_to_int(event_frames_df)

        return event_frames_df
    
    @staticmethod
    def _convert_batch_to_int(event_frame_df: pd.DataFrame) -> pd.DataFrame:
        """
        Replaces float values to int in the specified columns of the DataFrame.
        """
        cols_to_process = ["Batch", "BatchID", "ProcessOrder"]
        cols_to_iterate = [col for col in event_frame_df.columns.to_list() if col in cols_to_process]

        for col in cols_to_iterate:
            try:
                # Only convert non-null values to int
                event_frame_df[col] = event_frame_df[col].apply(lambda x: int(x) if pd.notnull(x) else x)
            except ValueError:
                pass

        return event_frame_df
    
    @staticmethod
    def _convert_running_time_to_seconds(event_frame_df: pd.DataFrame) -> pd.DataFrame:
        """Convert running time value to seconds."""
        if "running_time" in event_frame_df.columns.to_list():
            event_frame_df["running_time"] = event_frame_df["running_time"] * HOURS_TO_SECONDS_FACTOR
        return event_frame_df
    
    
    @staticmethod
    def fill_product_from_lead_product(
        data_df: pd.DataFrame,
        reporting_line: str,
        lead_product_repository: Optional[LeadProductRepository],
        apply_to_columns: list[str],
    ) -> pd.DataFrame:
        """
        PT-BR: Preenche colunas relacionadas a produto usando o valor configurado como lead product
        para a linha em questão.
        
        EN: Fills product related columns using the configured lead product value
        for the given line.
        
        Args:
            data_df: DataFrame containing the data to be filled.
            reporting_line: External ID of the reporting line.
            lead_product_repository: Repository for lead product data.
            apply_to_columns: Columns to be filled.
        Returns:
            DataFrame with the filled product related columns.
        """
        if (
            lead_product_repository is None
            or data_df is None
            or data_df.empty
            or not reporting_line
        ):
            return data_df

        candidate_columns = [col for col in apply_to_columns if col in data_df.columns]
        if not candidate_columns:
            return data_df

        result = data_df.copy()
        missing_masks = [
            result[col].isna() | (result[col].fillna("").astype(str).str.strip() == "") | (result[col].astype(str) == "0")
            for col in candidate_columns
        ]

        combined_mask = missing_masks[0]
        for mask in missing_masks[1:]:
            combined_mask = combined_mask | mask

        if not combined_mask.any():
            return data_df

        lead_value = EventFixingService._get_lead_product_value(
            reporting_line_id=reporting_line,
            lead_product_repository=lead_product_repository,
        )

        if not lead_value:
            return data_df

        for col, mask in zip(candidate_columns, missing_masks):
            if mask.any():
                result.loc[mask, col] = lead_value

        return result

    @staticmethod
    def _get_lead_product_value(
        reporting_line_id: str,
        lead_product_repository: LeadProductRepository,
    ) -> Optional[str]:
        try:
            lead_product = lead_product_repository.get_lead_product(
                reporting_line_external_id=reporting_line_id
            )
        except Exception:
            return None

        if lead_product is None:
            return None

        return EventFixingService._resolve_lead_product_value(lead_product)

    @staticmethod
    def _resolve_lead_product_value(lead_product: LeadProduct) -> Optional[str]:
        for source_name in ("bbct", "mdr", "msdp"):
            candidate = getattr(lead_product, source_name, None)
            if not candidate:
                continue

            value_candidates = [
                getattr(candidate, "pi_tag_value", None),
                getattr(candidate, "product", None),
            ]

            if hasattr(candidate, "product_group"):
                value_candidates.append(getattr(candidate, "product_group"))

            for value in value_candidates:
                normalized = EventFixingService._normalize_optional_str(value)
                if normalized:
                    return normalized

        return None

    @staticmethod
    def _normalize_optional_str(value: Optional[str]) -> Optional[str]:
        if value is None:
            return None

        text = str(value).strip()
        return text or None

    @staticmethod
    def _get_lead_bbct_value(
        reporting_line_id: str,
        lead_product_repository: LeadProductRepository,
    ) -> Optional[LeadProductBbct]:
        try:
            lead_product = lead_product_repository.get_lead_product(
                reporting_line_external_id=reporting_line_id
            )
        except Exception:
            return None

        if lead_product is None:
            return None

        return lead_product.bbct