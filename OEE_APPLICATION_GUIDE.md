# OEE Event Frame Application - Complete Guide

## Table of Contents
- [Application Overview](#application-overview)
- [Application Structure](#application-structure)
- [Running the Application](#running-the-application)
- [Defining Events for a New Site](#defining-events-for-a-new-site)
- [Event Types and Implementation](#event-types-and-implementation)
- [File Structure Reference](#file-structure-reference)
- [Configuration Files](#configuration-files)
- [Custom Event Development](#custom-event-development)

## Application Overview

This is a **Flask-based OEE (Overall Equipment Effectiveness) application** that connects to **Cognite Data Fusion (CDF)** to process manufacturing data and identify events across multiple industrial sites. The application analyzes time series data from production lines to automatically detect and categorize OEE events.

### Key Features
- **Multi-site support** with site-specific event logic
- **Real-time event detection** from PI tags/time series data
- **Configurable event hierarchies** and business rules
- **Integration with Cognite Data Fusion** for data storage
- **Web interface** for monitoring and analysis
- **Automated event processing** with customizable rules

## Application Structure

### Main Entry Point
```
OEE_EventFrame/
├── main.py                 # Main entry point - run with: python main.py
├── config.py              # Application configuration
├── requirements.txt       # Python dependencies
└── oee_function\.env      # Environment variables (Cognite settings)
```

### Core Directories
```
├── app/                   # Main application package
│   ├── __init__.py       # Flask app factory
│   ├── routes.py         # Web routes and API endpoints
│   ├── models/           # Data models and schemas
│   ├── services/         # Business logic and event processing
│   ├── repositories/     # Data access layer
│   └── enums/           # Site-specific enumerations
├── static/               # Web assets (CSS, JS, images)
├── templates/            # HTML templates
└── scripts/              # Utility scripts and data import
```

## Running the Application

### Prerequisites
1. **Python 3.8+** installed
2. **Access to Cognite Data Fusion** environment
3. **Proper authentication** configured in `.env` file

### Setup Steps
1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure environment** (update `oee_function\.env`):
   ```env
   COGNITE_BASE_URI=https://az-eastus-1.cognitedata.com
   COGNITE_PROJECT=celanese-dev
   COGNITE_DATA_MODEL_EXTERNAL_ID=OEESOL
   COGNITE_DEFAULT_DATA_MODEL_INSTANCES_SPACE=OEE-COR-ALL-DAT
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

4. **Access the web interface**:
   ```
   http://localhost:5000
   ```

## Defining Events for a New Site

### Step-by-Step Process

#### 1. Configure Site in Excel
Edit `scripts/data/GlobalConfiguration.xlsx` with three main sheets:

**A. OEEReportingSiteConfiguration Sheet:**
| Column | Description | Example |
|--------|-------------|---------|
| `reportingSiteExternalId` | Unique site ID | `STS-MYNEWSITE` |
| `reportingSiteSpace` | CDF space | `REF-COR-ALL-DAT` |
| `shifts` | Work shifts | `['Day', 'Night']` |
| `lineType` | Process type | `Batch` or `Continuous` |
| `extraTimeProcessing` | Buffer time (min) | `5` |

**B. OEEInputTagConfiguration Sheet:**
| Column | Description | Example |
|--------|-------------|---------|
| `reportingLineExternalId` | Production line ID | `RLN-MYNEWSITE-LINE01` |
| `timeSeries` | PI tag/time series | `PI_TAG_STATUS` |
| `alias` | Descriptive name | `Line01_Status` |
| `eventIdentification` | Used for events | `True` |

**C. OEEEventHierarchyConfiguration Sheet:**
| Column | Description | Example |
|--------|-------------|---------|
| `reportingLineExternalId` | Line ID | `RLN-MYNEWSITE-LINE01` |
| `eventDefinition` | Event name | `Not Running` |
| `eventHierarchy` | Event code | `1a` |
| `subCategoryLevel1` | Category | `Equipment Failure` |
| `eventCode` | Internal code | `EQ_FAIL` |
| `metricCode` | OEE metric | `AVAILABILITY` |

#### 2. Deploy Configuration
```bash
cd scripts
python import_reference_data.py
```

#### 3. Verify Setup
- Run application: `python main.py`
- Check site appears in web interface
- Test with sample data

### Common Event Types to Define

#### Availability Events (Type 1 - Downtime)
- **1a - Not Running**: Equipment stopped
- **1b - Changeover**: Product changeover
- **1c - Maintenance**: Planned maintenance
- **1d - Breakdown**: Unplanned failure

#### Performance Events (Type 2 - Speed Loss)
- **2a - Minor Stop**: Brief interruptions
- **2b - Reduced Speed**: Below target speed

#### Quality Events (Type 3 - Defects)
- **3a - Quality Issue**: Product defects
- **3b - Startup Reject**: Initial waste

## Event Types and Implementation

### Standard OEE Event Framework

The application implements a standardized event type system:

```python
# Event Type Methods in EventIdentification Protocol
identify_events_typeIa()    # 1a - Not Running
identify_events_typeIb()    # 1b - Changeover  
identify_events_typeIIa()   # 2a - Minor Stops
identify_events_typeIIIa()  # 3a - Quality Issues
identify_events_typeIVa()   # 4a - Custom Events
```

### Event Processing Pipeline

1. **Data Retrieval**: Fetch time series data from Cognite
2. **Event Identification**: Apply site-specific logic
3. **Event Processing**: Apply business rules
4. **Event Validation**: Fix and validate events
5. **Data Storage**: Save to Cognite Data Fusion

### Event Processors

- **Minor Stop Processor**: Handles short duration stops
- **Product Swap Processor**: Manages changeover events  
- **Not Running Processor**: Processes downtime events
- **Event Fixing Service**: Validates and corrects events

## File Structure Reference

### Core Event Framework
```
oee_function/app/
├── models/
│   ├── event_frame.py                    # Core Event classes
│   ├── oee_event.py                      # OEE Event model
│   ├── event_hierarchy_configuration.py  # Event config model
│   └── reporting_site_configuration.py   # Site config model
├── services/
│   ├── event_frame_service.py            # Main orchestrator
│   ├── event_frame_minor_stop_processor.py
│   ├── event_frame_not_running_processor.py
│   └── event_frame_product_swap_processor.py
└── repositories/
    ├── OEEEvent_repository.py            # Event data access
    └── reporting_site_repository.py      # Site config access
```

### Site-Specific Event Services
```
oee_function/app/services/event_identification/
├── bay/     # Bay site events
├── bis/     # Bis site events (multiple variants)
├── can/     # Can site events (AC20, ETA, MEA, MEO, MIBK)
├── eno/     # Enoree site events (batch, continuous, VAE)
├── frac/    # Frac site events (multiple product lines)
├── shy/     # Shy site events (FBR, SSP variants)
└── [30+ other site directories]
```

## Configuration Files

### Primary Configuration
- **`scripts/data/GlobalConfiguration.xlsx`**: Main configuration spreadsheet
- **`oee_function\.env`**: Cognite connection settings
- **`scripts/import_reference_data.py`**: Configuration import script

### Mock Data & Examples
- **`oee_function/app/mock/bbct_configurations.json`**: Best Batch Cycle Time data
- **Site-specific enum files**: Process status definitions

## Custom Event Development

### Creating a New Site Event Service

1. **Create site directory**:
   ```
   oee_function/app/services/event_identification/mynewsite/
   ```

2. **Implement EventIdentification protocol**:
   ```python
   class MyNewSiteEventIdentificationService:
       def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
           # Custom logic for Not Running events
           data = data.assign(
               event1a_start=(
                   (data["ProductionLineStatus"] == "Stopped")
                   & (data["ProductionLineStatus"].shift(1) != "Stopped")
               )
           )
           return data
   ```

3. **Register in factory**:
   ```python
   # In event_identification_factory.py
   from .mynewsite.mynewsite_event_identification_service import MyNewSiteEventIdentificationService
   
   # Add to factory mapping
   "RLN-MYNEWSITE-LINE01": MyNewSiteEventIdentificationService,
   ```

4. **Configure in Excel**: Add site configuration to GlobalConfiguration.xlsx

5. **Deploy**: Run import script and test

### Event Identification Patterns

#### Batch Process Example:
```python
def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
    # Not Running: When batch status is IDLE
    data = data.assign(
        event1a_start=(data["BatchStatus"] == "IDLE")
    )
    return data
```

#### Continuous Process Example:
```python
def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
    # Not Running: When flow rate below threshold
    data = data.assign(
        event1a_start=(
            (data["FlowRate"] < 100) 
            & (data["FlowRate"].shift(1) >= 100)
        )
    )
    return data
```

### Best Practices

1. **Use descriptive external IDs**: Follow naming convention `STS-SITECODE`
2. **Test with sample data**: Validate event detection before production
3. **Document custom logic**: Comment complex event identification rules
4. **Follow OEE standards**: Use standard event codes (1a, 2a, 3a, etc.)
5. **Backup configurations**: Keep Excel file versions
6. **Monitor performance**: Check event processing times

### Troubleshooting

- **Events not appearing**: Check site configuration in Excel
- **Wrong event types**: Verify event hierarchy codes
- **Data issues**: Validate time series connections
- **Performance problems**: Review custom event logic efficiency

## Advanced Configuration

### Environment Variables Reference

The `oee_function\.env` file contains critical Cognite Data Fusion settings:

```env
# Cognite Connection Settings
COGNITE_BASE_URI=https://az-eastus-1.cognitedata.com
COGNITE_PROJECT=celanese-dev
COGNITE_CLIENT_NAME=oee_event_frame

# Data Model Configuration
COGNITE_DATA_MODEL_EXTERNAL_ID=OEESOL
COGNITE_DATA_MODEL_SPACE=INO-COR-ALL-DML
COGNITE_DATA_MODEL_VERSION=6_1_2

# Data Spaces
COGNITE_DEFAULT_DATA_MODEL_INSTANCES_SPACE=OEE-COR-ALL-DAT
COGNITE_ASSET_HIERARCHY_INSTANCES_SPACE=REF-COR-ALL-DAT
COGNITE_DATA_SET_ID=7781506074618410

# Authentication
AUTH_TENANT_ID=7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37
AUTH_CLIENT_ID=07d2151f-7b19-4e88-976b-c84cbbc24a3b
AUTH_SCOPES=https://az-eastus-1.cognitedata.com/.default
AUTH_TOKEN_URI=https://login.microsoftonline.com/7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37/oauth2/v2.0/token
```

### Event Processing Rules

#### Minor Stop Processing
```python
# Default settings in event_frame_minor_stop_processor.py
MINOR_STOP_DEFINITIONS = {
    "Minor Stops",
    "Minor Stop",
    "Minor Stop During Product Trial"
}
DEFAULT_MINOR_STOP_DURATION_MINUTES = 15
```

#### Product Swap Processing
```python
# Settings in event_frame_product_swap_processor.py
PRODUCT_SWAP_EVENT_DEFINITIONS = {"Not Running"}
METRIC_CODE_DEFINITIONS = {"Availability"}
EVENT_CODE_DEFINITIONS = {"Product And Supply Optimization"}
SUBCAT_LEVEL_1_DEFINITIONS = {"Product Swap"}
DEFAULT_DURATION_MINUTES = 5
```

#### Not Running Processing
```python
# Settings in event_frame_not_running_processor.py
DURATION_NOT_RUNNING = 3600  # 1 hour in seconds
```

### Data Model Structure

#### Event Hierarchy Configuration Fields
- `externalId`: Unique identifier
- `space`: CDF space reference
- `reportingLine`: Production line reference
- `eventHierarchy`: Event code (1a, 2a, 3a, etc.)
- `eventDefinition`: Human-readable event name
- `subCategoryLevel1/2`: Event categorization
- `eventCode`: Internal event code
- `metricCode`: OEE metric classification
- `businessRule`: Enable/disable event processing
- `workShiftRule`: Apply shift-based rules
- `usesBbct`: Use Best Batch Cycle Time
- `minorStop`: Mark as minor stop event
- `notRunningRule`: Apply not-running rules
- `variableCategories`: Enable variable categorization

## Deployment and Operations

### Production Deployment

1. **Environment Setup**:
   ```bash
   # Create virtual environment
   python -m venv oee_env
   source oee_env/bin/activate  # Linux/Mac
   # or
   oee_env\Scripts\activate     # Windows

   # Install dependencies
   pip install -r requirements.txt
   ```

2. **Configuration Validation**:
   ```bash
   # Test Cognite connection
   python -c "from oee_function.app.infra.env_variables import EnvVariables; print('Config loaded successfully')"
   ```

3. **Data Import**:
   ```bash
   cd scripts
   python import_reference_data.py
   ```

4. **Service Startup**:
   ```bash
   # Development
   python main.py

   # Production (with gunicorn)
   gunicorn -w 4 -b 0.0.0.0:5000 main:app
   ```

### Monitoring and Maintenance

#### Log Monitoring
- Application logs are written to console
- Monitor for event processing errors
- Check Cognite API connection status

#### Performance Metrics
- Event processing time per site
- Data retrieval latency from Cognite
- Memory usage during large data processing

#### Regular Maintenance Tasks
1. **Update configurations**: Refresh GlobalConfiguration.xlsx
2. **Monitor data quality**: Check for missing time series data
3. **Validate events**: Review automatically generated events
4. **Performance tuning**: Optimize slow event identification services

### API Endpoints

The Flask application provides several endpoints:

- **`/`**: Main dashboard
- **`/sites`**: Site configuration management
- **`/events`**: Event monitoring and analysis
- **`/api/events`**: REST API for event data
- **`/health`**: Application health check

## Troubleshooting Guide

### Common Issues

#### 1. Site Not Appearing in Interface
**Symptoms**: New site doesn't show in dropdown
**Solutions**:
- Verify Excel configuration is complete
- Run `import_reference_data.py` script
- Check CDF space permissions
- Validate external IDs are unique

#### 2. Events Not Being Generated
**Symptoms**: No events created for a production line
**Solutions**:
- Check time series data availability in Cognite
- Verify event identification service is registered
- Review business rule settings in event hierarchy
- Test event logic with sample data

#### 3. Incorrect Event Classifications
**Symptoms**: Events assigned wrong types or categories
**Solutions**:
- Review event hierarchy configuration
- Check event identification logic
- Validate input tag mappings
- Test with known scenarios

#### 4. Performance Issues
**Symptoms**: Slow event processing or timeouts
**Solutions**:
- Optimize event identification queries
- Reduce data processing window
- Check Cognite API rate limits
- Review memory usage patterns

#### 5. Authentication Errors
**Symptoms**: Cannot connect to Cognite Data Fusion
**Solutions**:
- Verify `.env` file credentials
- Check Azure AD token validity
- Confirm client permissions in Cognite
- Test connection with Cognite SDK

### Debugging Tools

#### Enable Debug Logging
```python
# In main.py
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### Test Event Identification
```python
# Create test script
from oee_function.app.services.event_identification.event_identification_factory import EventIdentificationFactory
service = EventIdentificationFactory.create("RLN-YOURSITE-LINE01")
# Test with sample data
```

#### Validate Configuration
```python
# Test configuration loading
from oee_function.app.repositories.reporting_site_repository import ReportingSiteRepository
# Check site configuration
```

## Development Guidelines

### Code Organization
- **Models**: Data structures and validation
- **Services**: Business logic and processing
- **Repositories**: Data access and CDF integration
- **Enums**: Site-specific constants and status codes

### Testing Strategy
1. **Unit Tests**: Test individual event identification methods
2. **Integration Tests**: Test end-to-end event processing
3. **Data Tests**: Validate with historical production data
4. **Performance Tests**: Measure processing speed and memory usage

### Version Control
- Keep configuration files in version control
- Document changes to event identification logic
- Tag releases with version numbers
- Maintain changelog for configuration updates

---

**For additional support or questions, refer to the codebase documentation or contact the development team.**
