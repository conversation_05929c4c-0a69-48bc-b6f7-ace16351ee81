# Índice / Index

- [Seção em Português](#seção-em-português)
- [English Section](#english-section)

# Seção em Português

## Serviço de Delay de Eventos

Este documento descreve o funcionamento do serviço `EventsDelayService`, responsável por aplicar delay/atraso em eventos específicos baseado em configurações. O serviço remove eventos que ainda estão dentro do período de delay, aguardando tempo suficiente desde o fim do evento antes de incluí-los no processamento.

### Visão Geral

O serviço processa eventos aplicando regras de delay configuráveis por unidade de produção e tipos de eventos. A lógica principal é:

- **Remove eventos** que ainda estão dentro do período de delay (não passou tempo suficiente desde o fim do evento)
- **Mantém eventos** que já passaram do período de delay (passou tempo suficiente desde o fim do evento)

### Aplicação do Serviço

O serviço é aplicado automaticamente no `EventFrameService` durante o processamento de eventos, antes de aplicar outras correções e antes de salvar os eventos no FDM.

A aplicação ocorre no método `transform_time_series_to_event_frames` do `EventFrameService`:

```515:518:OEE_EventFrame/oee_function/app/services/event_frame_service.py
# PT-BR: Aplica delay configurado aos eventos, removendo aqueles que ainda estão dentro do período de delay
# EN: Applies configured delay to events, removing those still within the delay period
events_delay_service = EventsDelayService(event_frames_df)
event_frames_df = events_delay_service.apply_delay()
```

O serviço é aplicado a **todos os eventos** processados, mas apenas eventos que correspondem às configurações de delay são filtrados. Eventos que não correspondem a nenhuma regra são sempre mantidos.

### Configuração de Delay

A configuração de delay é definida na constante `DELAY_CONFIGURATION` da classe `EventsDelayService`:

```20:22:OEE_EventFrame/oee_function/app/services/events_delay_service.py
DELAY_CONFIGURATION: List[Dict[str, Any]] = [
    {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c"], "delay_minutes": 480},  # 8 hours
]
```

**Estrutura da configuração:**
- `unit`: Identificador da unidade de produção (ex: "UNT-NANCMP")
- `event_types`: Lista de tipos de eventos que devem ter delay aplicado (ex: ["3a", "3b", "3c"])
- `delay_minutes`: Tempo de delay em minutos (ex: 480 = 8 horas)

**Configuração atual:**
- Unidade `UNT-NANCMP`: Eventos `3a`, `3b`, `3c` com delay de 480 minutos (8 horas)

### Como Funciona

#### Lógica de Remoção/Mantimento

O serviço compara o tempo limite (`end_time + delay_minutes`) com a hora atual (`current_time`):

**Evento será REMOVIDO se:**
```
end_time + delay_minutes > current_time
```
Significa que ainda não passou tempo suficiente desde o fim do evento (ainda está dentro do período de delay).

**Evento será MANTIDO se:**
```
end_time + delay_minutes <= current_time
```
Significa que já passou tempo suficiente desde o fim do evento (já saiu do período de delay).

#### Exemplo Prático

Considere a configuração atual:
- Unidade: `UNT-NANCMP`
- Eventos: `["3a", "3b", "3c"]`
- Delay: 480 minutos (8 horas)
- Hora atual: `2024-01-15 14:00:00 UTC`

**Cenário 1: Evento será removido**
- Evento `3a` com `end_time = 2024-01-15 10:00:00 UTC`
- Tempo limite: `10:00 + 480min = 18:00`
- Comparação: `18:00 > 14:00` → **REMOVIDO** (ainda está dentro do delay)

**Cenário 2: Evento será mantido**
- Evento `3b` com `end_time = 2024-01-15 05:00:00 UTC`
- Tempo limite: `05:00 + 480min = 13:00`
- Comparação: `13:00 <= 14:00` → **MANTIDO** (já passou do delay)

### Fluxo de Processamento

1. **Validação Inicial**:
   - Verifica se o DataFrame está vazio (retorna sem modificações)
   - Valida se as colunas necessárias existem: `refUnitId`, `event_definition`, `end_time`
   - Se colunas estiverem faltando, retorna o DataFrame original sem modificações

2. **Preparação**:
   - Cria uma cópia do DataFrame para não modificar o original
   - Obtém a hora atual em UTC para comparação
   - Cria uma máscara inicial com todos os eventos marcados para manter

3. **Processamento por Regra**:
   - Para cada regra na `DELAY_CONFIGURATION`:
     - Filtra eventos que correspondem à unidade (`refUnitId == unit`)
     - Filtra eventos que correspondem aos tipos de eventos (`event_definition in event_types`)
     - Combina as máscaras (unidade E tipo de evento)
     - Converte `end_time` para datetime e normaliza para UTC
     - Filtra apenas eventos com `end_time` válido (não NaT)
     - Calcula o tempo limite: `end_time + delay_minutes`
     - Identifica eventos que devem ser removidos: `time_limit > current_time`
     - Atualiza a máscara de eventos a manter

4. **Filtragem Final**:
   - Filtra o DataFrame mantendo apenas os eventos que não devem ser removidos
   - Registra logs informativos sobre quantos eventos foram removidos

### Colunas Necessárias

O DataFrame de entrada deve conter as seguintes colunas:

- `refUnitId`: Identificador da unidade de produção (string)
- `event_definition`: Tipo do evento (string)
- `end_time`: Data/hora de fim do evento (datetime ou timestamp)

**Observação**: Se alguma dessas colunas estiver faltando, o serviço retorna o DataFrame original sem modificações e registra um aviso no log.

### Exemplos de Uso

#### Exemplo 1: Uso Básico

```python
from app.services.events_delay_service import EventsDelayService
import pandas as pd

# Preparar DataFrame com eventos
event_frames_df = pd.DataFrame({
    "refUnitId": ["UNT-NANCMP", "UNT-NANCMP", "UNT-NANCMP"],
    "event_definition": ["3a", "3b", "3c"],
    "end_time": [
        pd.Timestamp("2024-01-15 10:00:00", tz="UTC"),  # Será removido se ainda dentro do delay
        pd.Timestamp("2024-01-15 05:00:00", tz="UTC"),  # Será mantido se já passou do delay
        pd.Timestamp("2024-01-15 13:00:00", tz="UTC"),  # Será removido se ainda dentro do delay
    ],
    # ... outras colunas ...
})

# Criar instância do serviço
service = EventsDelayService(event_frames_df)

# Aplicar o delay (remove eventos que ainda estão dentro do período de delay)
filtered_df = service.apply_delay()

# Verificar resultados
print(f"Eventos originais: {len(event_frames_df)}")
print(f"Eventos após filtro: {len(filtered_df)}")
print(f"Eventos removidos: {len(event_frames_df) - len(filtered_df)}")
```

#### Exemplo 2: Eventos Não Configurados

Eventos que não correspondem a nenhuma regra de delay são sempre mantidos:

```python
event_frames_df = pd.DataFrame({
    "refUnitId": ["UNT-NANCMP", "UNT-OTHER", "UNT-NANCMP"],
    "event_definition": ["2a", "3a", "1a"],  # 2a não está na lista, UNT-OTHER não está configurado
    "end_time": [
        pd.Timestamp("2024-01-15 10:00:00", tz="UTC"),  # Mantido (não corresponde à regra)
        pd.Timestamp("2024-01-15 11:00:00", tz="UTC"),  # Mantido (unidade não configurada)
        pd.Timestamp("2024-01-15 12:00:00", tz="UTC"),  # Mantido (não corresponde à regra)
    ],
})

service = EventsDelayService(event_frames_df)
filtered_df = service.apply_delay()

# Todos os eventos serão mantidos pois não correspondem às regras configuradas
assert len(filtered_df) == len(event_frames_df)
```

#### Exemplo 3: Múltiplas Regras

Se houver múltiplas regras configuradas, cada evento é avaliado contra todas as regras:

```python
# Supondo que DELAY_CONFIGURATION contenha:
# [
#     {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c"], "delay_minutes": 480},
#     {"unit": "UNT-NANCMP", "event_types": ["1a", "1b"], "delay_minutes": 60},
# ]

event_frames_df = pd.DataFrame({
    "refUnitId": ["UNT-NANCMP", "UNT-NANCMP", "UNT-NANCMP"],
    "event_definition": ["3a", "1a", "2a"],
    "end_time": [
        pd.Timestamp("2024-01-15 10:00:00", tz="UTC"),  # Avaliado pela regra 1
        pd.Timestamp("2024-01-15 13:00:00", tz="UTC"),  # Avaliado pela regra 2
        pd.Timestamp("2024-01-15 12:00:00", tz="UTC"),  # Não corresponde a nenhuma regra
    ],
})

service = EventsDelayService(event_frames_df)
filtered_df = service.apply_delay()
```

### Como Implementar

#### 1. Adicionar Nova Regra de Delay

Para adicionar uma nova regra de delay, edite a constante `DELAY_CONFIGURATION`:

```python
DELAY_CONFIGURATION: List[Dict[str, Any]] = [
    {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c"], "delay_minutes": 480},
    {"unit": "UNT-FRACMP", "event_types": ["1a", "1b"], "delay_minutes": 120},  # Nova regra
    {"unit": "UNT-OTHER", "event_types": ["2a", "2b", "2c"], "delay_minutes": 240},  # Nova regra
]
```

#### 2. Modificar Delay Existente

Para modificar o delay de uma regra existente, altere o valor de `delay_minutes`:

```python
DELAY_CONFIGURATION: List[Dict[str, Any]] = [
    {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c"], "delay_minutes": 600},  # Alterado de 480 para 600
]
```

#### 3. Adicionar Tipos de Eventos a uma Regra

Para adicionar mais tipos de eventos a uma regra existente, adicione-os à lista `event_types`:

```python
DELAY_CONFIGURATION: List[Dict[str, Any]] = [
    {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c", "3d", "3e"], "delay_minutes": 480},  # Adicionados 3d e 3e
]
```

### Observações Importantes

- **Aplicação Automática**: O serviço é aplicado automaticamente no `EventFrameService` durante o processamento de eventos
- **Timezone**: Todas as operações de data/hora são normalizadas para UTC para garantir consistência
- **Preservação de Índices**: O serviço preserva os índices originais do DataFrame durante todas as operações
- **Eventos Não Configurados**: Eventos que não correspondem a nenhuma regra de delay são sempre mantidos (não são removidos)
- **Validação de Colunas**: Se as colunas necessárias não existirem, o serviço retorna o DataFrame original sem modificações
- **DataFrame Vazio**: Se o DataFrame estiver vazio, o serviço retorna sem modificações
- **End Time Inválido**: Eventos com `end_time` inválido (NaT) são ignorados na verificação de delay, mas não são removidos automaticamente
- **Múltiplas Regras**: Se um evento corresponder a múltiplas regras, ele será avaliado pela primeira regra que corresponder (a ordem das regras importa)
- **Performance**: O serviço utiliza operações vetorizadas do pandas para melhor performance em grandes volumes de dados
- **Logs**: O serviço registra logs informativos sobre quantos eventos foram removidos por regra e no total

### Casos de Uso

#### Caso de Uso 1: Aguardar Confirmação de Eventos Críticos

Alguns eventos críticos (como eventos de parada) podem precisar de um período de "resfriamento" antes de serem processados, para garantir que não há mais atualizações ou correções pendentes.

**Exemplo**: Eventos de parada (`3a`, `3b`, `3c`) na unidade `UNT-NANCMP` têm um delay de 8 horas para garantir que todas as informações foram atualizadas antes do processamento.

#### Caso de Uso 2: Evitar Processamento Prematuro

Eventos recentes podem ainda estar sendo atualizados ou corrigidos. O delay garante que apenas eventos "estáveis" sejam processados.

**Exemplo**: Um evento que terminou há 1 hora pode ainda estar sendo atualizado. Com um delay de 8 horas, apenas eventos que terminaram há mais de 8 horas são processados.

#### Caso de Uso 3: Sincronização com Sistemas Externos

Em alguns casos, pode ser necessário aguardar a sincronização com sistemas externos antes de processar eventos.

**Exemplo**: Aguardar 8 horas permite que sistemas externos atualizem informações relacionadas aos eventos antes do processamento.

----

# English Section

## Events Delay Service

This document describes the operation of the `EventsDelayService`, responsible for applying delay to specific events based on configurations. The service removes events that are still within the delay period, waiting for sufficient time since the event end before including them in processing.

### Overview

The service processes events by applying configurable delay rules by production unit and event types. The main logic is:

- **Removes events** that are still within the delay period (not enough time has passed since the event end)
- **Keeps events** that have already passed the delay period (enough time has passed since the event end)

### Service Application

The service is automatically applied in `EventFrameService` during event processing, before applying other corrections and before saving events to FDM.

The application occurs in the `transform_time_series_to_event_frames` method of `EventFrameService`:

```515:518:OEE_EventFrame/oee_function/app/services/event_frame_service.py
# PT-BR: Aplica delay configurado aos eventos, removendo aqueles que ainda estão dentro do período de delay
# EN: Applies configured delay to events, removing those still within the delay period
events_delay_service = EventsDelayService(event_frames_df)
event_frames_df = events_delay_service.apply_delay()
```

The service is applied to **all processed events**, but only events that match delay configurations are filtered. Events that do not match any rule are always kept.

### Delay Configuration

The delay configuration is defined in the `DELAY_CONFIGURATION` constant of the `EventsDelayService` class:

```20:22:OEE_EventFrame/oee_function/app/services/events_delay_service.py
DELAY_CONFIGURATION: List[Dict[str, Any]] = [
    {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c"], "delay_minutes": 480},  # 8 hours
]
```

**Configuration structure:**
- `unit`: Production unit identifier (e.g., "UNT-NANCMP")
- `event_types`: List of event types that should have delay applied (e.g., ["3a", "3b", "3c"])
- `delay_minutes`: Delay time in minutes (e.g., 480 = 8 hours)

**Current configuration:**
- Unit `UNT-NANCMP`: Events `3a`, `3b`, `3c` with delay of 480 minutes (8 hours)

### How It Works

#### Removal/Keep Logic

The service compares the time limit (`end_time + delay_minutes`) with the current time (`current_time`):

**Event will be REMOVED if:**
```
end_time + delay_minutes > current_time
```
Means that not enough time has passed since the event end (still within the delay period).

**Event will be KEPT if:**
```
end_time + delay_minutes <= current_time
```
Means that enough time has passed since the event end (already outside the delay period).

#### Practical Example

Consider the current configuration:
- Unit: `UNT-NANCMP`
- Events: `["3a", "3b", "3c"]`
- Delay: 480 minutes (8 hours)
- Current time: `2024-01-15 14:00:00 UTC`

**Scenario 1: Event will be removed**
- Event `3a` with `end_time = 2024-01-15 10:00:00 UTC`
- Time limit: `10:00 + 480min = 18:00`
- Comparison: `18:00 > 14:00` → **REMOVED** (still within delay)

**Scenario 2: Event will be kept**
- Event `3b` with `end_time = 2024-01-15 05:00:00 UTC`
- Time limit: `05:00 + 480min = 13:00`
- Comparison: `13:00 <= 14:00` → **KEPT** (already passed delay)

### Processing Flow

1. **Initial Validation**:
   - Checks if DataFrame is empty (returns without modifications)
   - Validates if required columns exist: `refUnitId`, `event_definition`, `end_time`
   - If columns are missing, returns original DataFrame without modifications

2. **Preparation**:
   - Creates a copy of DataFrame to avoid modifying the original
   - Gets current time in UTC for comparison
   - Creates an initial mask with all events marked to keep

3. **Rule Processing**:
   - For each rule in `DELAY_CONFIGURATION`:
     - Filters events that match the unit (`refUnitId == unit`)
     - Filters events that match event types (`event_definition in event_types`)
     - Combines masks (unit AND event type)
     - Converts `end_time` to datetime and normalizes to UTC
     - Filters only events with valid `end_time` (not NaT)
     - Calculates time limit: `end_time + delay_minutes`
     - Identifies events that should be removed: `time_limit > current_time`
     - Updates the mask of events to keep

4. **Final Filtering**:
   - Filters DataFrame keeping only events that should not be removed
   - Logs informative messages about how many events were removed

### Required Columns

The input DataFrame must contain the following columns:

- `refUnitId`: Production unit identifier (string)
- `event_definition`: Event type (string)
- `end_time`: Event end date/time (datetime or timestamp)

**Note**: If any of these columns are missing, the service returns the original DataFrame without modifications and logs a warning.

### Usage Examples

#### Example 1: Basic Usage

```python
from app.services.events_delay_service import EventsDelayService
import pandas as pd

# Prepare DataFrame with events
event_frames_df = pd.DataFrame({
    "refUnitId": ["UNT-NANCMP", "UNT-NANCMP", "UNT-NANCMP"],
    "event_definition": ["3a", "3b", "3c"],
    "end_time": [
        pd.Timestamp("2024-01-15 10:00:00", tz="UTC"),  # Will be removed if still within delay
        pd.Timestamp("2024-01-15 05:00:00", tz="UTC"),  # Will be kept if already passed delay
        pd.Timestamp("2024-01-15 13:00:00", tz="UTC"),  # Will be removed if still within delay
    ],
    # ... other columns ...
})

# Create service instance
service = EventsDelayService(event_frames_df)

# Apply delay (removes events that are still within the delay period)
filtered_df = service.apply_delay()

# Check results
print(f"Original events: {len(event_frames_df)}")
print(f"Filtered events: {len(filtered_df)}")
print(f"Removed events: {len(event_frames_df) - len(filtered_df)}")
```

#### Example 2: Unconfigured Events

Events that do not match any delay rule are always kept:

```python
event_frames_df = pd.DataFrame({
    "refUnitId": ["UNT-NANCMP", "UNT-OTHER", "UNT-NANCMP"],
    "event_definition": ["2a", "3a", "1a"],  # 2a not in list, UNT-OTHER not configured
    "end_time": [
        pd.Timestamp("2024-01-15 10:00:00", tz="UTC"),  # Kept (does not match rule)
        pd.Timestamp("2024-01-15 11:00:00", tz="UTC"),  # Kept (unit not configured)
        pd.Timestamp("2024-01-15 12:00:00", tz="UTC"),  # Kept (does not match rule)
    ],
})

service = EventsDelayService(event_frames_df)
filtered_df = service.apply_delay()

# All events will be kept as they do not match configured rules
assert len(filtered_df) == len(event_frames_df)
```

#### Example 3: Multiple Rules

If multiple rules are configured, each event is evaluated against all rules:

```python
# Assuming DELAY_CONFIGURATION contains:
# [
#     {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c"], "delay_minutes": 480},
#     {"unit": "UNT-NANCMP", "event_types": ["1a", "1b"], "delay_minutes": 60},
# ]

event_frames_df = pd.DataFrame({
    "refUnitId": ["UNT-NANCMP", "UNT-NANCMP", "UNT-NANCMP"],
    "event_definition": ["3a", "1a", "2a"],
    "end_time": [
        pd.Timestamp("2024-01-15 10:00:00", tz="UTC"),  # Evaluated by rule 1
        pd.Timestamp("2024-01-15 13:00:00", tz="UTC"),  # Evaluated by rule 2
        pd.Timestamp("2024-01-15 12:00:00", tz="UTC"),  # Does not match any rule
    ],
})

service = EventsDelayService(event_frames_df)
filtered_df = service.apply_delay()
```

### How to Implement

#### 1. Add New Delay Rule

To add a new delay rule, edit the `DELAY_CONFIGURATION` constant:

```python
DELAY_CONFIGURATION: List[Dict[str, Any]] = [
    {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c"], "delay_minutes": 480},
    {"unit": "UNT-FRACMP", "event_types": ["1a", "1b"], "delay_minutes": 120},  # New rule
    {"unit": "UNT-OTHER", "event_types": ["2a", "2b", "2c"], "delay_minutes": 240},  # New rule
]
```

#### 2. Modify Existing Delay

To modify the delay of an existing rule, change the `delay_minutes` value:

```python
DELAY_CONFIGURATION: List[Dict[str, Any]] = [
    {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c"], "delay_minutes": 600},  # Changed from 480 to 600
]
```

#### 3. Add Event Types to a Rule

To add more event types to an existing rule, add them to the `event_types` list:

```python
DELAY_CONFIGURATION: List[Dict[str, Any]] = [
    {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c", "3d", "3e"], "delay_minutes": 480},  # Added 3d and 3e
]
```

### Important Notes

- **Automatic Application**: The service is automatically applied in `EventFrameService` during event processing
- **Timezone**: All date/time operations are normalized to UTC to ensure consistency
- **Index Preservation**: The service preserves the original DataFrame indices during all operations
- **Unconfigured Events**: Events that do not match any delay rule are always kept (not removed)
- **Column Validation**: If required columns do not exist, the service returns the original DataFrame without modifications
- **Empty DataFrame**: If DataFrame is empty, the service returns without modifications
- **Invalid End Time**: Events with invalid `end_time` (NaT) are ignored in delay check, but are not automatically removed
- **Multiple Rules**: If an event matches multiple rules, it will be evaluated by the first matching rule (rule order matters)
- **Performance**: The service uses pandas vectorized operations for better performance with large data volumes
- **Logs**: The service logs informative messages about how many events were removed per rule and in total

### Use Cases

#### Use Case 1: Wait for Confirmation of Critical Events

Some critical events (such as stop events) may need a "cooling" period before being processed, to ensure there are no more pending updates or corrections.

**Example**: Stop events (`3a`, `3b`, `3c`) in unit `UNT-NANCMP` have an 8-hour delay to ensure all information has been updated before processing.

#### Use Case 2: Avoid Premature Processing

Recent events may still be being updated or corrected. The delay ensures that only "stable" events are processed.

**Example**: An event that ended 1 hour ago may still be being updated. With an 8-hour delay, only events that ended more than 8 hours ago are processed.

#### Use Case 3: Synchronization with External Systems

In some cases, it may be necessary to wait for synchronization with external systems before processing events.

**Example**: Waiting 8 hours allows external systems to update information related to events before processing.

