from datetime import datetime, timed<PERSON>ta
from typing import Union, List, Optional

import numpy as np
import pandas as pd
from app.enums.reporting_line import ReportingLineEnum
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.constants import Constants, EventFrameConstants
from app.utils.uom_conversion import lbs_to_kg

from .was.was_general_service import WashingtonGeneralService


class HourlyDataCompounding:
    def __init__(self, reporting_line_external_id: str) -> None:
        self._reporting_line_external_id = reporting_line_external_id

    def identify_events_typeIII(
        self,
        data: pd.DataFrame,
        prod_rate_data: pd.DataFrame,
        mdr: Optional[pd.DataFrame],
        configurations: ReportingSiteConfiguration,
        data_timeseries_configurations: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        identifies events of type III according to the business rules

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        # fill information to avoid wrong calculation

        prod_rate_data.fillna(
            value={
                col: "-"
                for col in prod_rate_data.select_dtypes(
                    include=["object", "string"]
                ).columns
            },
            inplace=True,
        )

        prod_rate_data.fillna(
            value={
                col: 0
                for col in prod_rate_data.select_dtypes(include=["number"]).columns
            },
            inplace=True,
        )

        prod_hourly_data = prod_rate_data.copy()

        prod_hourly_data.reset_index(inplace=True)

        # calculate Running Time using ProductionLineStatus

        prod_hourly_data["running_duration"] = (
            prod_hourly_data["index"].diff(-1).dt.total_seconds().abs()
        )

        if configurations.reporting_site.external_id == "STS-PEN":
            condition_where = prod_hourly_data["ProductionLineStatus"] > 0
        elif configurations.reporting_site.external_id == "STS-FOR":
            condition_where = (prod_hourly_data["ProductionLineStatus"] == 0) & (
                prod_hourly_data["BatchID"].astype(str).str.startswith("000")
            )
        elif configurations.reporting_site.external_id == "STS-FRA":
            condition_where = (prod_hourly_data["ProductionLineStatus"] == "On") & (
                prod_hourly_data["StartingUp"] == "On"
            )
        elif configurations.reporting_site.external_id == "STS-WAS":
            washington_general_service = WashingtonGeneralService(
                reporting_line_external_id=self._reporting_line_external_id, mdr=mdr
            )

            prod_hourly_data = washington_general_service.prepare_hourly_data(
                data=prod_hourly_data,
            )

            condition_where = prod_hourly_data["isRunning"] == True

        else:
            condition_where = prod_hourly_data["ProductionLineStatus"] == "Running"

        # exclude periods with events 2a, 2b, 2c from running time calculation
        data_prod_trial = data[data["event_definition"].isin({"2a", "2b", "2c"})]

        intervals = pd.IntervalIndex.from_arrays(
            data_prod_trial["start_time"], data_prod_trial["end_time"], closed="both"
        )
        timestamps = pd.Series(prod_hourly_data["index"])

        condition_where = condition_where & ~timestamps.apply(
            lambda ts: intervals.contains(ts).any()
        )

        prod_hourly_data["running_duration"] = prod_hourly_data[
            "running_duration"
        ].where(condition_where, 0)
        

        prod_hourly_data["TotalFeed"] = pd.to_numeric(
            prod_hourly_data["TotalFeed"], errors="coerce"
        )

        prod_hourly_data["running_duration"] = pd.to_numeric(
            prod_hourly_data["running_duration"], errors="coerce"
        )

        prod_hourly_data["production"] = (
            prod_hourly_data["TotalFeed"] * prod_hourly_data["running_duration"]
        )

        prod_hourly_data["production"] = prod_hourly_data["production"] / 3600

        # replacing the production column for Newport Site

        if configurations.reporting_site.external_id in ["STS-NPT"]:
            # there is a different method of calculating hourly rate in Newport Site
            prod_hourly_data["production"] = prod_hourly_data["TotalFeed"].copy()

        # creating hourly data for sites with different shifts times
        list_shifts = configurations.shifts.copy()
        flag_classifier = False

        for date_str in list_shifts:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            if date_obj.minute == 30:
                flag_classifier = True
                break

        prod_hourly_data["index_utc"] = prod_hourly_data["index"].dt.tz_convert("UTC")

        if flag_classifier:  # SHIFT BREAK IN 30 MINUTES
            prod_hourly_data["rounded_index"] = prod_hourly_data["index_utc"].dt.round(
                "h"
            )
            prod_hourly_data["start_time"] = prod_hourly_data[
                "rounded_index"
            ] - pd.Timedelta(minutes=30)
            mask = prod_hourly_data["index_utc"].dt.minute == 30
            prod_hourly_data.loc[mask, "start_time"] = (
                prod_hourly_data.loc[mask, "index_utc"].dt.floor("30min")
            )
            prod_hourly_data["start_time"] = prod_hourly_data[
                "start_time"
            ].dt.tz_convert(prod_hourly_data["index"].dt.tz)

        else:
            prod_hourly_data["rounded_index"] = prod_hourly_data["index_utc"].dt.floor(
                "h"
            )
            prod_hourly_data["start_time"] = prod_hourly_data["rounded_index"]
            prod_hourly_data["start_time"] = prod_hourly_data[
                "start_time"
            ].dt.tz_convert(prod_hourly_data["index"].dt.tz)

        prod_hourly_data.drop(
            columns=["index_utc", "rounded_index"], inplace=True
        )
        
        # Special production calculation for WAS Lines
        if self._reporting_line_external_id.startswith("RLN-WASSCD"):
            prod_hourly_data["TotalFeed"] = lbs_to_kg(prod_hourly_data["TotalFeed"])  # convert from lbs to kg
            # Update total_feed_unit to "kg/h" since we converted from lbs to kg
            prod_hourly_data["total_feed_unit"] = "kg/h"
            
            # Group by start_time to calculate production for each hour
            def calculate_wasscd_production(group):
                if len(group) == 0:
                    return group
                
                # Get values at start, end, and max within the hour
                start_value = group.iloc[0]["TotalFeed"]
                end_value = group.iloc[-1]["TotalFeed"]
                max_value = group["TotalFeed"].max()
                
                # Apply the special calculation logic
                if start_value < end_value:
                    production = end_value - start_value
                elif start_value == end_value and start_value == max_value:
                    production = 0
                else:
                    production = max_value - start_value + end_value
                
                # Update production for all rows in this hour group
                group = group.copy()
                group["production"] = production / len(group)  # Distribute evenly among records
                
                return group
            
            prod_hourly_data = prod_hourly_data.groupby("start_time").apply(calculate_wasscd_production).reset_index(drop=True)

        # there is a different method of calculating hourly rate in Newport Site
        if configurations.reporting_site.external_id in ["STS-NPT"]:
            prod_hourly_data = (
                prod_hourly_data.groupby(["start_time", "Product"])
                .agg(
                    {
                        "production": "first",
                        "running_duration": "sum",
                        "BatchID": "first",
                        "ProcessOrder": "first",
                        "ProductDescription": "first",
                    }
                )
                .reset_index()
            )

        else:
            prod_hourly_data = (
                prod_hourly_data.groupby(["start_time", "Product"])
                .agg(
                    {
                        "production": "sum",
                        "running_duration": "sum",
                        "BatchID": "first",
                        "ProcessOrder": "first",
                        "ProductDescription": "first",
                    }
                )
                .reset_index()
            )

        # if there are two products into an interval, it will get the most produced product
        prod_hourly_data = prod_hourly_data.loc[
            prod_hourly_data.groupby("start_time")["production"].idxmax()
        ]

        prod_hourly_data["end_time"] = pd.to_datetime(
            prod_hourly_data["start_time"]
        ) + pd.Timedelta(hours=1)

        prod_hourly_data["total_duration_seconds"] = (
            prod_hourly_data["end_time"] - prod_hourly_data["start_time"]
        ).dt.total_seconds()

        # find the year that is in accordance with the indexed date
        prod_hourly_data["Year"] = prod_hourly_data["start_time"].dt.year

        # find the month that is in accordance with the indexed date
        prod_hourly_data["Month"] = prod_hourly_data["start_time"].dt.month

        # PUT HERE THE EXTERNAL ID IF THE UNIT USE MDRTAG OR PUT IN in Constants.LINES_WITH_MDRTAG
        if configurations.external_id in {
            "OEERSC-STS-FLO",
            "OEERSC-STS-WIN",
            "OEERSC-STS-FOR",
            "OEERSC-STS-FRA",
            "OEERSC-STS-UTZ",
            "OEERSC-STS-PEN",
            "OEERSC-STS-NPT"
        } or self._reporting_line_external_id in Constants.LINES_WITH_MDRTAG:
            if "MdrTag" not in prod_rate_data.columns:
                prod_rate_data["MdrTag"] = 0

            prod_hourly_data = pd.merge(
                prod_hourly_data,
                prod_rate_data.reset_index()[["index", "MdrTag"]],
                left_on="start_time",
                right_on="index",
                how="left",
            )

            prod_hourly_data.drop(columns=["index"], inplace=True)
            prod_hourly_data.rename(columns={"MdrTag": "MDR"}, inplace=True)

        else:
            prod_hourly_data["MDR"] = prod_hourly_data.apply(
                lambda row: HourlyDataCompounding.get_mdr_value(
                    reporting_line=self._reporting_line_external_id,
                    row=row,
                    mdr_data=mdr,
                    data_aux=["unitAvgRate"],
                ),
                axis=1,
            )

        total_feed_filtered = data_timeseries_configurations.loc[
            data_timeseries_configurations["alias"].apply(
                lambda x: (
                    any("TotalFeed" in str(item) for item in x)
                    if isinstance(x, list)
                    else "TotalFeed" in str(x) if pd.notna(x) else False
                )
            ),
            "unit"
        ] if "unit" in data_timeseries_configurations.columns else pd.Series([])
        prod_hourly_data["total_feed_unit"] = (
            total_feed_filtered.iloc[0]
            if not total_feed_filtered.empty and pd.notna(total_feed_filtered.iloc[0])
            else "kg/h"
        )
        
        # For WASSCD lines, ensure total_feed_unit is "kg/h" since we converted from lbs to kg
        if self._reporting_line_external_id.startswith("RLN-WASSCD"):
            prod_hourly_data["total_feed_unit"] = "kg/h"
            data["total_feed_unit"] = "kg/h"

        # calculates hourly rate and rate loss time
        prod_hourly_data["hourly_rate"] = (
            prod_hourly_data["production"] / prod_hourly_data["running_duration"]
        ) * 3600

        prod_hourly_data.loc[
            prod_hourly_data["running_duration"] == 0, "hourly_rate"
        ] = 0

        prod_hourly_data["rate_loss_time_h"] = (
            prod_hourly_data["MDR"] - prod_hourly_data["hourly_rate"]
        ) / (prod_hourly_data["MDR"] / (prod_hourly_data["running_duration"] / 3600))

        prod_hourly_data.loc[prod_hourly_data["MDR"] == 0, "rate_loss_time_h"] = 0

        prod_hourly_data["measurement_unit"] = (
            mdr["refUnitOfMeasurement"][0].get("symbol") if mdr is not None else ""
        )

        # look into the hourly data to get the events for
        # each working shift
        new_events = self._create_working_shifts(
            prod_hourly_data, configurations.shifts
        )

        # complete EventsIDs
        new_events["EventID"] = data["EventID"].max() + list(range(new_events.shape[0]))

        # concatenate events frames and new events
        data = pd.concat([data, new_events])

        # sort data
        data.sort_values(by=["end_time"], inplace=True)

        return data, prod_hourly_data

    def _create_working_shifts(
        self,
        data: pd.DataFrame,
        shifts: list[pd.Timestamp],
    ) -> pd.DataFrame:
        """
        looks into the hourly data information and creates the aggregation for each working shift

        :param data: hourly data dataframe
        :type data: pd.DataFrame
        :return: aggregate data for working shifts
        :rtype: pd.DataFrame
        """

        # Has Normal System Variation Event
        allowed_compounding_lines = [
            # Forli
            "RLN-FORETPQ02",
            "RLN-FORETPQ04",
            "RLN-FORETPQ05",
            "RLN-FORETPQ06",
            "RLN-FORPARD05",
            "RLN-FORPARD06",
            "RLN-FORPA0Z03",
            "RLN-FORPA0Z04",
            "RLN-FORPA0Z05",
            "RLN-FORPA0Z06",
            "RLN-FORPA0Z07",
            "RLN-FORPA0Z08",
            "RLN-FORLFTLF1",
            "RLN-FORLFTLF2",
            "RLN-FORLFTLF3",
            "RLN-FORLFTLF4",
            "RLN-FORLFTLF5",
            "RLN-FORTPEB02",
            "RLN-FORTPEB03",
            "RLN-FORTPEB04",
            "RLN-FORTPEB05",
            # Frankfurt
            "RLN-FRACMP523",
            "RLN-FRACMP528",
            "RLN-FRACMP513",
            "RLN-FRACMP522",
            "RLN-FRACMP511",
            "RLN-FRACMP512",
            "RLN-FRACMP525",
            "RLN-FRACMP524",
            "RLN-FRACMP526",
            "RLN-FRACMP527",
            "RLN-FRACMP521",
            "RLN-FRACMP531",
            # Bishop
            "RLN-BISCMP133",
            "RLN-BISCMPLN2",
            "RLN-BISCMPLN3",
            "RLN-BISCMPO92",
            "RLN-BISCMPN92",
            "RLN-BISCMPL70",
            # Nanjing
            "RLN-NANCELLN1",
            "RLN-NANCELLN2",
            # Washington SCD
            "RLN-WASSCDEXA",
            "RLN-WASSCDEXB",
            "RLN-WASSCDEXC",
            "RLN-WASSCDEXD",
            "RLN-WASSCDEXF",
            # Washington Works
            "RLN-WASMPWZ01",
            "RLN-WASMPWZ02",
            ReportingLineEnum.RLNWASEPCCP6.value,
            ReportingLineEnum.RLNWASEPCCP7.value,
            ReportingLineEnum.RLNWASEPCCP8.value,
            ReportingLineEnum.RLNWASEPCCP9.value,
        ]

        # get the nearest shift start and end concerning the first and last timestamp of
        # the hourly data

        start_time = data.iloc[0, :]["start_time"]
        end_time = data.iloc[-1, :]["end_time"]

        # get 00:00:00 time to remove of shifts list
        shifts = sorted(datetime.strptime(ts, "%Y-%m-%d %H:%M:%S") for ts in shifts)
        midnight = min(shifts, key=lambda dt: dt.time())
        shifts.remove(midnight)

        shift_times = [dt.time() for dt in shifts]

        # Normalize both timestamps to the same timezone to avoid DST issues
        start_time_normalized = start_time.tz_convert("UTC").tz_localize(None)
        end_time_normalized = end_time.tz_convert("UTC").tz_localize(None)

        days = pd.Series(
            pd.date_range(
                start=start_time_normalized, end=end_time_normalized, freq="D"
            )
        )

        # Convert back to the original timezone
        days = days.dt.tz_localize("UTC").dt.tz_convert(start_time.tz)

        ws_shifts = []

        for day in days:
            for i, start_shift_time in enumerate(shift_times):
                end_shift_time = shift_times[(i + 1) % len(shift_times)]
                start_dt = pd.Timestamp.combine(day, start_shift_time).tz_localize(
                    day.tz
                )
                end_dt = pd.Timestamp.combine(day, end_shift_time).tz_localize(day.tz)

                if end_dt <= start_dt:
                    end_dt += timedelta(days=1)

                ws_shifts.append((start_dt, end_dt))

        ws_events = pd.DataFrame(ws_shifts, columns=["start_time", "end_time"])

        ws_events = ws_events.loc[
            (ws_events["start_time"] >= start_time)
            & (ws_events["end_time"] <= end_time)
        ].reset_index(drop=True)

        # aggregate hourly data into working shifts
        ws_events["total_duration_seconds"] = ws_events.apply(
            self._aggregate_hourly_data, hourly_data=data, axis=1
        )

        # aggregate production into working shifts
        ws_events["NetProduction"] = ws_events.apply(
            self._aggregate_production, hourly_data=data, axis=1
        )

        # aggregate production into working shifts
        ws_events["running_time"] = ws_events.apply(
            self._aggregate_running_duration, hourly_data=data, axis=1
        )

        if self._reporting_line_external_id in allowed_compounding_lines:
            ws_events = self._assign_event_definition(ws_events)
        else:
            # correct Production Events Descriptions
            ws_events["event_definition"] = ws_events["total_duration_seconds"].apply(
                lambda x: "3a" if x > 0 else ("3b" if x < 0 else None)
            )

        # drop shift events with duration zero (event definition = None)
        ws_events.dropna(inplace=True)

        cols_to_get = [
            EventFrameConstants.PRODUCT,
            EventFrameConstants.PRODUCT_DESCRIPTION,
            EventFrameConstants.BATCH_ID,
            EventFrameConstants.PROCESS_ORDER,
            EventFrameConstants.MDR,
            EventFrameConstants.TOTAL_FEED_UNIT,
        ]

        ws_events[cols_to_get] = ws_events.apply(
            lambda row: self._get_first_valid_row(
                data, row["start_time"], row["end_time"], cols_to_get
            ),
            axis=1,
        )

        return ws_events

    def _get_first_valid_row(self, data, start, end, cols):
        """
        retrieves the first valid row from the data within the given start and end time

        :param data: the input DataFrame
        :type data: pd.DataFrame
        :param start: the start time for the search
        :type start: pd.Timestamp
        :param end: the end time for the search
        :type end: pd.Timestamp
        :param cols: the columns to retrieve from the valid row
        :type cols: list
        :return: the first valid row from the data
        :rtype: pd.Series
        """

        mask = (
            (data["start_time"] >= start)
            & (data["start_time"] <= end)
            & (data["running_duration"] != 0)
        )
        valid_rows = data.loc[mask]

        if not valid_rows.empty:
            return valid_rows.iloc[0][cols]
        else:
            return data.loc[data["start_time"] == start][cols]

    def _assign_event_definition(self, ws_events: pd.DataFrame):
        """
        This function receives a DataFrame ws_events and creates the 'event_definition' column based on the
        'total_duration_seconds' column according to the following rules:

        - If the value is between 0 (exclusive) and 15 minutes (inclusive): '3a'
        - If the value is between -15 minutes (inclusive) and 0 (exclusive): '3b'
        - If the value is positive and greater than 15 minutes: '3c'
        - If the value is negative and less than -15 minutes: '3d'

        Note: total_duration_seconds is in seconds, so the thresholds are in seconds (15 minutes = 900 seconds).
        Values that do not meet any of these conditions will receive the default value (None).
        """
        # Convert 15 minutes to seconds
        minutes_to_seconds = 15 * 60  # 900 seconds

        cond_3a = (ws_events["total_duration_seconds"] > 0) & (
            ws_events["total_duration_seconds"] <= minutes_to_seconds
        )

        cond_3b = (ws_events["total_duration_seconds"] < 0) & (
            ws_events["total_duration_seconds"] >= -minutes_to_seconds
        )

        cond_3c = ws_events["total_duration_seconds"] > minutes_to_seconds

        cond_3d = ws_events["total_duration_seconds"] < -minutes_to_seconds

        conditions = [cond_3a, cond_3b, cond_3c, cond_3d]
        choices = ["3a", "3b", "3c", "3d"]

        ws_events["event_definition"] = np.select(conditions, choices, default=None)

        return ws_events

    def _aggregate_hourly_data(
        self, row: pd.DataFrame, hourly_data: pd.DataFrame
    ) -> float:
        """
        aggregates the rate loss time for the entire working shift

        :param row: row with the start and end time of the working shifts
        :type row: pd.DataFrame
        :param hourly_data: data with hourly rate loss time
        :type hourly_data: pd.DataFrame
        :return: aggregation of the rate loss time for each working shift
        :rtype: float
        """

        # get start and end of the shift
        start = row["start_time"]
        end = row["end_time"]

        # filter the hourly data and sum the rate loss time
        rlt = hourly_data.loc[
            ((hourly_data["start_time"] >= start) & (hourly_data["end_time"] <= end)),
            "rate_loss_time_h",
        ].sum()

        return rlt * 3600

    def _aggregate_production(self, row: pd.Series, hourly_data: pd.DataFrame) -> float:
        """
        Aggregates the production for the entire working shift.

        :param row: row with the start and end time of the working shift
        :type row: pd.Series
        :param hourly_data: data with hourly production values
        :type hourly_data: pd.DataFrame
        :return: total production for the shift
        :rtype: float
        """
        start = row["start_time"]
        end = row["end_time"]

        production_sum = hourly_data.loc[
            (hourly_data["start_time"] >= start) & (hourly_data["end_time"] <= end),
            "production",
        ].sum()

        return production_sum

    def _aggregate_running_duration(
        self, row: pd.Series, hourly_data: pd.DataFrame
    ) -> float:
        """
        Aggregates the running duration for the entire working shift.

        :param row: row with the start and end time of the working shift
        :type row: pd.Series
        :param hourly_data: data with hourly running duration values
        :type hourly_data: pd.DataFrame
        :return: total running duration for the shift
        :rtype: float
        """
        start = row["start_time"]
        end = row["end_time"]

        production_sum = hourly_data.loc[
            (hourly_data["start_time"] >= start) & (hourly_data["end_time"] <= end),
            "running_duration",
        ].sum()

        production_sum /= 3600

        return production_sum

    @staticmethod
    def get_mdr_value(
        reporting_line: str, 
        row: pd.Series, 
        mdr_data: pd.DataFrame, 
        data_aux: List[str],
    ) -> Union[float, pd.Series]:
        """
        retrieves the MDR from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: MDR value
        :rtype: float
        """
        # extract filter parameter
        prod_id = row["Product"]
        mdr_product = HourlyDataCompounding._get_mdr_product(mdr_data=mdr_data)
        start_time_year = row["start_time"].year
        start_time_month = row["start_time"].month

        # create reference date to find BBCT
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # first filter - filter by year and month of the start date and
        # by the productid
        filter_1 = (
            (mdr_product == prod_id)
            & (mdr_data["reportingLineExternalId"] == reporting_line)
            & (mdr_data["year"] == start_time_year)
            & (mdr_data["month"] == start_time_month)
        )
        aux = mdr_data.loc[filter_1, :]

        # if aux is empty, we need to test the second filter
        if aux.shape[0] == 0:
            filter_2 = (mdr_product.str.lower() == prod_id.lower()) & (
                mdr_data["reportingLineExternalId"] == reporting_line
            )
            aux = mdr_data.loc[filter_2, :]

            # if still the aux is empty, we need to test the third filter
            if aux.shape[0] == 0:
                # third filter - check if reporting_line is in LINES_WITH_MDR_EQUALS_FOR_ALL_PRODUCTS
                if reporting_line in Constants.LINES_WITH_MDR_EQUALS_FOR_ALL_PRODUCTS:
                    # filter by reporting_line and find the last record with year/month <= start_time
                    filter_3 = (
                        mdr_data["reportingLineExternalId"] == reporting_line
                    ) & (
                        (mdr_data["year"] < start_time_year)
                        | (
                            (mdr_data["year"] == start_time_year)
                            & (mdr_data["month"] <= start_time_month)
                        )
                    )
                    aux = mdr_data.loc[filter_3, :]

                    # if still empty, return 0
                    if aux.shape[0] == 0:
                        return 0 if isinstance(data_aux, str) else pd.Series([0] * len(data_aux))

                    # sort by year and month descending to get the most recent record
                    aux = aux.sort_values(by=["year", "month"], ascending=False)
                    # get only the first (most recent) record
                    aux = aux.head(1)
                else:
                    # if reporting_line is not in constant, return 0
                    return 0 if isinstance(data_aux, str) else pd.Series([0] * len(data_aux))

            # ensure ordering by the most recent year, considering the event start date
            t = (aux["timestamp"] - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # fill values to get the most recent date preceding the date of event
            aux.ffill(inplace=True)
            aux.fillna({"unitAvgRate": 0}, inplace=True)

            # ensure ordering by the most recent year, considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # extract value of MDR
        return aux[data_aux].head(1).values[0]

    @staticmethod
    def _get_mdr_product(mdr_data: pd.DataFrame):
        if mdr_data["productId"] is not None:
            return mdr_data["productId"]
        else:
            material = mdr_data["refMaterial"]

            if not material.empty and not material.isna().all():
                return pd.Series(
                    [item["name"] for item in material if isinstance(item, dict)]
                )
