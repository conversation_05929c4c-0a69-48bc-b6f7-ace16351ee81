import os
from typing import Literal, Optional

from cognite.client.data_classes.data_modeling import DataModelId
from dotenv import load_dotenv
from pydantic_settings import BaseSettings, SettingsConfigDict

# get or define environment
python_env = os.getenv("PYTHON_ENV") or "dev"

ENV_FILE_LIST = (".env", ".env." + python_env)


CogniteProjects = Literal["celanese-dev", "celanese-stg", "celanese"]


class AuthVariables(BaseSettings):
    """
    class to store authentication variables
    inherits BaseSettings from pydantic-settings
    """

    model_config = SettingsConfigDict(
        env_file=ENV_FILE_LIST, env_prefix="auth_", extra="ignore"
    )

    client_id: str
    tenant_id: str
    secret: str
    scopes: str
    token_uri: str
    token_override: Optional[str] = None


class CogniteVariables(BaseSettings):
    """
    class to store cognite variables
    inherits BaseSettings from pydantic-settings
    """

    model_config = SettingsConfigDict(
        env_file=ENV_FILE_LIST, env_prefix="cognite_", extra="ignore"
    )
    base_uri: str
    client_name: str
    data_model_space: str
    data_model_external_id: str
    data_model_version: str
    default_data_model_instances_space: str
    asset_hierarchy_instances_space: str
    project: CogniteProjects

    def get_data_model_id(self) -> DataModelId:
        return DataModelId(
            self.data_model_space,
            self.data_model_external_id,
            self.data_model_version,
        )


class EnvVariables:
    """
    class to store all Environment variables
    """

    def __init__(self) -> None:
        try:
            load_dotenv(override=True)
        finally:
            self.auth = AuthVariables()
            self.cognite = CogniteVariables()
