from typing import Optional

from pydantic import Field

from .node import Node


class Product(Node):
    name: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)
    ref_region: Optional[Node] = Field(default=None, alias="refRegion")
    ref_country: Optional[Node] = Field(default=None, alias="refCountry")
    ref_site: Optional[Node] = Field(default=None, alias="refSite")
