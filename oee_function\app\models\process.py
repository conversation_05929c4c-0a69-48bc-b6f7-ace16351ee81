from typing import Optional
from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, ViewId


class OeeProcess:
    def __init__(self, 
                 space: str, 
                 external_id: str, 
                 name: str, 
                 category: str, 
                 is_active: bool,
                 ref_oee_process: Optional[dict[str, str]] = None 
                 ):
        self.space = space
        self.external_id = external_id
        self.name = name
        self.category = category
        self.is_active = is_active
        self.ref_oee_process = ref_oee_process 

    def _extract_properties_without_identifier(
        self, by_alias: bool = True, keys_to_exclude: Optional[list[str]] = None
    ):
        properties = {
            "name": self.name,
            "OEECategory": self.category,
            "isActive": self.is_active,
        }

        # Adicionar refOEEProcess, se disponível
        if self.ref_oee_process:
            properties["refOEEProcess"] = {
                "externalId": self.ref_oee_process,
                "space": "INO-COR-ALL-DML",
            }

        final_keys_to_exclude = {"externalId", "space"}
        if keys_to_exclude:
            final_keys_to_exclude.update(keys_to_exclude)

        return {k: v for k, v in properties.items() if k not in final_keys_to_exclude}

    def convert_to_cognite_node(
        self,
        view_id: ViewId,
        by_alias: bool = True,
        keys_to_exclude: Optional[list[str]] = None,
    ) -> NodeApply:
        return NodeApply(
            space=self.space,
            external_id=self.external_id,
            sources=[
                NodeOrEdgeData(
                    source=view_id,
                    properties=self._extract_properties_without_identifier(
                        by_alias=by_alias, keys_to_exclude=keys_to_exclude
                    ),
                )
            ],
        )