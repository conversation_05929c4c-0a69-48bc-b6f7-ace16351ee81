from typing import Any
import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.event_detection_utils import (
    detect_sustained_transition,
    detect_sustained_transition_between_boundries,
)
from app.utils.data_frame import reset_index, set_column_as_index


class EdaEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._batch_idle_end = pd.Series(dtype=bool)
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1c": self.identify_events_typeIc,
            "2a": self.identify_events_typeIIa,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # Start trigger
        # ProductionLineStatus goes from
        # ProductionLineStatus > 700 to
        # 700 >= ProductionLineStatus >= 10 for at least 3 minutes
        event1a_start = detect_sustained_transition_between_boundries(
            df=data,
            col="ProductionLineStatus",
            lower_condition="gte",
            lower_boundry=10,
            upper_condition="lte",
            upper_boundry=700,
            direction="desc",
            duration="3min",
        )

        # End trigger
        # ProductionLineStatus <= 10 OR ProductionLineStatus > 700 for at least 3 minutes
        lower_than_ten = (data["ProductionLineStatus"].shift() > 10) & (
            data["ProductionLineStatus"] <= 10
        )
        greater_than_seven_hundred = detect_sustained_transition(
            data, "ProductionLineStatus", 700, "3min", "gt"
        )
        event1a_end = lower_than_ten | greater_than_seven_hundred

        data = data.assign(event1a_start=event1a_start, event1a_end=event1a_end)
        reset_index(data)

        return data

    def identify_events_typeIc(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies events of type Ic.
        """
        # Make sure that the index is set to te desired column
        set_column_as_index(df=data, col="index")

        # Start trigger
        # ProductionLineStatus <= 10
        event1c_start = (data["ProductionLineStatus"].shift() >= 10) & (
            data["ProductionLineStatus"] < 10
        )

        # End trigger
        # ProductionLineStatus changes from < 10 to >= 10 and sustains for at least 3 minutes
        event1c_end = detect_sustained_transition(
            df=data,
            col="ProductionLineStatus",
            threshold=10,
            duration="3min",
            condition="gte",
        )

        data = data.assign(event1c_start=event1c_start, event1c_end=event1c_end)
        reset_index(data)

        return data

    def identify_events_typeIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies events type IIa
        """
        # Start trigger
        # ProductionLineStatus changes from < 10 to >= 10 and sustains for at least 3 minutes
        event2a_start = detect_sustained_transition(
            df=data,
            col="ProductionLineStatus",
            threshold=10,
            duration="3min",
            condition="gte",
        )

        lower_than_ten = (data["ProductionLineStatus"].shift() > 10) & (
            data["ProductionLineStatus"] <= 10
        )
        greater_than_seven_hundred = (data["ProductionLineStatus"].shift() <= 700) & (
            data["ProductionLineStatus"] > 700
        )
        event2a_end = lower_than_ten | greater_than_seven_hundred

        data = data.assign(event2a_start=event2a_start, event2a_end=event2a_end)
        reset_index(data)

        return data
