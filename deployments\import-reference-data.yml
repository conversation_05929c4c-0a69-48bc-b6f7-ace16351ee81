trigger:
  branches:
    include:
      - dev
      - qa
      - prod
  paths:
    include:
      - scripts/deploy.py
      - scripts/import_reference_data.py
      - scripts/data

pool:
  name: "Azure Pipelines"

variables:
  - group: oee-cicd
  - name: pythonEnv
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: dev
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: qa
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: prod
    ${{ else }}:
      value: dev
  - name: celaneseProject
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: celanese-stg
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: celanese-dev
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: celanese
    ${{ else }}:
      value: celanese-dev
  - name: authClientId
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(AUTH_CLIENT_ID_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(AUTH_CLIENT_ID_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(AUTH_CLIENT_ID_PROD)
    ${{ else }}:
      value: $(AUTH_CLIENT_ID_DEV)
  - name: authSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(S_AUTH_CLIENT_SECRET_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(S_AUTH_CLIENT_SECRET_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(S_AUTH_CLIENT_SECRET_PROD)
    ${{ else }}:
      value: $(S_AUTH_CLIENT_SECRET_DEV)
  - name: cicdAuthClientId
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(CICD_AUTH_CLIENT_ID_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(CICD_AUTH_CLIENT_ID_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(CICD_AUTH_CLIENT_ID_PROD)
    ${{ else }}:
      value: $(CICD_AUTH_CLIENT_ID_DEV)
  - name: cicdAuthSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(S_CICD_AUTH_CLIENT_SECRET_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(S_CICD_AUTH_CLIENT_SECRET_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(S_CICD_AUTH_CLIENT_SECRET_PROD)
    ${{ else }}:
      value: $(S_CICD_AUTH_CLIENT_SECRET_DEV)

stages:
  - stage: ImportReferenceData
    displayName: "Import Reference Data to Cognite for ${{ variables.celaneseProject }} Project"
    jobs:
      - job: ImportReferenceData
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: "3.11.5"
            displayName: "Use python 3.11"

          - script: |
              cd $(System.DefaultWorkingDirectory)/scripts
              python -m pip install --upgrade pip
              pip install -r requirements.txt
            displayName: "Install dependencies"

          - script: |
              cd $(System.DefaultWorkingDirectory)/scripts
              python -m deploy import-reference-data
            displayName: "Import reference data"
            env:
              PYTHON_ENV: ${{ variables.pythonEnv }}
              AUTH_CICD_CLIENT_ID: ${{ variables.cicdAuthClientId }}
              AUTH_CICD_SECRET: ${{ variables.cicdAuthSecret }}
              AUTH_SECRET: ${{ variables.authSecret }}
              AUTH_CLIENT_ID: ${{ variables.authClientId }}
