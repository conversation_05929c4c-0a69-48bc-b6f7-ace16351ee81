import logging
import os
import sys
import pandas as pd
from cognite.client.data_classes.data_modeling import NodeApply, NodeOrEdgeData, NodeId

"""
    ATTENTION: This script will  perform a retroactive update of events fields as AssignedHours and AssignedLostProductionMT into EventDetails for automatic assigned events.
"""


# Set script path to find import packages
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

# Import classes and repositories from oee_function
from cognite.client.data_classes.data_modeling.data_models import DataModelId, ViewId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables
from oee_function.app.infra.logger_adapter import get_logger
from oee_function.app.repositories.view_repository import ViewRepository
import oee_function.app.utils.graphql as graphqlClient

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
variables = EnvVariables()
cognite_client = CogniteClientFactory.create(variables)
data_model_id = DataModelId(
    variables.cognite.data_model_space,
    variables.cognite.data_model_external_id,
    variables.cognite.data_model_version,
)


def oee_event_query():
    return """
    query GetOEEEvent($filter: _ListOEEEventFilter, $after: String) {
        listOEEEvent(first: 300,filter: $filter,after: $after) {
            items {
                externalId
                space
                totalDuration
                lostProductionMT
                refOEEEventDetail{
                    items{
                        externalId
                        space
                    }
                }
            }
            pageInfo {
                endCursor
                hasNextPage
            }
        }
    }
    """


def get_all_automatic_assigned_events():
    logger.info("Getting all automatic assigned events")
    filter_obj = {
        "and": [
            {"status": {"eq": "Assigned"}},
            {"createdBy": {"isNull": True}},
            {"refCreatedBy": {"externalId": {"isNull": True}}},
            {"refModifiedBy": {"externalId": {"isNull": True}}},
            {"modifiedBy": {"isNull": True}},
            {"or": [{"isManual": {"isNull": True}}, {"isManual": {"eq": False}}]},
        ]
    }
    query = oee_event_query()
    items = graphqlClient.query_all(
        cognite_client, data_model_id, query, "listOEEEvent", filter_obj
    )
    return items


def upsert_oee_event_detail(events):
    logger.info("Upserting data into event details of automatic assigned events")
    views = cognite_client.data_modeling.views.list(
        limit=-1, space=variables.cognite.data_model_space
    )
    view = [v for v in views if v.external_id == "OEEEventDetail"]
    view_id = view[0].as_id()
    total = 0
    for event in events:
        if event.get("refOEEEventDetail") and len(event["refOEEEventDetail"]) == 1:
            event_detail = event["refOEEEventDetail"][0]
            props = {
                "assignedHours": (
                    event["totalDuration"] if event["totalDuration"] else 0
                ),
                "assignedLostProductionMT": (
                    event["lostProductionMT"] if event["lostProductionMT"] else 0
                ),
            }
            instance_node = NodeApply(
                space=event_detail["space"],
                external_id=event_detail["externalId"],
                sources=[NodeOrEdgeData(view_id, props)],
            )
            cognite_client.data_modeling.instances.apply(instance_node)
            total = total + 1

    logger.info(f"{total} instaces of event details were updated.")


def run():
    """
    ATTENTION: This script will perform a retroactive update for all OEEEventDetail for automatic assigned events.
    """

    oee_event_data = get_all_automatic_assigned_events()
    upsert_oee_event_detail(oee_event_data)


if __name__ == "__main__":
    run()
