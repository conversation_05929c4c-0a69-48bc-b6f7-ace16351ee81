from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.enums.nan.batch_hold import BatchHoldEnum
from app.models.lead_product import LeadProductBbct

class NanBatchEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        bbct: pd.DataFrame,
    ) -> None:
        super().__init__()
        if bbct is not None:
            self._valid_prod_ids = list(bbct.productId.unique())
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # event trigger start - ProductionLineStatus = 0 (Inactive)
        data = data.assign(
            event1a_start=(
                (data["Product"] == '')
                & (data["BatchID"] == '')
            )
        )

        # event trigger end - ProductionLineStatus = 1 (Active)
        data = data.assign(
            event1a_end=(
                (data["Product"] != '')
                & (data["BatchID"] != '')
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        ).assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ib

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # event trigger start - Product AND ProductID = ''
        data = data.assign(
            event1b_start=(
                (data["BatchHold"] == BatchHoldEnum.HOLD.value)
            )
        )

        # event trigger start - Product AND ProductID != ''
        data = data.assign(
            event1b_end=(
                (data["BatchHold"] != BatchHoldEnum.HOLD.value)
            )
        )

        # correct start and end flags
        data = data.assign(
            event1b_start=(
                (data["event1b_start"] == True)
                & (data["event1b_start"].shift(1) != True)
            )
        ).assign(
            event1b_end=(
                (data["event1b_end"] == True)
                & (data["event1b_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    #Product Trial
    def identify_events_typeIIa(self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # Assign event2a_start using shift for previous row comparison
        data = data.assign(
            event2a_start=(
                (data["InitPhaseActive"] == 1)
                & (data["BatchID"] != '')
                & (~data["Product"].isin(self._valid_prod_ids))
                & ((data["InitPhaseActive"].shift(1) != 1) | (data["Product"].shift(1).isin(self._valid_prod_ids)))
            )
        )

        # Assign event2a_end using shift for previous row comparison
        data = data.assign(
            event2a_end=(
                (
                    (data["ReleasePhaseActive"] == 1)
                    & (data["BatchID"] == '')
                    & (~data["BatchID"].shift(1).isna())
                ) | (
                    (data["Product"].isin(self._valid_prod_ids))
                    & (~data["Product"].shift(1).isin(self._valid_prod_ids))
                )
            )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIIb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type III event
        :rtype: pl.DataFrame
        """
        data = data.assign(
            event3b_start=(
                (data["InitPhaseActive"] == 1)
                & (data["BatchID"] != '')
                & (data["Product"].isin(self._valid_prod_ids))
            )
        )

        data = data.assign(
            event3b_end=(
                (
                    (data["ReleasePhaseActive"] == 1)
                    & (data["BatchID"] == '')
                )
            )
        )

        return data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type III event
        :rtype: pl.DataFrame
        """
        data = data.assign(
            event3c_start=(
                (data["InitPhaseActive"] == 1)
                & (data["BatchID"] != '')
                & (data["Product"].isin(self._valid_prod_ids))
            )
        )

        data = data.assign(
            event3c_end=(
                (
                    (data["ReleasePhaseActive"] == 1)
                    & (data["BatchID"] == '')
                )
            )
        )

        return data

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def get_bbct_value(self, row: pd.Series, bbct_data: pd.DataFrame) -> tuple[float, float]:
        """
        retrieves the BBCT from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: Tuple with BBCT and leadBBCT values
        :rtype: tuple[float, float]
        """
        # extract filter parameter
        prod_id = row["Product"]
        event = row["event_definition"]
        start_time_year = row["start_time"].year
        start_time_month = row["start_time"].month

        # create reference date to find BBCT
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # if the event does not depend on BBCT values, return 0
        if not self._reporting_site_configuration.event_dependends_on_bbct(
            self._reporting_line_external_id,
            event,
        ):
            return 0, 0

        # first filter - filter by year and month of the start date and
        # by the productid
        filter_1 = (
            (bbct_data["productId"] == prod_id)
            & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            & (bbct_data["year"] == start_time_year)
            & (bbct_data["month"] == start_time_month)
        )
        aux = bbct_data.loc[filter_1, :]

        # if aux is empty, we need to test the second filter
        if aux.shape[0] == 0:
            filter_2 = (bbct_data["productId"].str.lower() == prod_id.lower()) & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            aux = bbct_data.loc[filter_2, :]

            # if still the aux is empty, then we don't have matches, return 0
            if aux.shape[0] == 0:
                return 0, 0

            # ensure ordering by the most recent year, considering the event start date
            t = (aux["timestamp"] - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # fill values to get the most recent date preceding the date of event
            aux.fillna(method="ffill", inplace=True)
            aux["bbct"].fillna(0, inplace=True)
            aux["leadBBCT"].fillna(0, inplace=True)

            # ensure ordering by the most recent year, considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # extract value of BBCT
        return aux["bbct"].head(1).values[0], aux["leadBBCT"].head(1).values[0]

    def fix_3b_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame, lead_bbct: LeadProductBbct
    ) -> pd.DataFrame:
        """
        applies the business that demands subtraction of the
        Best Batch Cycle time from every event of type 3b

        :param data: event data
        :type data: pd.DataFrame
        :return: data with the right duration of the events
        :rtype: pd.DataFrame
        """

        # apply extraction of the BBCT value
        data[["bbct", "leadBBCT"]] = pd.DataFrame(
            data.apply(lambda row: self.get_bbct_value(row, bbct_data), axis=1).tolist(),
            index=data.index
        )

        # fix events 3b duration (only where event_definition == "3b")
        data.loc[data["event_definition"] == "3b", "total_duration_seconds"] -= data["bbct"]

        # fix events 3c duration (apply only if leadBBCT != bbct, otherwise set to 0)
        mask_3c = (data["event_definition"] == "3c") & (data["bbct"] != data["leadBBCT"])
        data.loc[mask_3c, "total_duration_seconds"] = data["bbct"] - data["leadBBCT"]
        data.loc[~mask_3c & (data["event_definition"] == "3c"), "total_duration_seconds"] = 0

        # Set total_duration_seconds to 0 when event is 3c and condition is not met
        data.loc[data["event_definition"] == "3c", "total_duration_seconds"] = data["total_duration_seconds"].fillna(0)

        # fill NaN values with 0 when data does not have BBCT
        data["total_duration_seconds"].fillna(0, inplace=True)

        # events which products does not have bbct is reported with duration zero
        data.loc[
            ((data["event_definition"] == "3b") & (data["bbct"] == 0)),
            "total_duration_seconds",
        ] = 0

        data = data.query("total_duration_seconds != 0")
        
        # drop created column
        data.drop(columns=["bbct"], inplace=True)

        return data