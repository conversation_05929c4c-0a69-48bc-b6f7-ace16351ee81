# Índice / Index

- [Seção em Português](#seção-em-português)
- [English Section](#english-section)

# Seção em Português

## Serviço de Producing Waste by Quality Control

Este documento descreve o funcionamento do serviço `ProducingWasteByQualityControlService`, responsável por identificar eventos diários de "Producing Waste by Quality Control" (Produção de Resíduos por Controle de Qualidade) baseado em tags de contadores cumulativos.

### Visão Geral

O serviço processa séries temporais de tags cumulativas (como `DivertTotalizer`) para identificar eventos diários de produção de resíduos. A lógica principal é:

- **Calcula incrementos diários** de cada tag de contador, considerando resets (quando o contador volta a zero)
- **Soma os incrementos** de todas as tags para obter um valor total diário de scrap
- **Cria eventos diários** (de 00:00 até 00:00 do dia seguinte) sempre que o valor diário total for maior que zero
- **Calcula duração** dos eventos baseado no MDR (Material Data Rate) do lead product

### Aplicação do Serviço

O serviço é utilizado no `WASSCDEventIdentificationService` para identificar eventos do tipo **4c** (Producing Waste by Quality Control) na unidade SCD (Washington Works).

A aplicação ocorre no método `identify_events_typeIVc` do `WASSCDEventIdentificationService`:

```569:609:OEE_EventFrame/oee_function/app/services/event_identification/was/was_scd_event_identification_service.py
def identify_events_typeIVc(
    self, data: pd.DataFrame, **args
) -> pd.DataFrame:
    """
    Identify Producing Waste by Quality Control events (type IVc).
    ...
    """
    data_copy = data.copy()
    producing_waste_tags = ["DivertTotalizer"]
    
    data_result = ProducingWasteByQualityControlService.process(
        data_copy,
        producing_waste_tags,
        self._mdr,
        self._lead_product,
        "4c",
    )
    
    return data_result
```

### Como Funciona

#### Lógica de Cálculo de Incrementos

O serviço calcula quanto cada tag **aumentou efetivamente** em cada dia, considerando:

1. **Série cumulativa**: A tag mantém um valor cumulativo que aumenta ao longo do tempo
2. **Cálculo de incremento por amostra**: Para cada amostra, calcula o incremento positivo em relação ao valor anterior
3. **Tratamento de resets**: Quando há reset (valor atual < valor anterior), o incremento daquele ponto é 0; os novos incrementos após o reset são contabilizados normalmente
4. **Agrupamento por dia**: Os incrementos são calculados dentro de cada dia (normalizado), garantindo que o "valor inicial do dia" seja a referência
5. **Soma diária**: A soma dos incrementos dentro de um dia representa o "scrap" daquele dia

#### Cálculo de Duração

A duração do evento é calculada usando a fórmula:

```
duration_seconds = (scrap_total_day / MDR) * 3600
```

Onde:
- `scrap_total_day`: Total de scrap do dia (soma de todas as tags)
- `MDR`: Material Data Rate do lead product (obtido do MDR DataFrame)
- `3600`: Conversão de horas para segundos

**Observação**: Se o MDR não estiver disponível ou for inválido (NaN ou <= 0), a duração será 0.

#### Exemplo Prático

Considere uma tag `DivertTotalizer` com os seguintes valores diários máximos:

**Cenário:**
- Dia 02/10: valor máximo = 0
- Dia 03/10: valor máximo = 105
- Dia 04/10: valor máximo = 112
- Dia 05/10: valor máximo = 153
- Após 06/10: sem aumentos

**Cálculo de scrap diário:**
- Dia 02/10: scrap = 0 (sem aumento)
- Dia 03/10: scrap = 105 (105 - 0)
- Dia 04/10: scrap = 7 (112 - 105)
- Dia 05/10: scrap = 41 (153 - 112)
- Após 06/10: scrap = 0 (sem aumento)

**Eventos criados:**
- Evento 1: 03/10 00:00 até 04/10 00:00 (scrap = 105)
- Evento 2: 04/10 00:00 até 05/10 00:00 (scrap = 7)
- Evento 3: 05/10 00:00 até 06/10 00:00 (scrap = 41)

**Cálculo de duração (exemplo com MDR = 100 kg/h):**
- Evento 1: (105 / 100) * 3600 = 3780 segundos (1.05 horas)
- Evento 2: (7 / 100) * 3600 = 252 segundos (0.07 horas)
- Evento 3: (41 / 100) * 3600 = 1476 segundos (0.41 horas)

### Fluxo de Processamento

1. **Validação Inicial**:
   - Verifica se a coluna "index" existe (retorna sem modificações se não existir)
   - Cria uma cópia do DataFrame para não modificar o original

2. **Preparação**:
   - Ordena o DataFrame por timestamp (coluna "index")
   - Inicializa colunas de evento: `event{event_number_type}_start` e `event{event_number_type}_end`
   - Inicializa colunas de métricas: `ProducingWasteDuration`, `TotalProducingWaste`, `total_duration_seconds`, `MDR`
   - Cria coluna auxiliar `event_date` com data normalizada (dia)

3. **Cálculo de Scrap por Tag**:
   - Para cada tag em `producing_waste_tags`:
     - Garante que a tag existe no DataFrame
     - Converte valores para numérico (trata erros como NaN e preenche com 0)
     - Calcula incrementos por dia usando `groupby(event_date).diff()`
     - Aplica `clip(lower=0)` para considerar apenas incrementos positivos
     - Soma os incrementos diários para obter o scrap diário da tag
     - Acumula o scrap diário da tag no total diário

4. **Identificação de Dias com Eventos**:
   - Identifica dias onde `daily_scrap_total > 0`
   - Se nenhum dia tiver scrap, retorna DataFrame sem eventos (mas preenche MDR)

5. **Criação de Eventos**:
   - Para cada dia com scrap > 0:
     - Calcula o scrap total do dia
     - Calcula a duração em segundos usando MDR
     - Identifica início do evento: primeira amostra do dia (ordenada por timestamp)
     - Identifica fim do evento: primeira amostra do dia seguinte (se existir), caso contrário última amostra do dia atual
     - Marca início e fim do evento nas colunas correspondentes
     - Preenche métricas (TotalProducingWaste, ProducingWasteDuration, MDR) nas linhas de start/end

6. **Limpeza e Filtragem**:
   - Remove colunas auxiliares (`__delta_*`, `event_date`)
   - Garante somente primeira ocorrência de start/end usando `keep_only_first_occurrence`
   - Filtra apenas linhas onde há início ou fim de evento
   - Remove duplicatas por dia normalizado (resolve problema de fuso horário)
   - Normaliza timestamps para 00:00 do dia correspondente (mantém fuso horário)
   - Remove primeira e última linha por segurança

### Colunas Necessárias

O DataFrame de entrada deve conter as seguintes colunas:

- `index`: Timestamp das amostras (datetime ou timestamp)
- Colunas de tags: Nomes das tags especificadas em `producing_waste_tags` (ex: `DivertTotalizer`)

**Observação**: Se a coluna "index" não existir, o serviço retorna o DataFrame original sem modificações.

### Colunas Geradas

O DataFrame de saída contém as seguintes colunas:

- `index`: Timestamp normalizado para 00:00 do dia (datetime)
- `event{event_number_type}_start`: Boolean indicando início do evento (ex: `event4c_start`)
- `event{event_number_type}_end`: Boolean indicando fim do evento (ex: `event4c_end`)
- `TotalProducingWaste`: Total de scrap do dia (float)
- `ProducingWasteDuration`: Duração do evento em segundos (float)
- `total_duration_seconds`: Duração do evento em segundos (float)
- `MDR`: Material Data Rate do lead product (float)

**Observação**: Apenas linhas onde `event{event_number_type}_start` ou `event{event_number_type}_end` são `True` são retornadas.

### Parâmetros do Método

#### `process(data, producing_waste_tags, mdr, lead_product, event_number_type="4c")`

**Parâmetros:**

- `data` (pd.DataFrame): Série temporal com coluna "index" (timestamp) e colunas de tags
- `producing_waste_tags` (list[str]): Lista de nomes de tags a monitorar (ex: `["DivertTotalizer"]`)
- `mdr` (pd.DataFrame): DataFrame com MDRs (usado para obter MDR do lead product)
- `lead_product` (Optional[LeadProduct]): Lead product (usado para obter MDR)
- `event_number_type` (str): Sufixo do tipo de evento (padrão: `"4c"`)

**Retorno:**

- `pd.DataFrame`: DataFrame filtrado contendo apenas linhas de início/fim de eventos, com timestamps normalizados para 00:00 do dia

### Exemplos de Uso

#### Exemplo 1: Uso Básico

```python
from app.services.event_identification.was.producing_waste_by_quality_control_service import (
    ProducingWasteByQualityControlService,
)
import pandas as pd

# Preparar DataFrame com séries temporais
data = pd.DataFrame({
    "index": pd.date_range("2024-10-02", "2024-10-06", freq="1H"),
    "DivertTotalizer": [0, 0, 50, 80, 105, 105, 108, 112, 112, 120, 140, 153, 153, 153],
    # ... outras colunas ...
})

# Preparar MDR e lead product
mdr = pd.DataFrame({
    "externalId": ["PROD-001"],
    "unitAvgRate": [100.0],  # 100 kg/h
})
lead_product = LeadProduct(...)  # Com mdr.external_id = "PROD-001"

# Processar eventos
result = ProducingWasteByQualityControlService.process(
    data=data,
    producing_waste_tags=["DivertTotalizer"],
    mdr=mdr,
    lead_product=lead_product,
    event_number_type="4c",
)

# Verificar resultados
print(f"Eventos criados: {len(result)}")
print(result[["index", "event4c_start", "event4c_end", "TotalProducingWaste", "ProducingWasteDuration"]])
```

#### Exemplo 2: Múltiplas Tags

```python
# Processar com múltiplas tags de contador
result = ProducingWasteByQualityControlService.process(
    data=data,
    producing_waste_tags=["DivertTotalizer", "PlopTotalizer"],  # Múltiplas tags
    mdr=mdr,
    lead_product=lead_product,
    event_number_type="4c",
)

# O scrap total será a soma dos scraps de ambas as tags
```

#### Exemplo 3: Tratamento de Resets

```python
# Exemplo com reset do contador
data = pd.DataFrame({
    "index": pd.date_range("2024-10-02", "2024-10-03", freq="1H"),
    "DivertTotalizer": [
        100, 105, 110, 115,  # Aumento normal
        0, 5, 10, 15, 20,     # Reset (volta a zero) e novo aumento
    ],
})

# O serviço detecta o reset e calcula incrementos corretamente:
# - Antes do reset: incrementos de 5, 5, 5
# - No reset: incremento = 0 (115 -> 0)
# - Após o reset: incrementos de 5, 5, 5, 5
```

### Tratamento de Casos Especiais

#### Reset de Contador

Quando um contador volta a zero (reset), o serviço trata corretamente:

- O incremento no ponto do reset é 0 (não negativo)
- Os incrementos após o reset são contabilizados normalmente
- O cálculo é feito por dia, então resets entre dias não afetam o cálculo diário

#### Mudança de Fuso Horário

O serviço trata mudanças de fuso horário:

- Normaliza timestamps para 00:00 do dia correspondente (mantém fuso horário)
- Remove duplicatas por dia normalizado
- Agrupa por dia normalizado e mantém apenas a última linha de cada dia

#### MDR Não Disponível

Se o MDR não estiver disponível ou for inválido:

- A duração será calculada como 0 segundos
- O evento ainda será criado (se houver scrap > 0)
- A coluna `MDR` será preenchida com 0.0

#### Sem Tags Processadas

Se nenhuma tag for processada (todas ausentes do DataFrame):

- Retorna DataFrame sem eventos
- Preenche MDR mesmo sem eventos
- Remove colunas auxiliares

#### Dias sem Scrap

Dias onde o scrap total é 0 não geram eventos:

- Apenas dias com `daily_scrap_total > 0` geram eventos
- Se todos os dias tiverem scrap = 0, retorna DataFrame vazio (após filtragem)

### Observações Importantes

- **Eventos Diários**: Os eventos são sempre criados por dia completo (de 00:00 até 00:00 do dia seguinte)
- **Normalização de Timestamps**: Todos os timestamps são normalizados para 00:00 do dia correspondente
- **Timezone**: O fuso horário é preservado durante a normalização
- **Filtragem**: Apenas linhas de início/fim de eventos são retornadas (não todas as linhas do DataFrame original)
- **Remoção de Duplicatas**: Duplicatas causadas por mudança de fuso horário são removidas
- **Primeira e Última Linha**: A primeira e última linha são removidas por segurança para evitar problemas de borda
- **Métricas**: As métricas são preenchidas apenas nas linhas de start/end (não em todas as linhas do evento)
- **Performance**: O serviço utiliza operações vetorizadas do pandas para melhor performance

### Casos de Uso

#### Caso de Uso 1: Identificação de Eventos de Scrap Diário

O serviço é usado para identificar quando houve produção de resíduos por controle de qualidade em um determinado dia, baseado em contadores cumulativos que medem a quantidade de material desviado.

**Exemplo**: Na unidade SCD, o contador `DivertTotalizer` mede a quantidade total de material desviado. O serviço identifica dias onde houve desvio e cria eventos do tipo 4c.

#### Caso de Uso 2: Cálculo de Duração de Eventos

O serviço calcula a duração equivalente dos eventos de scrap baseado no MDR do lead product, permitindo entender quanto tempo de produção foi "perdido" devido ao scrap.

**Exemplo**: Se 100 kg de scrap foram produzidos e o MDR é 100 kg/h, a duração equivalente é 1 hora.

#### Caso de Uso 3: Agregação de Múltiplas Tags

O serviço permite agregar scrap de múltiplas tags, somando os incrementos diários de cada tag para obter um valor total.

**Exemplo**: Se `DivertTotalizer` teve 50 kg de scrap e `PlopTotalizer` teve 30 kg, o scrap total do dia é 80 kg.

----

# English Section

## Producing Waste by Quality Control Service

This document describes the operation of the `ProducingWasteByQualityControlService`, responsible for identifying daily "Producing Waste by Quality Control" events based on cumulative counter tags.

### Overview

The service processes time series of cumulative tags (such as `DivertTotalizer`) to identify daily waste production events. The main logic is:

- **Calculates daily increments** of each counter tag, considering resets (when the counter returns to zero)
- **Sums the increments** from all tags to obtain a total daily scrap value
- **Creates daily events** (from 00:00 until 00:00 of the next day) whenever the total daily value is greater than zero
- **Calculates duration** of events based on the MDR (Material Data Rate) of the lead product

### Service Application

The service is used in `WASSCDEventIdentificationService` to identify events of type **4c** (Producing Waste by Quality Control) in the SCD unit (Washington Works).

The application occurs in the `identify_events_typeIVc` method of `WASSCDEventIdentificationService`:

```569:609:OEE_EventFrame/oee_function/app/services/event_identification/was/was_scd_event_identification_service.py
def identify_events_typeIVc(
    self, data: pd.DataFrame, **args
) -> pd.DataFrame:
    """
    Identify Producing Waste by Quality Control events (type IVc).
    ...
    """
    data_copy = data.copy()
    producing_waste_tags = ["DivertTotalizer"]
    
    data_result = ProducingWasteByQualityControlService.process(
        data_copy,
        producing_waste_tags,
        self._mdr,
        self._lead_product,
        "4c",
    )
    
    return data_result
```

### How It Works

#### Increment Calculation Logic

The service calculates how much each tag **effectively increased** each day, considering:

1. **Cumulative series**: The tag maintains a cumulative value that increases over time
2. **Increment calculation per sample**: For each sample, calculates the positive increment relative to the previous value
3. **Reset handling**: When there is a reset (current value < previous value), the increment at that point is 0; new increments after the reset are counted normally
4. **Grouping by day**: Increments are calculated within each day (normalized), ensuring that the "initial value of the day" is the reference
5. **Daily sum**: The sum of increments within a day represents the "scrap" of that day

#### Duration Calculation

The event duration is calculated using the formula:

```
duration_seconds = (scrap_total_day / MDR) * 3600
```

Where:
- `scrap_total_day`: Total scrap of the day (sum of all tags)
- `MDR`: Material Data Rate of the lead product (obtained from MDR DataFrame)
- `3600`: Conversion from hours to seconds

**Note**: If MDR is not available or invalid (NaN or <= 0), the duration will be 0.

#### Practical Example

Consider a `DivertTotalizer` tag with the following daily maximum values:

**Scenario:**
- Day 02/10: maximum value = 0
- Day 03/10: maximum value = 105
- Day 04/10: maximum value = 112
- Day 05/10: maximum value = 153
- After 06/10: no increases

**Daily scrap calculation:**
- Day 02/10: scrap = 0 (no increase)
- Day 03/10: scrap = 105 (105 - 0)
- Day 04/10: scrap = 7 (112 - 105)
- Day 05/10: scrap = 41 (153 - 112)
- After 06/10: scrap = 0 (no increase)

**Events created:**
- Event 1: 03/10 00:00 until 04/10 00:00 (scrap = 105)
- Event 2: 04/10 00:00 until 05/10 00:00 (scrap = 7)
- Event 3: 05/10 00:00 until 06/10 00:00 (scrap = 41)

**Duration calculation (example with MDR = 100 kg/h):**
- Event 1: (105 / 100) * 3600 = 3780 seconds (1.05 hours)
- Event 2: (7 / 100) * 3600 = 252 seconds (0.07 hours)
- Event 3: (41 / 100) * 3600 = 1476 seconds (0.41 hours)

### Processing Flow

1. **Initial Validation**:
   - Checks if "index" column exists (returns without modifications if it doesn't exist)
   - Creates a copy of DataFrame to avoid modifying the original

2. **Preparation**:
   - Sorts DataFrame by timestamp (column "index")
   - Initializes event columns: `event{event_number_type}_start` and `event{event_number_type}_end`
   - Initializes metric columns: `ProducingWasteDuration`, `TotalProducingWaste`, `total_duration_seconds`, `MDR`
   - Creates auxiliary column `event_date` with normalized date (day)

3. **Scrap Calculation per Tag**:
   - For each tag in `producing_waste_tags`:
     - Ensures tag exists in DataFrame
     - Converts values to numeric (treats errors as NaN and fills with 0)
     - Calculates increments per day using `groupby(event_date).diff()`
     - Applies `clip(lower=0)` to consider only positive increments
     - Sums daily increments to get daily scrap of the tag
     - Accumulates daily scrap of the tag in the daily total

4. **Identification of Days with Events**:
   - Identifies days where `daily_scrap_total > 0`
   - If no day has scrap, returns DataFrame without events (but fills MDR)

5. **Event Creation**:
   - For each day with scrap > 0:
     - Calculates total scrap of the day
     - Calculates duration in seconds using MDR
     - Identifies event start: first sample of the day (ordered by timestamp)
     - Identifies event end: first sample of the next day (if exists), otherwise last sample of current day
     - Marks event start and end in corresponding columns
     - Fills metrics (TotalProducingWaste, ProducingWasteDuration, MDR) in start/end rows

6. **Cleaning and Filtering**:
   - Removes auxiliary columns (`__delta_*`, `event_date`)
   - Ensures only first occurrence of start/end using `keep_only_first_occurrence`
   - Filters only rows where there is start or end of event
   - Removes duplicates by normalized day (solves timezone problem)
   - Normalizes timestamps to 00:00 of corresponding day (keeps timezone)
   - Removes first and last row for safety

### Required Columns

The input DataFrame must contain the following columns:

- `index`: Timestamp of samples (datetime or timestamp)
- Tag columns: Names of tags specified in `producing_waste_tags` (e.g., `DivertTotalizer`)

**Note**: If the "index" column does not exist, the service returns the original DataFrame without modifications.

### Generated Columns

The output DataFrame contains the following columns:

- `index`: Timestamp normalized to 00:00 of the day (datetime)
- `event{event_number_type}_start`: Boolean indicating event start (e.g., `event4c_start`)
- `event{event_number_type}_end`: Boolean indicating event end (e.g., `event4c_end`)
- `TotalProducingWaste`: Total scrap of the day (float)
- `ProducingWasteDuration`: Event duration in seconds (float)
- `total_duration_seconds`: Event duration in seconds (float)
- `MDR`: Material Data Rate of the lead product (float)

**Note**: Only rows where `event{event_number_type}_start` or `event{event_number_type}_end` are `True` are returned.

### Method Parameters

#### `process(data, producing_waste_tags, mdr, lead_product, event_number_type="4c")`

**Parameters:**

- `data` (pd.DataFrame): Time series with "index" column (timestamp) and tag columns
- `producing_waste_tags` (list[str]): List of tag names to monitor (e.g., `["DivertTotalizer"]`)
- `mdr` (pd.DataFrame): DataFrame with MDRs (used to get MDR of lead product)
- `lead_product` (Optional[LeadProduct]): Lead product (used to get MDR)
- `event_number_type` (str): Event type suffix (default: `"4c"`)

**Returns:**

- `pd.DataFrame`: Filtered DataFrame containing only start/end event rows, with timestamps normalized to 00:00 of the day

### Usage Examples

#### Example 1: Basic Usage

```python
from app.services.event_identification.was.producing_waste_by_quality_control_service import (
    ProducingWasteByQualityControlService,
)
import pandas as pd

# Prepare DataFrame with time series
data = pd.DataFrame({
    "index": pd.date_range("2024-10-02", "2024-10-06", freq="1H"),
    "DivertTotalizer": [0, 0, 50, 80, 105, 105, 108, 112, 112, 120, 140, 153, 153, 153],
    # ... other columns ...
})

# Prepare MDR and lead product
mdr = pd.DataFrame({
    "externalId": ["PROD-001"],
    "unitAvgRate": [100.0],  # 100 kg/h
})
lead_product = LeadProduct(...)  # With mdr.external_id = "PROD-001"

# Process events
result = ProducingWasteByQualityControlService.process(
    data=data,
    producing_waste_tags=["DivertTotalizer"],
    mdr=mdr,
    lead_product=lead_product,
    event_number_type="4c",
)

# Check results
print(f"Events created: {len(result)}")
print(result[["index", "event4c_start", "event4c_end", "TotalProducingWaste", "ProducingWasteDuration"]])
```

#### Example 2: Multiple Tags

```python
# Process with multiple counter tags
result = ProducingWasteByQualityControlService.process(
    data=data,
    producing_waste_tags=["DivertTotalizer", "PlopTotalizer"],  # Multiple tags
    mdr=mdr,
    lead_product=lead_product,
    event_number_type="4c",
)

# Total scrap will be the sum of scraps from both tags
```

#### Example 3: Reset Handling

```python
# Example with counter reset
data = pd.DataFrame({
    "index": pd.date_range("2024-10-02", "2024-10-03", freq="1H"),
    "DivertTotalizer": [
        100, 105, 110, 115,  # Normal increase
        0, 5, 10, 15, 20,     # Reset (returns to zero) and new increase
    ],
})

# The service detects the reset and calculates increments correctly:
# - Before reset: increments of 5, 5, 5
# - At reset: increment = 0 (115 -> 0)
# - After reset: increments of 5, 5, 5, 5
```

### Special Case Handling

#### Counter Reset

When a counter returns to zero (reset), the service handles it correctly:

- The increment at the reset point is 0 (not negative)
- Increments after the reset are counted normally
- Calculation is done per day, so resets between days do not affect daily calculation

#### Timezone Change

The service handles timezone changes:

- Normalizes timestamps to 00:00 of corresponding day (keeps timezone)
- Removes duplicates by normalized day
- Groups by normalized day and keeps only the last row of each day

#### MDR Not Available

If MDR is not available or invalid:

- Duration will be calculated as 0 seconds
- Event will still be created (if there is scrap > 0)
- `MDR` column will be filled with 0.0

#### No Tags Processed

If no tags are processed (all absent from DataFrame):

- Returns DataFrame without events
- Fills MDR even without events
- Removes auxiliary columns

#### Days Without Scrap

Days where total scrap is 0 do not generate events:

- Only days with `daily_scrap_total > 0` generate events
- If all days have scrap = 0, returns empty DataFrame (after filtering)

### Important Notes

- **Daily Events**: Events are always created for full days (from 00:00 until 00:00 of the next day)
- **Timestamp Normalization**: All timestamps are normalized to 00:00 of the corresponding day
- **Timezone**: Timezone is preserved during normalization
- **Filtering**: Only start/end event rows are returned (not all rows from original DataFrame)
- **Duplicate Removal**: Duplicates caused by timezone changes are removed
- **First and Last Row**: First and last rows are removed for safety to avoid edge problems
- **Metrics**: Metrics are filled only in start/end rows (not in all event rows)
- **Performance**: The service uses pandas vectorized operations for better performance

### Use Cases

#### Use Case 1: Daily Scrap Event Identification

The service is used to identify when there was waste production by quality control on a given day, based on cumulative counters that measure the amount of diverted material.

**Example**: In the SCD unit, the `DivertTotalizer` counter measures the total amount of diverted material. The service identifies days where there was diversion and creates type 4c events.

#### Use Case 2: Event Duration Calculation

The service calculates the equivalent duration of scrap events based on the MDR of the lead product, allowing understanding of how much production time was "lost" due to scrap.

**Example**: If 100 kg of scrap were produced and MDR is 100 kg/h, the equivalent duration is 1 hour.

#### Use Case 3: Multiple Tags Aggregation

The service allows aggregating scrap from multiple tags, summing the daily increments of each tag to obtain a total value.

**Example**: If `DivertTotalizer` had 50 kg of scrap and `PlopTotalizer` had 30 kg, the total scrap of the day is 80 kg.

