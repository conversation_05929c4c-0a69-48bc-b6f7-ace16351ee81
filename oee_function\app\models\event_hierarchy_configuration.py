from typing import Any, Optional

from pydantic import Field

from .node import Node
from .reporting_line import ReportingLine


class EventHierarchyConfiguration(Node):
    reporting_line: ReportingLine = Field(None, alias="reportingLine")
    event_hierarchy: Optional[str] = Field(None, alias="eventHierarchy")
    variable_categories: Optional[bool] = Field(
        False, alias="variableCategories"
    )
    event_definition: Optional[str] = Field(None, alias="eventDefinition")
    sub_category_level1: Optional[str] = Field(None, alias="subCategoryLevel1")
    sub_category_level2: Optional[str] = Field(None, alias="subCategoryLevel2")
    event_code: Optional[str] = Field(None, alias="eventCode")
    metric_code: Optional[str] = Field(None, alias="metricCode")
    business_rule: Optional[bool] = Field(False, alias="businessRule")
    work_shift_rule: Optional[bool] = Field(False, alias="workShiftRule")
    uses_bbct: Optional[bool] = Field(False, alias="usesBbct")
    minor_stop: Optional[bool] = Field(False, alias="minorStop")
    new_minor_stop: Optional[str] = Field(None, alias="newMinorStop")
    new_not_running: Optional[str] = Field(None, alias="newNotRunning")
    not_running_rule: Optional[bool] = Field(False, alias="notRunningRule")
    event_hierarchy_shifts: Optional[list[str]] = Field(default=[], alias="eventHierarchyShifts")

    @classmethod
    def from_cognite_response(
        cls, item: dict[str, Any]
    ) -> "EventHierarchyConfiguration":
        keys = {"reportingUnit", "reportingLine"}
        return EventHierarchyConfiguration(
            reportingLine=ReportingLine(**item["reportingLine"])
            if item["reportingLine"]
            else None,
            **{k: v for k, v in item.items() if k not in keys},
        )
