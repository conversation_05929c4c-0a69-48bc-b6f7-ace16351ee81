import pandas as pd


def _check_material_matches(msdp_data: pd.DataFrame, clean_product: str) -> tuple:
    """Helper function to check material name and description matches"""
    material_name_mask = pd.Series(False, index=msdp_data.index)
    material_desc_mask = pd.Series(False, index=msdp_data.index)
    
    if "refMaterial" not in msdp_data.columns:
        return material_name_mask, material_desc_mask
        
    material_rows = msdp_data["refMaterial"].notna()
    
    if not material_rows.any():
        return material_name_mask, material_desc_mask
        
    for idx in msdp_data.index[material_rows]:
        material = msdp_data.at[idx, "refMaterial"]
        if not isinstance(material, dict):
            continue
        
        # Check material name
        if "name" in material and material["name"]:
            material_name = str(material["name"]).replace("-", "").strip().lower()
            material_name_mask.at[idx] = material_name == clean_product
        
        # Check material description
        if "description" in material and material["description"]:
            material_desc = str(material["description"]).replace("-", "").strip().lower()
            material_desc_mask.at[idx] = material_desc == clean_product
    
    return material_name_mask, material_desc_mask


def create_product_match_filter(
    msdp_data: pd.DataFrame, 
    product_name: str
) -> pd.Series:
    """
    Creates a filter mask that matches product exactly across piTagValue, refMaterial.name, and refMaterial.description
    
    Args:
        msdp_data: DataFrame with MSDP data
        product_name: Product name to match
    
    Returns:
        Boolean mask Series where True indicates a row that matches the product exactly
    """
    if pd.isna(product_name) or not product_name:
        return pd.Series(False, index=msdp_data.index)
        
    clean_product = str(product_name).replace(" ", "").replace("\u00A0", "").replace("-", "").strip().lower()
    if not clean_product:
        return pd.Series(False, index=msdp_data.index)
    
    # Check product group matches
    pi_tag_value_mask = pd.Series(False, index=msdp_data.index)
    if "piTagValue" in msdp_data.columns:
        normalized_pi_tag_value = (
            msdp_data["piTagValue"]
            .str.replace(r'[\s\u00A0]+', '', regex=True)
            .str.replace("-", "", regex=False)
            .str.lower()
        )
        pi_tag_value_mask = normalized_pi_tag_value == clean_product
    
    material_name_mask, material_desc_mask = _check_material_matches(msdp_data, clean_product)
    
    combined_mask = pi_tag_value_mask | material_name_mask | material_desc_mask
    
    return combined_mask
