import logging
import os
import sys
from typing import List

import numpy as np
import pandas as pd
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables

"""
    ATENÇÃO: Este script deverá corrigir todas as Categorias OEE Process que estão inativas.
"""

# Definindo o caminho do script para encontrar os pacotes corretamente
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from oee_function.app.infra.logger_adapter import get_logger
from oee_function.app.repositories.OEEEvent_repository import OEEEventRepository
from oee_function.app.repositories.view_repository import ViewRepository

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_cognite_client(variables: EnvVariables):
    """Cria uma instância do cliente Cognite usando as variáveis de ambiente."""
    return CogniteClientFactory.create(variables)

def create_repositories(variables: EnvVariables):
    """Inicializa os repositórios necessários para acessar os dados."""
    cognite_client = create_cognite_client(variables)
    data_model_id = DataModelId(
        variables.cognite.data_model_space,
        variables.cognite.data_model_external_id,
        variables.cognite.data_model_version,
    )
    return {
        "OEEEvent": OEEEventRepository(
            cognite_client,            
            data_model_id,
            ViewRepository(cognite_client, data_model_id),
        ),
    }
    
def get_all_oee_events_from_units(variables: EnvVariables) -> List[dict]:
    """Executa a query para buscar todos os processos OEE Events"""
    units = [
        {
        "externalId": {"eq":  "UNT-SHYVEC"},
        },
    ]
    eventsDefinition = [
        'Not Running', 'Batch Idle - Known No demand'
        ]
    repositories = create_repositories(variables)
    oeeEvent_repository = repositories.get("OEEEvent")
    if oeeEvent_repository is None:
        logger.error("ProcessRepository não encontrado.")
        return pd.DataFrame()

    data = oeeEvent_repository.query_all_oee_events_from_units(units, eventsDefinition)
    return data

def create_oee_events(variables, oee_events_data):
    """
    Cria os eventos OEE no Cognite Data Model.
    """
    repositories = create_repositories(variables)
    oeeEvent_repository = repositories.get("OEEEvent")
    if oeeEvent_repository is None:
        logger.error("ProcessRepository não encontrado.")
        return pd.DataFrame()

    # updateOeeEvents = oeeEvent_repository.create_oee_events(oee_events_data)
    return []

def run():
    """
    Script para coletar todos os processos OEE e exibir os dados.
    """
    
    variables = EnvVariables()    
    log = get_logger()
    
    log.info("Inicializando Fix de OEE Events...")

    log.info("Capturando todos OEE Events...")
    oee_events_data = get_all_oee_events_from_units(variables)
    
    if len(oee_events_data) <= 0:
        log.error("Nenhum OEE Event encontrado.")
        return

    log.info("OEE Events encontrados: %d" % len(oee_events_data))
    
    # Cria os eventos OEE no Cognite Data Model
    log.info("Criando OEE Event no Cognite Data Model...")
    create_oee_events(variables, oee_events_data)
    
    logger.info("Script concluído com sucesso")


if __name__ == "__main__":
    run()
