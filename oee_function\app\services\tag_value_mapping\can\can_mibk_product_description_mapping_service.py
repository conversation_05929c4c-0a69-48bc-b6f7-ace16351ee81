import pandas as pd


class CanMibkProductDescriptionMappingService:
    def __init__(self) -> None:
        pass

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        # data[time_series] = "MIBK/MIBC"
        data[time_series] = data.apply(
            lambda row: 'MIBC' if row['NetProductionMIBC'] > 0 else ('MIBK' if row['NetProductionMIBK'] > 0 else None), axis=1
        )
        return data