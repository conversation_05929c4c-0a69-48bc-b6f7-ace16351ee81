# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
energy-model-portal/node_modules
energy-model-portal/.pnp
.pnp.js

# testing
energy-model-portal/coverage

# next.js
energy-model-portal/.next/
/out/

# production
energy-model-portal/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*


# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts


# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
__pycache__
dist
dist-ssr
*.local

# Editor directories and files
.vscode/
*/*.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*/*.venv*
*mlruns*

*.Identifier

# Image
#*.png
TODO.md


*.env

*.vscode
*.env.prod
*.env.qa


/home/<USER>/radix/celenase/new2/OEE_EventFrame/oee_function/.vscode

*.venv*
*venv*