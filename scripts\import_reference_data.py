from enum import Enum
import logging
import os
import sys
from ast import literal_eval

import pandas as pd
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory, CogniteClient
from core.env_variables import EnvVariables

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))


from oee_function.app.models.bbct import Bbct
from oee_function.app.models.business_segment import BusinessSegment
from oee_function.app.models.country import Country
from oee_function.app.models.mdr import Mdr
from oee_function.app.models.msdp import Msdp
from oee_function.app.models.region import Region
from oee_function.app.models.reporting_line import ReportingLine
from oee_function.app.models.reporting_site import ReportingSite
from oee_function.app.models.material import Material
from oee_function.app.models.product_transition import ProductTransition
from oee_function.app.models.reporting_site_configuration import (
    EventHierarchyConfiguration,
    InputTagConfiguration,
    ReportingSiteConfiguration,
)
from oee_function.app.models.reporting_unit import ReportingUnit
from oee_function.app.models.timeseries_configuration import (
    TimeseriesConfiguration,
)
from oee_function.app.models.unit_of_measurement import UnitOfMeasurement
from oee_function.app.repositories.bbct_repository import BbctRepository
from oee_function.app.repositories.mdr_repository import MdrRepository
from oee_function.app.repositories.msdp_repository import MsdpRepository
from oee_function.app.repositories.reporting_site_repository import (
    ReportingSiteRepository,
)
from oee_function.app.repositories.timeseries_configuration_repository import (
    TimeseriesConfigurationRepository,
)
from oee_function.app.repositories.product_transition_repository import (
    ProductTransitionRepository
)
from oee_function.app.repositories.view_repository import ViewRepository
from oee_function.app.enums.entity_code import SpaceEnum

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

SHEET_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    "data",
    "2026-01-07_Was-MPW-Comp_GlobalConfiguration.xlsx",
)


class TaskEnum(Enum):
    INPUT_TAG_CONFIGURATION = "InputTagConfiguration"
    EVENT_HIERARCHY_CONFIGURATION = "EventHierarchyConfiguration"
    BBCT = "BBCT"
    MSDP = "MSDP"
    MDR = "MDR"
    TIME_SERIES_CONFIGURATION = "TimeseriesConfiguration"
    PRODUCT_TRANSITION = "ProductTransition"


class ImportReferenceData:
    def __init__(self, tasks: list[TaskEnum]):
        self.__tasks = tasks
        self.__env_variables = EnvVariables()
        self.__data_model_id = DataModelId(
            self.__env_variables.cognite.data_model_space,
            self.__env_variables.cognite.data_model_external_id,
            self.__env_variables.cognite.data_model_version,
        )
        self.__cognite_client = self.__create_cognite_client()
        self.__instance_space = (
            self.__env_variables.cognite.default_data_model_instances_space
        )
        self.__material_space = SpaceEnum.MATERIAL_SPACE.value

    def __create_cognite_client(self) -> CogniteClient:
        return CogniteClientFactory.create(self.__env_variables)

    def __read_spreeadsheet(self, sheet_name: str) -> pd.DataFrame:
        return pd.read_excel(SHEET_PATH, sheet_name=sheet_name)

    def __run_reporting_site_configuration(self) -> list[ReportingSiteConfiguration]:
        data = self.__read_spreeadsheet("OEEReportingSiteConfiguration")

        data["fixEventNoVal"] = data["fixEventNoVal"].fillna("False")
        data["regexProcessing"] = data["regexProcessing"].fillna("")
        data["extraEventProcessing"] = data["extraEventProcessing"].fillna("")
        data["extraTimeProcessing"] = data["extraTimeProcessing"].fillna("")

        configurations = data.to_dict(orient="records")
        result: list[ReportingSiteConfiguration] = []
        for configuration in configurations:
            reporting_site_external_id = configuration.get("reportingSiteExternalId")
            reporting_site_space = configuration.get("reportingSiteSpace")
            shift_value = configuration.get("shifts", "")
            fix_null_values_subset_value = configuration.get("fixNullValuesSubset", "")

            shifts = literal_eval(shift_value)
            fix_null_values_subset = literal_eval(fix_null_values_subset_value)

            extra_time_processing = configuration.get("extraTimeProcessing")
            result.append(
                ReportingSiteConfiguration(
                    externalId=f"OEERSC-{reporting_site_external_id}",
                    space=self.__instance_space,
                    reportingSite=ReportingSite(
                        externalId=reporting_site_external_id,
                        space=reporting_site_space,
                    ),
                    shifts=shifts,
                    extraEventProcessing=configuration.get("extraEventProcessing"),
                    extraTimeProcessing=(
                        extra_time_processing if extra_time_processing != "" else None
                    ),
                    regexProcessing=configuration.get("regexProcessing"),
                    fixEventNoVal=configuration.get("fixEventNoVal"),
                    lineType=configuration.get("lineType"),
                    fixNullValuesSubset=fix_null_values_subset,
                )
            )

        return result

    def __run_event_hierarchy_configuration(self) -> list[EventHierarchyConfiguration]:
        data = self.__read_spreeadsheet("OEEEventHierarchyConfiguration")
        data["workShiftRule"] = data["workShiftRule"].fillna("False")
        data["usesBbct"] = data["usesBbct"].fillna("False")
        data["variableCategories"] = data["variableCategories"].fillna("False")
        data["notRunningRule"] = data["notRunningRule"].fillna("False")
        data["minorStop"] = data["minorStop"].fillna("False")
        data["notRunningRule"] = data["notRunningRule"].fillna("False")
        data["businessRule"] = data["businessRule"].fillna("False")
        data.fillna("", inplace=True)
        events_hierarchy = data.to_dict(orient="records")

        result: list[EventHierarchyConfiguration] = []
        for event in events_hierarchy:
            reporting_line_external_id = event.get("reportingLineExternalId")
            reporting_line_space = event.get("reportingLineSpace")
            event_definition = event.get("eventDefinition")
            event_hierarchy = event.get("eventHierarchy")
            eh_shift_value = event.get("eventHierarchyShifts", "")

            if eh_shift_value:
                try:
                    eh_shifts = literal_eval(eh_shift_value)
                except (ValueError, SyntaxError) as e:
                    print(f"There's no eventHierarchyShift: {e}")
                    eh_shifts = None
            else:
                eh_shifts = None

            external_id = f"OEEEHC-{reporting_line_external_id}-{event_definition}-{event_hierarchy}"
            result.append(
                EventHierarchyConfiguration(
                    externalId=external_id,
                    space=self.__instance_space,
                    reportingLine=(
                        ReportingLine(
                            externalId=reporting_line_external_id,
                            space=reporting_line_space,
                        )
                        if reporting_line_external_id
                        else None
                    ),
                    **{k: v for k, v in event.items() if k != "eventHierarchyShifts"},
                    eventHierarchyShifts=eh_shifts,
                )
            )

        return result

    def __run_input_tag_configuration(self) -> list[InputTagConfiguration]:
        data = self.__read_spreeadsheet("OEEInputTagConfiguration")
        data["eventIdentification"] = data["eventIdentification"].fillna("False")
        data["tagValueMapping"] = data["tagValueMapping"].fillna("False")
        input_tags = data.to_dict(orient="records")
        result: list[InputTagConfiguration] = []
        for input_tag in input_tags:
            reporting_line_external_id = input_tag.get("reportingLineExternalId")
            reporting_line_space = input_tag.get("reportingLineSpace")
            timeseries = input_tag.get("timeSeries")
            alias = input_tag.get("alias")
            event_identification = input_tag.get("eventIdentification")
            tag_value_mapping = input_tag.get("tagValueMapping")
            result.append(
                InputTagConfiguration(
                    externalId=f"OEEITC-{reporting_line_external_id}-{alias}",
                    space=self.__instance_space,
                    alias=alias,
                    timeSeries=timeseries,
                    reportingLine=ReportingLine(
                        externalId=reporting_line_external_id,
                        space=reporting_line_space,
                    ),
                    eventIdentification=event_identification,
                    tagValueMapping=tag_value_mapping,
                )
            )

        return result

    def __run_bbct_configuration(self) -> list[Bbct]:
        bbct_raw = self.__read_spreeadsheet("OEEBBCT")

        bbct_preprocessed = (
            bbct_raw.assign(
                dateSet=lambda df: pd.to_datetime(
                    df.dateSet, format="%m/%d/%Y %I:%M %p", errors="coerce"
                ).fillna(
                    pd.to_datetime(
                        df.dateSet, format="%m/%d/%Y  %H:%M:%S", errors="coerce"
                    )
                )
            )
            .assign(dateSet=lambda df: df.dateSet.dt.strftime("%Y-%m-%dT%H:%M:%S"))
            .assign(
                reportingLineExternalId=lambda df: df.reportingLineExternalId.str.strip()
            )
            .astype({"pITagValue": str})
            .dropna(axis=1, how="all")
            .fillna("")
            .replace({"bestBatchCycleTimeMT": "", "active": "", "leadBBCT": ""}, None)
            .drop_duplicates()
            .reset_index(drop=True)
            .reset_index()
        )

        bbct_records = bbct_preprocessed.to_dict(orient="records")

        result: list[Bbct] = []
        for bbct in bbct_records:
            reporting_line_external_id = bbct.get("reportingLineExternalId")
            reporting_line_space = bbct.get("reportingLineSpace")
            reporting_site_external_id = bbct.get("reportingSiteExternalId")
            reporting_site_space = bbct.get("reportingSiteSpace")

            index = bbct.get("index")
            external_id = f"OEEB-052924-A{'{:04d}'.format(index + 1)}"

            site_code = f"-{reporting_site_external_id.split('-')[1]}-"
            cor_code = "-COR-"
            site_space = self.__instance_space.replace(cor_code, site_code)

            product_name = bbct.get("refProduct")

            material_external_id = bbct.get("materialId")
            if isinstance(material_external_id, int):
                material_external_id = f"M_{material_external_id}"

            result.append(
                Bbct(
                    externalId=external_id,
                    space=site_space,
                    refSite=(
                        ReportingSite(
                            externalId=reporting_site_external_id,
                            space=reporting_site_space,
                        )
                        if reporting_site_external_id
                        else None
                    ),
                    refReportingLine=(
                        ReportingLine(
                            externalId=reporting_line_external_id,
                            space=reporting_line_space,
                        )
                        if reporting_line_external_id
                        else None
                    ),
                    product=product_name,
                    refMaterial=(
                        Material(
                            externalId=material_external_id,
                            space=self.__material_space,
                        )
                        if material_external_id
                        else None
                    ),
                    **bbct,
                )
            )

        return result

    def __run_msdp_configuration(self) -> list[Msdp]:
        msdp_raw = self.__read_spreeadsheet("OEEMSDP")
        msdp_raw["Month"] = msdp_raw["Month"].astype(str).str.zfill(2)
        msdp_raw["msdp"] = msdp_raw["msdp"].astype(float)
        msdp_preprocessed = (
            msdp_raw.assign(
                effectiveDate=lambda df: df["Year"].astype(str)
                + "-"
                + df["Month"]
                + "-"
                + "01"
            )
            .astype({"piTagValue": str, "scheduledRate": float})
            .fillna({"scheduledRate": 0})
            .drop_duplicates(
                subset=[
                    "reportingLineExternalId",
                    "Month",
                    "Year",
                    "msdp",
                    "mssr",
                    "piTagValue",
                    "scheduledRate",
                ]
            )
            .dropna(subset=["msdp"])
            .dropna(axis=1, how="all")
            .replace({float("nan"): None})
            .replace({str("nan"): None})
        )

        msdp_records = msdp_preprocessed.to_dict(orient="records")

        result: list[Msdp] = []
        for msdp in msdp_records:
            business_segment_external_id = msdp.get("businessSegmentExternalId")
            business_segment_space = msdp.get("businessSegmentSpace")
            reporting_site_external_id = msdp.get("reportingSiteExternalId")
            reporting_site_space = msdp.get("reportingSiteSpace")
            region_external_id = msdp.get("geoRegionExternalId")
            region_space = msdp.get("geoRegionSpace")
            country_external_id = msdp.get("countryExternalId")
            country_space = msdp.get("countrySpace")
            reporting_line_external_id = msdp.get("reportingLineExternalId")
            reporting_line_space = msdp.get("reportingLineSpace")
            uom_external_id = msdp.get("uomExternalId")
            uom_space = msdp.get("uomSpace")
            material_external_id = msdp.get("materialId")
            if isinstance(material_external_id, int):
                material_external_id = f"M_{material_external_id}"
            pi_tag_value = msdp.get("piTagValue")

            external_id = (
                f"OEEM{('-' + reporting_line_external_id.split('-')[1]) if reporting_line_external_id else ''}"
                f"-{msdp.get('Year')}"
                f"-{msdp.get('Month')}"
                f"{'-' + pi_tag_value if pi_tag_value else ''}"
                f"-{str(msdp.get('msdp'))}"
                f"-{str(msdp.get('scheduledRate'))}"
            ).replace(" ", "")

            site_code = f"-{reporting_site_external_id.split('-')[1]}-"
            cor_code = "-COR-"
            site_space = self.__instance_space.replace(cor_code, site_code)

            result.append(
                Msdp(
                    externalId=external_id,
                    space=site_space,
                    refSite=(
                        ReportingSite(
                            externalId=reporting_site_external_id,
                            space=reporting_site_space,
                        )
                        if reporting_site_external_id
                        else None
                    ),
                    refBusinessSegment=(
                        BusinessSegment(
                            externalId=business_segment_external_id,
                            space=business_segment_space,
                        )
                        if business_segment_external_id
                        else None
                    ),
                    refRegion=(
                        Region(
                            externalId=region_external_id,
                            space=region_space,
                        )
                        if region_external_id
                        else None
                    ),
                    refCountry=(
                        Country(
                            externalId=country_external_id,
                            space=country_space,
                        )
                        if country_external_id
                        else None
                    ),
                    refReportingLine=(
                        ReportingLine(
                            externalId=reporting_line_external_id,
                            space=reporting_line_space,
                        )
                        if reporting_line_external_id
                        else None
                    ),
                    uom=(
                        UnitOfMeasurement(
                            externalId=uom_external_id,
                            space=uom_space,
                        )
                        if uom_external_id
                        else None
                    ),
                    refMaterial=(
                        Material(
                            externalId=material_external_id, space=self.__material_space
                        )
                        if material_external_id
                        else None
                    ),
                    **msdp,
                )
            )

        return result

    def __run_mdr_configuration(self) -> list[Mdr]:
        mdr_raw = self.__read_spreeadsheet("OEEMDR")

        mdr_preprocessed = (
            mdr_raw.assign(
                dateSet=lambda df: df.dateSet.dt.strftime("%Y-%m-%dT%H:%M:%S")
            )
            .astype({"PiTAGValue": str})
            .dropna(axis=1, how="all")
            .fillna("")
            .drop_duplicates()
            .reset_index(drop=True)
            .reset_index()
        )

        mdr_records = mdr_preprocessed.to_dict(orient="records")

        result: list[Mdr] = []
        for mdr in mdr_records:
            reporting_site_external_id = mdr.get("reportingSiteExternalId")
            reporting_site_space = mdr.get("reportingSiteSpace")
            business_segment_external_id = mdr.get("businessSegmentExternalId")
            business_segment_space = mdr.get("businessSegmentSpace")
            reporting_line_external_id = mdr.get("reportingLineExternalId")
            reporting_line_space = mdr.get("reportingLineSpace")
            uom_external_id = mdr.get("uomExternalId")
            uom_space = mdr.get("uomSpace")
            date_set = mdr.get("dateSet")
            unit_avg_rate = mdr.get("unitAvgRate")
            material_external_id = mdr.get("materialId")
            if isinstance(material_external_id, int):
                material_external_id = f"M_{material_external_id}"
            pi_tag_value = mdr.get("PiTAGValue")
            region_external_id = mdr.get("regionExternalId")
            region_space = mdr.get("regionSpace")
            country_external_id = mdr.get("countryExternalId")
            country_space = mdr.get("countrySpace")
            scheduled_rate = mdr.get("scheduledRate")
            is_active = mdr.get("isActive")

            external_id = f"OEEMADR-{reporting_line_external_id}-{pi_tag_value if pi_tag_value else material_external_id}".replace(" ", "")

            site_code = f"-{reporting_site_external_id.split('-')[1]}-"
            cor_code = "-COR-"
            site_space = self.__instance_space.replace(cor_code, site_code)

            result.append(
                Mdr(
                    externalId=external_id,
                    space=site_space,
                    refSite=(
                        ReportingSite(
                            externalId=reporting_site_external_id,
                            space=reporting_site_space,
                        )
                        if reporting_site_external_id
                        else None
                    ),
                    refRegion=(
                        Region(externalId=region_external_id, space=region_space)
                        if region_external_id
                        else None
                    ),
                    refCountry=(
                        Country(externalId=country_external_id, space=country_space)
                        if country_external_id
                        else None
                    ),
                    refUnitOfMeasurement=(
                        UnitOfMeasurement(externalId=uom_external_id, space=uom_space)
                        if uom_external_id
                        else None
                    ),
                    refBusinessSegment=(
                        BusinessSegment(
                            externalId=business_segment_external_id,
                            space=business_segment_space,
                        )
                        if business_segment_external_id
                        else None
                    ),
                    refReportingLine=(
                        ReportingLine(
                            externalId=reporting_line_external_id,
                            space=reporting_line_space,
                        )
                        if reporting_line_external_id
                        else None
                    ),
                    refMaterial=(
                        Material(
                            externalId=material_external_id, space=self.__material_space
                        )
                        if material_external_id
                        else None
                    ),
                    dateSet=date_set,
                    piTagValue=pi_tag_value,
                    scheduledRate=scheduled_rate,
                    unitAvgRate=unit_avg_rate,
                    isActive=is_active,
                )
            )

        return result

    def __run_timeseries_configuration(self) -> list[TimeseriesConfiguration]:
        data = self.__read_spreeadsheet("OEETimeseriesConfiguration")

        data["isStep"] = data["isStep"].fillna("False")
        data["isString"] = data["isString"].fillna("False")
        data.replace({float("nan"): None}, inplace=True)

        configs = data.to_dict(orient="records")

        result: list[TimeseriesConfiguration] = []
        for config in configs:
            reporting_site_external_id = config.get("reportingSiteExternalId")
            reporting_site_space = config.get("reportingSiteSpace")
            reporting_unit_external_id = config.get("reportingUnitExternalId")
            reporting_unit_space = config.get("reportingUnitSpace")
            reporting_line_external_id = config.get("reportingLineExternalId")
            reporting_line_space = config.get("reportingLineSpace")
            uom_external_id = config.get("uomExternalId")
            uom_space = config.get("uomSpace")

            timeseries_external_id = config.get("timeseriesExternalId")
            external_id = f"OEET-{reporting_line_external_id.split('-')[1]}-{timeseries_external_id.split(':')[1]}"

            site_code = f"-{reporting_site_external_id.split('-')[1]}-"
            cor_code = "-COR-"
            site_space = self.__instance_space.replace(cor_code, site_code)

            result.append(
                TimeseriesConfiguration(
                    externalId=external_id,
                    space=site_space,
                    reportingSite=ReportingSite(
                        externalId=reporting_site_external_id,
                        space=reporting_site_space,
                    ),
                    reportingUnit=ReportingUnit(
                        externalId=reporting_unit_external_id,
                        space=reporting_unit_space,
                    ),
                    reportingLine=ReportingLine(
                        externalId=reporting_line_external_id,
                        space=reporting_line_space,
                    ),
                    uom=UnitOfMeasurement(
                        externalId=uom_external_id,
                        space=uom_space,
                    ),
                    **config,
                )
            )

        return result
    
    def __run_product_transition_configuration(self) -> list[ProductTransition]:
        data = self.__read_spreeadsheet("OEEProductTransition")

        data.fillna("", inplace=True)
        configs = data.to_dict(orient="records")
        result: list[ProductTransition] = []
        for config in configs:
            reporting_line_external_id = config.get("reportingLineExternalId")
            reporting_line_space = config.get("reportingLineSpace")

            external_id = f"OEEPT-{reporting_line_external_id.split('-')[1]}-{config.get('fromProduct')}-{config.get('toProduct')}"

            from_product_id = config.get("fromProduct")
            if isinstance(from_product_id, int):
                from_product_id = f"M_{from_product_id}"
                
            to_product_id = config.get("toProduct")
            if isinstance(to_product_id, int):
                to_product_id = f"M_{to_product_id}"

            result.append(
                ProductTransition(
                    externalId=external_id,
                    space=self.__instance_space,
                    refReportingLine=ReportingLine(
                        externalId=reporting_line_external_id,
                        space=reporting_line_space,
                    ),
                    fromProduct=Material(
                        externalId=from_product_id,
                        space=self.__material_space,
                    ),
                    toProduct=Material(
                        externalId=to_product_id,
                        space=self.__material_space,
                    ),
                    duration=config.get("duration"),
                )
            )
            
        return result

    def __create_reporting_site_repository(self) -> ReportingSiteRepository:
        return ReportingSiteRepository(
            self.__cognite_client,
            ViewRepository(self.__cognite_client, self.__data_model_id),
            self.__data_model_id,
        )

    def __create_time_series_configuration_repository(
        self,
    ) -> TimeseriesConfigurationRepository:
        return TimeseriesConfigurationRepository(
            self.__cognite_client,
            ViewRepository(self.__cognite_client, self.__data_model_id),
            self.__data_model_id,
        )

    def __create_bbct_repository(self) -> BbctRepository:
        return BbctRepository(
            self.__cognite_client,
            self.__data_model_id,
            ViewRepository(self.__cognite_client, self.__data_model_id),
        )

    def __create_msdp_repository(self) -> MsdpRepository:
        return MsdpRepository(
            self.__cognite_client,
            self.__data_model_id,
            ViewRepository(self.__cognite_client, self.__data_model_id),
        )

    def __create_mdr_repository(self) -> MdrRepository:
        return MdrRepository(
            self.__cognite_client,
            self.__data_model_id,
            ViewRepository(self.__cognite_client, self.__data_model_id),
        )
        
    def __create_product_transition_repository(self) -> ProductTransitionRepository:
        return ProductTransitionRepository(
            self.__cognite_client,
            self.__data_model_id,
            ViewRepository(self.__cognite_client, self.__data_model_id),
        )

    def run(self) -> None:
        reporting_site = self.__run_reporting_site_configuration()

        if TaskEnum.EVENT_HIERARCHY_CONFIGURATION.value in self.__tasks:
            events_hierarchy = self.__run_event_hierarchy_configuration()
            reporting_site_repository = self.__create_reporting_site_repository()
            for site in reporting_site:
                site_code = site.reporting_site.external_id.replace("STS-", "")
                reporting_line_code = f"RLN-{site_code}"
                site.events_hierarchy = [
                    item
                    for item in events_hierarchy
                    if item.reporting_line.external_id.startswith(reporting_line_code)
                ]
                reporting_site_repository.create_configuration(site)

        if TaskEnum.INPUT_TAG_CONFIGURATION.value in self.__tasks:
            input_tags = self.__run_input_tag_configuration()
            reporting_site_repository = self.__create_reporting_site_repository()
            for site in reporting_site:
                site_code = site.reporting_site.external_id.replace("STS-", "")
                reporting_line_code = f"RLN-{site_code}"
                site.input_tags = [
                    item
                    for item in input_tags
                    if item.reporting_line.external_id.startswith(reporting_line_code)
                ]
                reporting_site_repository.create_configuration(site)

        if TaskEnum.BBCT.value in self.__tasks:
            bbct = self.__run_bbct_configuration()
            bbct_repository = self.__create_bbct_repository()
            for site in reporting_site:
                site_code = site.reporting_site.external_id.replace("STS-", "")
                reporting_line_code = f"RLN-{site_code}"
                bbcts = [
                    item
                    for item in bbct
                    if item.reporting_line.external_id.startswith(reporting_line_code)
                ]
                bbct_repository.create_bbct(bbcts)

        if TaskEnum.MSDP.value in self.__tasks:
            msdp = self.__run_msdp_configuration()
            msdp_repository = self.__create_msdp_repository()
            for site in reporting_site:
                site_code = site.reporting_site.external_id.replace("STS-", "")
                reporting_line_code = f"RLN-{site_code}"
                msdps = [
                    item
                    for item in msdp
                    if item.reporting_line.external_id.startswith(reporting_line_code)
                ]
                msdp_repository.create_msdp(msdps)

        if TaskEnum.MDR.value in self.__tasks:
            mdr = self.__run_mdr_configuration()
            mdr_repository = self.__create_mdr_repository()
            for site in reporting_site:
                site_code = site.reporting_site.external_id.replace("STS-", "")
                reporting_line_code = f"RLN-{site_code}"

                mdrs = [
                    item
                    for item in mdr
                    if item.reporting_line.external_id.startswith(reporting_line_code)
                ]
                mdr_repository.create_mdr(mdrs)

        if TaskEnum.TIME_SERIES_CONFIGURATION.value in self.__tasks:
            timeseries_configuration = self.__run_timeseries_configuration()
            timeseries_configuration_repository = (
                self.__create_time_series_configuration_repository()
            )
            for site in reporting_site:
                site_code = site.reporting_site.external_id.replace("STS-", "")
                reporting_line_code = f"RLN-{site_code}"
                timeseries_configs = [
                    item
                    for item in timeseries_configuration
                    if item.reporting_line.external_id.startswith(reporting_line_code)
                ]
                timeseries_configuration_repository.create_configuration(
                    timeseries_configs
                )
                
        if TaskEnum.PRODUCT_TRANSITION.value in self.__tasks:
            product_transitions = self.__run_product_transition_configuration()
            product_transition_repository = (
                self.__create_product_transition_repository()
            )
            for site in reporting_site:
                site_code = site.reporting_site.external_id.replace("STS-", "")
                reporting_line_code = f"RLN-{site_code}"
                product_transitions_filtered = [
                    item
                    for item in product_transitions
                    if item.ref_reporting_line.external_id.startswith(reporting_line_code)
                ]
                product_transition_repository.create_product_transition(
                    product_transitions_filtered
                )

if __name__ == "__main__":
    tasks = [
        # TaskEnum.INPUT_TAG_CONFIGURATION.value,
        # TaskEnum.EVENT_HIERARCHY_CONFIGURATION.value,
        # TaskEnum.BBCT.value,
        # TaskEnum.MSDP.value,
        # TaskEnum.MDR.value,
        # TaskEnum.TIME_SERIES_CONFIGURATION.value,
        TaskEnum.PRODUCT_TRANSITION.value
    ]
    import_reference_data = ImportReferenceData(tasks)
    import_reference_data.run()
