from abc import ABC
from typing import Annotated, Any, List, Literal, Optional, Union

import pandas as pd
from pydantic import BaseModel, Field, TypeAdapter

conditional_operators = Literal[">", "<", "=", ">=", "<=", "!="]


def _check(left: pd.Series, right: pd.Series, operator: conditional_operators):
    try:
        match operator:
            case ">":
                return left > right
            case "<":
                return left < right
            case "=":
                return left == right
            case "!=":
                return left != right
            case ">=":
                return left >= right
            case "<=":
                return left <= right
    except Exception as e:
        raise e

    raise Exception("Unable to compare")


class Or(BaseModel):
    type: Literal["or"]
    child_conditions: List["ValidEvaluator"] = Field(default_factory=list)

    def evaluate(self, data: pd.DataFrame):
        return pd.concat(
            objs=(cc.evaluate(data) for cc in self.child_conditions), axis=1
        ).any(axis=1)


class And(BaseModel):
    type: Literal["and"]
    child_conditions: List["ValidEvaluator"] = Field(default_factory=list)

    def evaluate(self, data: pd.DataFrame):
        return pd.concat(
            objs=(cc.evaluate(data) for cc in self.child_conditions), axis=1
        ).all(axis=1)


class ElapsedTimeWithValue(BaseModel):
    type: Literal["elapsed_time_with_value"]
    value: Any
    field: str
    elapsed_time_operator: conditional_operators
    elapsed_time: float

    def evaluate(self, data: pd.DataFrame):
        return (
            (data[self.field] == self.value)
            & (data[self.field].shift(1) != self.value)
            & (
                _check(
                    data["dt"].shift(-1),
                    self.elapsed_time,
                    self.elapsed_time_operator,
                )
            )
        )


ValidEvaluator = Annotated[
    Union[Or, And, ElapsedTimeWithValue], Field(discriminator="type")
]


def evaluate(conditions: Any, data: pd.DataFrame):
    adapter = TypeAdapter(ValidEvaluator)
    try:
        evaluator = adapter.validate_python(conditions)
    except Exception as e:
        print(f"Error parsing evaluator: {e}")
        raise e

    try:
        return evaluator.evaluate(data)
    except Exception as e:
        print(f"Error evaluating conditions: {e}")
        raise e


IA_START_CONDITIONS = {
    "type": "or",
    "child_conditions": [
        {
            "type": "elapsed_time_with_value",
            "field": "BatchID",
            "value": "",
            "elapsed_time_operator": ">",
            "elapsed_time": 2,
        },
        {
            "type": "elapsed_time_with_value",
            "field": "BatchID",
            "value": "Inactive",
            "elapsed_time_operator": ">",
            "elapsed_time": 2,
        },
    ],
}


if __name__ == "__main__":
    event_configs = [
        {
            "type": "1a",
            "line": "ENO...",
            "start_condition": {
                "type": "or",
                "child_conditions": [
                    {
                        "type": "elapsed_time_with_value",
                        "field": "BatchID",
                        "value": "",
                        "elapsed_time_operator": ">",
                        "elapsed_time": 2,
                    },
                    {
                        "type": "elapsed_time_with_value",
                        "field": "BatchID",
                        "value": "Inactive",
                        "elapsed_time_operator": ">",
                        "elapsed_time": 2,
                    },
                ],
            },
        }
    ]

    adapter = TypeAdapter(ValidEvaluator)
    data = pd.DataFrame(
        {"BatchID": ["", "", "Other", "", "Inactive"], "dt": [0, 10, 1, 10, 1]}
    )
    for event_config in event_configs:
        ev_type = event_config["type"]
        data = data.assign(
            **{
                f"event{ev_type}_start": evaluate(
                    adapter, data, event_config["start_condition"]
                )
            }
        )
        print(data)
