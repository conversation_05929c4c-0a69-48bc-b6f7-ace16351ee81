import logging
import os
import sys
from datetime import datetime

import pandas as pd
from cognite.client.data_classes.data_modeling import NodeId
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables

"""
    ATENÇÃO: Este script irá localizar e deletar eventos OEE específicos com base nos seguintes filtros:
    - Sites
    - Units
    - Linhas
    - Events Definitions
"""

# Definindo o caminho do script para encontrar os pacotes corretamente
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

# Importando as classes e repositórios necessários
from oee_function.app.infra.logger_adapter import get_logger
from oee_function.app.repositories.OEEEvent_repository import OEEEventRepository
from oee_function.app.repositories.view_repository import ViewRepository

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constantes de filtros
SITES = ["STS-BIS"]  # External IDs dos sites
UNITS = []          # External IDs das units
LINES = ["RLN-BISPOMSHY"]  # External IDs das linhas
EVENTS_DEFINITIONS = ["Product Changeover", "Product changeover"]  # Event Definitions
START_DATE = "2024-01-01T00:00:00.000Z"  # Data de início
END_DATE = "2025-01-31T00:00:00.000Z"  # Data de término

def require_current_day():
    """
    Solicita ao usuário que insira o dia atual para liberar a execução do script.
    """
    today = datetime.now().strftime("%d")
    print("\nAtenção: Este processo é irreversivel para continuar, digite o dia atual (somente número): ")
    
    user_input = input("Dia atual: ").strip()
    
    if user_input != today:
        logger.error("Dia inserido incorreto. Execução abortada.")
        sys.exit(1)  # Encerra o programa com código de erro

    logger.info("Dia confirmado corretamente. Continuando execução.")


def create_cognite_client(variables: EnvVariables):
    """Cria uma instância do cliente Cognite usando as variáveis de ambiente."""
    return CogniteClientFactory.create(variables)

def create_repositories(variables: EnvVariables):
    """Inicializa os repositórios necessários para acessar os dados."""
    cognite_client = create_cognite_client(variables)
    data_model_id = DataModelId(
        variables.cognite.data_model_space,
        variables.cognite.data_model_external_id,
        variables.cognite.data_model_version,
    )
    return {
        "oee_event": OEEEventRepository(
            cognite_client,
            data_model_id,
            view_repository=ViewRepository(cognite_client, data_model_id),
        ),
    }

import json


def build_dynamic_query(sites, units, lines, event_definitions, start_date = None, end_date = None):
    """Constrói a query dinâmica para buscar eventos OEE com base nos filtros fornecidos."""
    filters = []
    if sites:
        filters.append(f'{{ refSite: {{ externalId: {{ in: {json.dumps(sites)} }} }} }}')
    if units:
        filters.append(f'{{ refUnit: {{ externalId: {{ in: {json.dumps(units)} }} }} }}')
    if lines:
        filters.append(f'{{ refReportingLine: {{ externalId: {{ in: {json.dumps(lines)} }} }} }}')
    if event_definitions:
        filters.append(f'{{ eventDefinition: {{ in: {json.dumps(event_definitions)} }} }}')
    if start_date and end_date:
        filters.append(f'{{startDateTime: {{gte: "{start_date}", lte: "{end_date}" }} }}')

    filters_str = ", ".join(filters)
    query = f"""
    query MyQuery($after: String) {{
      listOEEEvent(
        first: 1000
        filter: {{ and: [{filters_str}] }}
        after: $after
      ) {{
        items {{
          externalId
          space
          eventDefinition
          refSite {{
            externalId
          }}
          refUnit {{
            externalId
          }}
          refReportingLine {{
            externalId
          }}
          refOEEEventDetail {{
            items {{
            externalId
            space
            }}
         }}
          startDateTime
        }}
        pageInfo {{
          endCursor
          hasNextPage
        }}
      }}
    }}
    """
    return query



def get_filtered_oee_events(variables: EnvVariables) -> pd.DataFrame:
    """Busca eventos OEE que atendem aos filtros fornecidos."""
    repositories = create_repositories(variables)
    oee_event_repository = repositories.get("oee_event")
    if oee_event_repository is None:
        logger.error("OEEEventRepository não encontrado.")
        return pd.DataFrame()

    # Constrói a query com base nos filtros fornecidos
    query = build_dynamic_query(SITES, UNITS, LINES, EVENTS_DEFINITIONS, START_DATE, END_DATE)

    # Executa a query e retorna os resultados como DataFrame
    events = oee_event_repository.list_all(query=query, variables={})
    if not events:
        return pd.DataFrame()

    return pd.DataFrame(events)

def delete_oee_events_in_batch(variables: EnvVariables, oee_event_data: pd.DataFrame, batch_size: int = 100) -> None:
    """Exclui eventos OEE listados em oee_event_data em lotes."""
    if oee_event_data.empty:
        logger.info("Nenhum evento para deletar.")
        return

    repositories = create_repositories(variables)
    oee_event_repository = repositories.get("oee_event")

    # Prepara a lista de NodeIds
    nodes_to_delete = [
        NodeId(external_id=row["externalId"], space=row["space"])
        for _, row in oee_event_data.iterrows()
    ]

    try:
        # Divide a lista em lotes e realiza a exclusão em cada lote
        for i in range(0, len(nodes_to_delete), batch_size):
            batch = nodes_to_delete[i:i + batch_size]
            oee_event_repository._cognite_client.data_modeling.instances.delete(nodes=batch)
            logger.info(f"Lote de {len(batch)} eventos excluído com sucesso.")
    except Exception as e:
        logger.error(f"Erro ao excluir eventos em lote: {e}")
        
        
def delete_oee_events_details_in_batch(variables: EnvVariables, oee_event_data: pd.DataFrame, batch_size: int = 100) -> None:
    """Exclui os OEEEventDetail associados aos eventos em oee_event_data."""
    if oee_event_data.empty:
        logger.info("Nenhum detalhe de evento para deletar.")
        return

    repositories = create_repositories(variables)
    cognite_client = repositories["oee_event"]._cognite_client

    # Coleta todos os details referenciados
    detail_node_ids = []

    for _, row in oee_event_data.iterrows():
        details = row.get("refOEEEventDetail", [])
        if isinstance(details, list):
            for detail in details:
                external_id = detail.get("externalId")
                space = detail.get("space")
                if external_id and space:
                    detail_node_ids.append(NodeId(external_id=external_id, space=space))

    if not detail_node_ids:
        logger.info("Nenhum OEEEventDetail encontrado para deletar.")
        return

    try:
        for i in range(0, len(detail_node_ids), batch_size):
            batch = detail_node_ids[i:i + batch_size]
            cognite_client.data_modeling.instances.delete(nodes=batch)
            logger.info(f"Lote de {len(batch)} detalhes de eventos excluído com sucesso.")
    except Exception as e:
        logger.error(f"Erro ao excluir detalhes de eventos em lote: {e}")



def delete_oee_events(variables: EnvVariables, oee_event_data: pd.DataFrame) -> None:
    """Exclui eventos OEE listados em oee_event_data."""
    if oee_event_data.empty:
        logger.info("Nenhum evento para deletar.")
        return

    repositories = create_repositories(variables)
    oee_event_repository = repositories.get("oee_event")

    for _, row in oee_event_data.iterrows():
        external_id = row["externalId"]
        space = row["space"]
        try:
            oee_event_repository.delete_event(external_id, space)
            logger.info(f"Evento com externalId '{external_id}' e space '{space}' deletado com sucesso. ({_})")
        except Exception as e:
            logger.error(f"Erro ao deletar o evento com externalId '{external_id}' e space '{space}': {e}")

def run():
    """
        Localiza e exclui eventos OEE com base nos filtros definidos.
    """
    variables = EnvVariables()
    
    require_current_day()

    # Busca eventos com os filtros fornecidos
    oee_event_data = get_filtered_oee_events(variables)
    
    if oee_event_data.empty:
        logger.info("Nenhum evento encontrado para os filtros fornecidos.")
    else:
        logger.info(f"Eventos OEE encontrados:\n{oee_event_data}")

        # # Chama a função para deletar os eventos encontrados
        delete_oee_events_details_in_batch(variables, oee_event_data, batch_size=100)
        delete_oee_events_in_batch(variables, oee_event_data, batch_size=100)

if __name__ == "__main__":
    run()
