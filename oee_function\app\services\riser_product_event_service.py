from typing import Literal
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from app.utils.msdp_utils import get_msdp_value as utils_get_msdp_value
from app.utils.product_matching_utils import create_product_match_filter
from typing import Callable, List
from typing import Dict

from dataclasses import dataclass
@dataclass
class RiserEventResult:
    event_data: pd.DataFrame
    rlt: float
    sr: float
    sr_adjusted: float
    start_index: int
    end_index: int
    net_production: float
    running_time: float
    
    @property
    def start_time(self) -> pd.Timestamp:
        return self.event_data["index"].min().normalize()

    @property
    def end_time(self) -> pd.Timestamp:
        return self.start_time + pd.Timedelta(days=1)


class RiserProductEventService:
    def __init__(self,
                 msdp: pd.DataFrame,
                 reporting_line_external_id: str,
                 net_production_fn: Callable[[pd.DataFrame], pd.Series],
                 ):
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._net_production_fn = net_production_fn

    
    def get_rlt_and_msdp_riser_event_blocks(
        self,
        data: pd.DataFrame,
        riser_product_names: list[str]
    ) -> pd.DataFrame:
        results_by_day: Dict[pd.Timestamp, List[RiserEventResult]] = {}

        start_indexes = data.index[data["event2ab_start"]].tolist()
        end_indexes = data.index[data["event2ab_end"]].tolist()

        paired_indexes = []
        start_pointer = 0
        end_pointer = 0

        while start_pointer < len(start_indexes) and end_pointer < len(end_indexes):
            if start_indexes[start_pointer] < end_indexes[end_pointer]:
                paired_indexes.append((start_indexes[start_pointer], end_indexes[end_pointer]))
                start_pointer += 1
                end_pointer += 1
            else:
                end_pointer += 1

        start_indexes, end_indexes = zip(*paired_indexes) if paired_indexes else ([], [])

        for start_idx, end_idx in zip(start_indexes, end_indexes):
            event_data = data.loc[start_idx:end_idx].copy()
            if event_data.empty:
                continue

            next_non_riser = data.loc[end_idx:][
                ~data["ProductDescription"].isin(riser_product_names)
            ]["ProductDescription"].head(1)

            if next_non_riser.empty:
                continue

            next_product = next_non_riser.values[0]
            daily_results = self._calculate_rlt_for_riser_event(event_data, next_product)

            for result in daily_results:
                day = result.event_data["index"].dt.normalize().iloc[0]
                results_by_day.setdefault(day, []).append(result)

        # PT: Agrupar os blocos por dia consolidando as métricas
        # EN: Agroup the blocks by day consolidating the metrics
        consolidated_results = []
        for day, results in results_by_day.items():
            if not results:
                continue
            all_data = pd.concat([r.event_data for r in results])
            total_net_prod = sum(r.net_production for r in results)
            rlt = sum(r.rlt for r in results)
            running_time = sum(r.running_time for r in results)
            sr = pd.Series([r.running_time for r in results], index=[r.sr for r in results]).groupby(level=0).sum().idxmax()

            consolidated_results.append({
                "index": day,
                "start_time": all_data["index"].min().normalize(),
                "end_time": all_data["index"].min().normalize() + pd.Timedelta(days=1),
                "SCHR": sr,
                "NetProduction": total_net_prod,
                "rlt": rlt,
                "total_duration_seconds": rlt,
                "running_time": running_time,
            })
        df = pd.DataFrame(consolidated_results)
        return df

    def _calculate_rlt_for_riser_event(self, event_data: pd.DataFrame, next_product: str) -> List[RiserEventResult]:
        if event_data.empty:
            return []

        results = []

        event_data = event_data.sort_values(by="index").copy()
        start_time = event_data["index"].iloc[0]
        end_time = event_data["index"].iloc[-1]

        # PT: Criar pontos de corte nas viradas de dia
        # EN: Create split points at the end of each day
        split_points = [start_time]
        current_day = start_time.normalize() + timedelta(days=1)
        while current_day < end_time:
            split_points.append(current_day)
            current_day += timedelta(days=1)
        split_points.append(end_time)

        # PT: Dividir o bloco contínuo em partes por dia
        # EN: Split the continuous block into parts by day
        for i in range(len(split_points) - 1):
            slice_start = split_points[i]
            slice_end = split_points[i + 1]

            day_df = event_data[
                (event_data["index"] >= slice_start) & (event_data["index"] < slice_end)
            ].copy()

            if day_df.empty:
                continue

            msdp_row = day_df.iloc[-1].copy()
            msdp_row["ProductDescription"] = next_product
            ts = msdp_row["index"] if "index" in msdp_row else msdp_row.name
            msdp_row["Year"] = ts.year
            msdp_row["Month"] = ts.month

            msdp_data = self._msdp[
                self._msdp["reportingLineExternalId"] == self._reporting_line_external_id
            ]
            combined_mask = create_product_match_filter(msdp_data, next_product)
            product_filter = combined_mask if combined_mask.any() else msdp_data["piTagValue"] == ""
            sr_value = utils_get_msdp_value(msdp_row, msdp_data, "scheduledRate", product_filter)
            if isinstance(sr_value, (list, np.ndarray)):
                sr_value = sr_value[0]

            # PT: Calcular produção e RLT
            # EN: Calculate production and RLT
            day_df = day_df.assign(NetProduction=self._net_production_fn(day_df))
            net_production = day_df["NetProduction"].sum()
            sr_per_hour = sr_value / 24
            running_time = day_df["duration_in_seconds"].sum() / 3600
            sr_adjusted = sr_per_hour * running_time
            rlt = 3600 * ((sr_adjusted - net_production) / (sr_per_hour)) if sr_adjusted else 0.0

            results.append(RiserEventResult(
                event_data=day_df,
                rlt=rlt,
                sr=sr_value,
                sr_adjusted=sr_adjusted,
                start_index=day_df.index[0],
                end_index=day_df.index[-1],
                net_production=net_production,
                running_time=running_time,
            ))

        return results

