from typing import List, Dict, Any

import pandas as pd

from ..infra.logger_adapter import get_logger

log = get_logger()


class EventsDelayService:
    """
    PT-BR: Serviço responsável por aplicar delay/atraso em eventos específicos baseado em configurações.
    Remove eventos que ainda estão dentro do período de delay (aguardando tempo suficiente desde o fim do evento).
    EN: Service responsible for applying delay to specific events based on configurations.
    Removes events that are still within the delay period (waiting for sufficient time since event end).
    """

    # PT-BR: Configuração de delay por unidade e tipos de eventos
    # EN: Delay configuration by unit and event types
    DELAY_CONFIGURATION: List[Dict[str, Any]] = [
        {"unit": "UNT-NANCMP", "event_types": ["3a", "3b", "3c"], "delay_minutes": 480},  # 8 hours
    ]

    def __init__(self, event_frames_df: pd.DataFrame) -> None:
        """
        PT-BR: Inicializa o serviço de delay de eventos.
        EN: Initializes the events delay service.

        :param event_frames_df: DataFrame contendo os eventos a serem processados
        :type event_frames_df: pd.DataFrame
        """
        self._event_frames_df = event_frames_df

    def apply_delay(self) -> pd.DataFrame:
        """
        PT-BR: Aplica o delay configurado aos eventos, removendo aqueles que ainda estão dentro do período de delay.
        Um evento será removido se: end_time + delay_minutes > hora_atual (ainda não passou tempo suficiente).
        Um evento será mantido se: end_time + delay_minutes <= hora_atual (já passou tempo suficiente).
        
        EN: Applies the configured delay to events, removing those that are still within the delay period.
        An event will be removed if: end_time + delay_minutes > current_time (not enough time has passed).
        An event will be kept if: end_time + delay_minutes <= current_time (enough time has passed).

        :return: DataFrame com os eventos filtrados (eventos que ainda estão no delay foram removidos)
        :rtype: pd.DataFrame
        """
        log.info("Applying delay to events based on configuration")

        if self._event_frames_df.empty:
            log.info("Empty dataframe provided, skipping delay application")
            return self._event_frames_df

        # PT-BR: Valida se as colunas necessárias existem no dataframe
        # EN: Validates if required columns exist in the dataframe
        required_columns = ["refUnitId", "event_definition", "end_time"]
        missing_columns = [col for col in required_columns if col not in self._event_frames_df.columns]

        if missing_columns:
            log.warning(
                f"Missing required columns {missing_columns} for delay application; skipping"
            )
            return self._event_frames_df

        # PT-BR: Cria uma cópia do dataframe para não modificar o original
        # EN: Creates a copy of the dataframe to avoid modifying the original
        result_df = self._event_frames_df.copy()

        # PT-BR: Obtém a hora atual em UTC para comparação
        # EN: Gets current time in UTC for comparison
        current_time = pd.Timestamp.now(tz="UTC")

        # PT-BR: Cria uma máscara inicial com todos os eventos marcados para manter
        # EN: Creates an initial mask with all events marked to keep
        keep_mask = pd.Series(True, index=result_df.index)

        # PT-BR: Itera sobre cada regra de configuração (abordagem performática)
        # EN: Iterates over each configuration rule (performant approach)
        for config in self.DELAY_CONFIGURATION:
            unit = config["unit"]
            event_types = set(config["event_types"])
            delay_minutes = config["delay_minutes"]

            log.debug(
                f"Processing delay rule: unit={unit}, event_types={event_types}, delay_minutes={delay_minutes}"
            )

            # PT-BR: Filtra eventos que correspondem à unidade da configuração
            # EN: Filters events that match the configuration unit
            unit_mask = result_df["refUnitId"].astype(str) == unit

            if not unit_mask.any():
                log.debug(f"No events found for unit {unit}")
                continue

            # PT-BR: Filtra eventos que correspondem aos tipos de eventos da configuração
            # EN: Filters events that match the configuration event types
            event_type_mask = result_df["event_definition"].astype(str).isin(event_types)

            # PT-BR: Combina as máscaras: unidade E tipo de evento
            # EN: Combines masks: unit AND event type
            matching_events_mask = unit_mask & event_type_mask

            if not matching_events_mask.any():
                log.debug(
                    f"No events found matching unit {unit} and event_types {event_types}"
                )
                continue

            # PT-BR: Converte end_time para datetime se necessário e garante timezone UTC
            # EN: Converts end_time to datetime if needed and ensures UTC timezone
            end_times = pd.to_datetime(result_df.loc[matching_events_mask, "end_time"], errors="coerce")

            # PT-BR: Filtra apenas eventos com end_time válido (não NaT)
            # EN: Filters only events with valid end_time (not NaT)
            valid_time_mask = end_times.notna()

            if not valid_time_mask.any():
                log.warning(
                    f"No valid end_time found for unit {unit} and event_types {event_types}; skipping"
                )
                continue

            # PT-BR: Processa apenas eventos com tempo válido
            # EN: Processes only events with valid time
            valid_end_times = end_times[valid_time_mask]

            if valid_end_times.dt.tz is not None:
                valid_end_times = valid_end_times.dt.tz_convert("UTC")
            else:
                # PT-BR: Se não tiver timezone, assume UTC
                # EN: If no timezone, assumes UTC
                valid_end_times = valid_end_times.dt.tz_localize("UTC")

            # PT-BR: Calcula o tempo limite: end_time + delay_minutes
            # EN: Calculates the time limit: end_time + delay_minutes
            time_limit = valid_end_times + pd.Timedelta(minutes=delay_minutes)

            # PT-BR: Identifica eventos que devem ser removidos (time_limit > current_time)
            # Se time_limit > current_time, significa que ainda está dentro do período de delay
            # EN: Identifies events that should be removed (time_limit > current_time)
            # If time_limit > current_time, it means the event is still within the delay period
            events_to_remove = time_limit > current_time

            # PT-BR: Obtém os índices dos eventos que correspondem à regra
            # EN: Gets the indices of events that match the rule
            matching_indices = result_df.index[matching_events_mask]

            # PT-BR: Cria uma série completa com False para todos os eventos correspondentes
            # EN: Creates a complete series with False for all matching events
            full_events_to_remove = pd.Series(False, index=matching_indices)

            # PT-BR: Atualiza apenas os eventos com tempo válido com o resultado da verificação de delay
            # EN: Updates only events with valid time with the delay check result
            valid_indices = matching_indices[valid_time_mask]
            full_events_to_remove.loc[valid_indices] = events_to_remove.values

            # PT-BR: Atualiza a máscara de eventos a manter (marca como False os que devem ser removidos)
            # EN: Updates the mask of events to keep (marks as False those that should be removed)
            keep_mask.loc[matching_indices] = ~full_events_to_remove.values

            removed_count = full_events_to_remove.sum()
            if removed_count > 0:
                log.info(
                    f"Removed {removed_count} events for unit {unit} with event_types {event_types} "
                    f"that are still within delay period of {delay_minutes} minutes"
                )

        # PT-BR: Filtra o dataframe mantendo apenas os eventos que não devem ser removidos
        # EN: Filters the dataframe keeping only events that should not be removed
        filtered_df = result_df[keep_mask].copy()

        total_removed = len(result_df) - len(filtered_df)
        if total_removed > 0:
            log.info(f"Total of {total_removed} events removed (still within delay period)")
        else:
            log.info("No events were removed based on delay configuration")

        return filtered_df

