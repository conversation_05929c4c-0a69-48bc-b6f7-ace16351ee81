class BisCmpVariableCategoriesMappingService:
    def __init__(self) -> None:
        self._mapping_subcatlevel2_dict = {
            0: "",
            1: "",
            2: "Purging",
            3: "Turnaround",
            4: "I&E/DCT issue",
            5: "Mechanical Issue",
            6: "Quality issue",
            7: "Raw material unavailable",
            8: "",
            9: "Lack of Storage Capacity",
            10: "Color Checks",
            11: "Scheduled PM",
            12: "Warehouse Equipment Issue",
            13: "Blade Rap",
            14: "Torque Out",
            15: "",
            16: "Post Blend Delay",
            17: "Maintenance Unavailable",
        }
        
        self._mapping_subcatlevel1_dict = {
            0: "Running",
            1: "",
            2: "Transition",
            3: "Transition",
            4: "",
            5: "",
            6: "",
            7: "",
            8: "Not Scheduled to Run",
            9: "Packaging",
            10: "Waiting on Test Result",
            11: "",
            12: "Packaging",
            13: "Quench / Cutter / Water Removal",
            14: "Extruder",
            15: "Manpower",
            16: "Packaging",
            17: "Manpower",
        }

        self._mapping_eventcode_dict = {
            0: "",
            1: "",
            2: "Product & Supply Optimization",
            3: "Product & Supply Optimization",
            4: "Reactive Downtime",
            5: "Reactive Downtime",
            6: "Reactive Downtime",
            7: "Product & Supply Optimization",
            8: "Not Scheduled to Run",
            9: "Product & Supply Optimization",
            10: "Planned Downtime",
            11: "Planned Downtime",
            12: "Reactive Downtime",
            13: "Reactive Downtime",
            14: "Reactive Downtime",
            15: "Reactive Downtime",
            16: "Reactive Downtime",
            17: "Reactive Downtime",
        }

        self._mapping_metriccode_dict = {
            0: "",
            1: "Availability",
            2: "Availability",
            3: "Availability",
            4: "Availability",
            5: "Availability",
            6: "Availability",
            7: "Availability",
            8: "Loading",
            9: "Availability",
            10: "Availability",
            11: "Availability",
            12: "Availability",
            13: "Availability",
            14: "Availability",
            15: "Availability",
            16: "Availability",
            17: "Availability",
        }

    def map(self, prod_line_status: int, property: str) -> str:
        property_dict_mapping = {
            "subCategoryLevel2": self._mapping_subcatlevel2_dict,
            "subCategoryLevel1": self._mapping_subcatlevel1_dict,
            "eventCode": self._mapping_eventcode_dict,
            "metricCode": self._mapping_metriccode_dict,
        }
        property_dict = property_dict_mapping.get(property)
        return property_dict.get(prod_line_status)
