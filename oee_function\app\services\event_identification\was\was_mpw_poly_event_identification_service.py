from typing import Any, Optional

import pandas as pd

from app.models.lead_product import LeadProduct
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import (
    RateLossTimeContinuous,
)
from app.services.event_identification.was.washington_works_event_identification_service import (
    WashingtonWorksEventIdentificationService,
)
from app.utils.constants import Constants as const
from app.utils.event_detection_utils import detect_not_runnings_type_two


class WASMPWPolyEventIdentificationService(WashingtonWorksEventIdentificationService):
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
        product_transition: Optional[dict[str, dict[str, dict[str, int]]]],
        lead_products: Optional[list[LeadProduct]],
    ) -> None:

        self._mdr = mdr
        self._product_transition = product_transition
        self._not_running_start = pd.Series(dtype=bool)
        self._not_running_end = pd.Series(dtype=bool)
        self._not_running_with_transitions = pd.DataFrame()
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._total_produced = pd.Series(dtype=float)
        self.available_products = mdr["piTagValue"].to_list() if mdr is not None else []
        self._lead_product = next(
            (
                x
                for x in (lead_products or [])
                if x.reporting_line.external_id == reporting_line_external_id
            ),
            None,
        )

        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=(
                mdr.rename(
                    columns={
                        "unitAvgRate": const.MSDP,
                        "scheduledRate": const.SCHEDULED_RATE,
                    }
                ).assign(effective_date_datetime="2025-01-01")
                if mdr is not None
                else pd.DataFrame()
            ),
            multi_product=True,
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        processed_data = self.process_not_running_with_transitions(data)

        if processed_data.empty:
            return data

        if "event1c_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1c_start"], inplace=True)

        if "event1c_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1c_end"], inplace=True)

        return processed_data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self, data: pd.DataFrame, **args):
        """
        identifies the events of type Ic

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        processed_data = self.process_not_running_with_transitions(data)

        if processed_data.empty:
            return data

        if "event1a_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1a_start"], inplace=True)

        if "event1a_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1a_end"], inplace=True)

        return processed_data

    def identify_events_typeIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """
        # event trigger start
        start_trigger = self.product_trial_start_fn(data)
        event2a_start = start_trigger & (start_trigger.shift(1) != True)

        # event trigger end
        end_trigger = self.product_trial_end_fn(data)
        event2a_end = end_trigger & (end_trigger.shift(1) != True)

        data = data.assign(event2a_start=event2a_start, event2a_end=event2a_end)

        return data

    def identify_events_typeIIb(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIIc(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """
        data = detect_not_runnings_type_two(
            data,
            self.get_type_two_trigger,
            equal_product=True,
            start_key="event2c_start",
            end_key="event2c_end",
        )

        return data

    def identify_events_typeIId(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IId

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """
        data = detect_not_runnings_type_two(
            data,
            self.get_type_two_trigger,
            equal_product=False,
            start_key="event2d_start",
            end_key="event2d_end",
        )

        return data

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args):
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(
            data, "sum", [const.MSDP, const.SCHEDULED_RATE], shift_value=1
        )

        if day_data.empty:
            return day_data
        
        if "msdp" in day_data.columns.to_list():
            day_data[const.TOTAL_PRODUCED] = day_data[const.TOTAL_PRODUCED] * 24
            day_data.drop(columns=["msdp", "scheduledRate"], inplace=True)

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]

        return day_data

    def identify_events_typeIIIb(self, data: pd.DataFrame, **args):
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(
            data, "sum", [const.MSDP, const.SCHEDULED_RATE], shift_value=1
        )

        if day_data.empty:
            return day_data

        if "msdp" in day_data.columns.to_list():
            day_data[const.TOTAL_PRODUCED] = day_data[const.TOTAL_PRODUCED] * 24
            day_data.drop(columns=["msdp", "scheduledRate"], inplace=True)

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIIIc(self, data: pd.DataFrame, **args):
        """
        identifies events of type IIIc

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(
            data, "sum", [const.MSDP, const.SCHEDULED_RATE], shift_value=1
        )

        if day_data.empty:
            return day_data

        if "msdp" in day_data.columns.to_list():
            day_data[const.TOTAL_PRODUCED] = day_data[const.TOTAL_PRODUCED] * 24
            day_data.drop(columns=["msdp", "scheduledRate"], inplace=True)

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIVa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IVa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IV event
        :rtype: pd.DataFrame
        """

        # event trigger start
        start_trigger = self.producing_waste_start_fn(data)
        event4a_start = start_trigger & (start_trigger.shift(1) != True)

        # event trigger end
        end_trigger = self.producing_waste_end_fn(data)
        event4a_end = end_trigger & (end_trigger.shift(1) != True)

        data = data.assign(event4a_start=event4a_start, event4a_end=event4a_end)

        return data

    def identify_events_typeIVb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identify Producing Waste by Quality Control events (type IVb).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of type IVb events.
        :rtype: pd.DataFrame
        """
        if self._reporting_line_external_id == "RLN-WASMPWSPP":
            data["PlopTotalizer"] = 0

        data_copy = data.copy()

        producing_waste_tags = ["PlopTotalizer", "DivertTotalizer"]

        freq = "12h"

        offset = pd.Timedelta(hours=5, minutes=30)

        triggers = {
            "NotRunning": {
                "start": self.not_running_start_fn,
                "end": self.not_running_end_fn,
            },
            "ProductTrial": {
                "start": self.product_trial_start_fn,
                "end": self.product_trial_end_fn,
            },
            "NotRunningDuringProductTrial": {
                "start": self.not_running_during_product_trial_start_fn,
                "end": self.not_running_during_product_trial_end_fn,
            },
            "ProducingWaste": {
                "start": self.producing_waste_start_fn,
                "end": self.producing_waste_end_fn,
            },
        }

        return self.process_producing_waste_by_quality_control(
            data_copy,
            producing_waste_tags,
            self._mdr,
            self._lead_product,
            freq,
            offset,
            triggers,
            "4b"
        )

    def process_not_running_with_transitions(self, df: pd.DataFrame) -> pd.DataFrame:
        if not self._not_running_with_transitions.empty:
            return self._not_running_with_transitions.copy()

        self._not_running_with_transitions = self.identify_not_running_with_transitions(
            self._reporting_line_external_id,
            df,
            self.not_running_start_fn,
            self.not_running_end_fn,
            self.available_products,
            self._product_transition,
            3,
        )

        return self._not_running_with_transitions.copy()

    def not_running_start_fn(self, data: pd.DataFrame) -> pd.Series:
        # Feed Flow < 1lb/h AND Product Description Tag reports a Product code that is ON a predefined list
        return (
            (data["FeedFlow"] < 1) 
            & (data[const.PRODUCT_DESCRIPTION].isin(self.available_products))
        )

    def not_running_end_fn(self, data: pd.DataFrame) -> pd.Series:
        # Feed Flow >= 1lb/h OR Product Description Tag reports a Product code that is NOT on a predefined list
        return (
            (data["FeedFlow"] >= 1) 
            | (~data[const.PRODUCT_DESCRIPTION].isin(self.available_products))
        )

    def product_trial_start_fn(self, data: pd.DataFrame) -> pd.Series:
        # Feed Flow >= 1lb/h AND Product Description Tag reports a Product code that is NOT on a predefined list
        return (
            (data["FeedFlow"] >= 1)
            & (~data[const.PRODUCT_DESCRIPTION].isin(self.available_products))
        )

    def product_trial_end_fn(self, data: pd.DataFrame) -> pd.Series:
        # Feed Flow < 1lb/h OR Product Description Tag reports a Product code that is NOT on a predefined list
        return (
            (data["FeedFlow"] < 1)
            | (data[const.PRODUCT_DESCRIPTION].isin(self.available_products))
        )

    def not_running_during_product_trial_start_fn(
        self, data: pd.DataFrame
    ) -> pd.Series:
        # Feed Flow < 1lb/h AND
        # Product Description Tag reports a Product code that is NOT on a predefined list
        return (
            (data["FeedFlow"] < 1)
            & (~data[const.PRODUCT_DESCRIPTION].isin(self.available_products))
        )

    def not_running_during_product_trial_end_fn(self, data: pd.DataFrame) -> pd.Series:
        # Feed Flow >= 1lb/h OR
        # Product Description Tag reports a Product code that is in a predefined list
        return (
            (data["FeedFlow"] >= 1) 
            | (data[const.PRODUCT_DESCRIPTION].isin(self.available_products))
        )

    def producing_waste_start_fn(self, data: pd.DataFrame) -> pd.Series:
        # Feed Flow >= 1lb/h AND
        # Divert Valve equals DIVERT AND
        # Product Description Tag reports a Product code that is in a predefined list
        return (
            ((data["FeedFlow"] >= 1)
            & (data["DivertValve"] == "DIVERT"))
            & (data[const.PRODUCT_DESCRIPTION].isin(self.available_products))
        )

    def producing_waste_end_fn(self, data: pd.DataFrame) -> pd.Series:
        # Feed Flow < 1lb/h OR
        # Divert Valve equals PROD OR
        # Product Description Tag reports a Product code that is not on a predefined list
        return (
            ((data["FeedFlow"] < 1)
            | (data["DivertValve"] == "PROD"))
            | (~data[const.PRODUCT_DESCRIPTION].isin(self.available_products))
        )

    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced

        net_production = ((df["FeedFlow"] * df["index"].diff().dt.total_seconds().fillna(0).shift(-1)) / 3600) / 24

        self._total_produced = net_production

        return net_production

    def not_running_fn(self, data: pd.DataFrame) -> pd.DataFrame:
        # Modified running_time calculation
        not_running_condition = (
            (data["FeedFlow"] < 1)
            & (data[const.PRODUCT_DESCRIPTION].isin(self.available_products))
        )

        return not_running_condition

    def get_type_two_trigger(
        self, data: pd.DataFrame, start_key: str, end_key: str
    ) -> pd.DataFrame:
        # event trigger start
        data[start_key] = self.not_running_during_product_trial_start_fn(data)

        # event trigger end
        data[end_key] = self.not_running_during_product_trial_end_fn(data)

        return data
