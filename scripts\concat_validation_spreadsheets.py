import os
import sys

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

import pandas as pd


class ConcatValidationSpreadSheets:
    def read_spreadsheets(self, line: str, months: list[str], sheet_name: str) -> list[pd.DataFrame]:
        data_frames: list[pd.DataFrame] = []
        
        for month in months:
            data_frames.append(self.read_spreadsheet(line, month, sheet_name))

        return data_frames

    def read_spreadsheet(self, line: str, month: str, sheet_name:str) -> pd.DataFrame:
        folder: str = f"RLN-WASMPWZ0{line}"
        file = f"Z0{line}-{month}.xlsx"
        sheet_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
            "data",
            folder,
            file,
        )
        return pd.read_excel(sheet_path, sheet_name=sheet_name)

    def concat_data_frames(self, data_frames: list[pd.DataFrame]) -> pd.DataFrame:
        return pd.concat(data_frames, ignore_index=True)

    def run(self, lines: list[str], sheet_names: list[str], months: list[str]):
        for line in lines:
            for name in sheet_names:
                data_frames = self.read_spreadsheets(line, months, name)
                data_frame = self.concat_data_frames(data_frames)
                data_frame.to_excel(f"{name}-{line}.xlsx", index=False)


if __name__ == "__main__":
    lines = ["1", "2"]
    sheet_names = ["DATA", "HOURLY_DATA", "EVENTS"]
    months = ["Jan", "Fev", "Mar", "Abr", "May", "June", "July", "Aug", "Sept", "Out", "Nov", "Dec"]
    concat_validation_spreadsheet = ConcatValidationSpreadSheets()
    concat_validation_spreadsheet.run(lines, sheet_names, months)

