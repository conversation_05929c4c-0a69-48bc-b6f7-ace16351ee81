import pandas as pd


class BisChemicalsMO3ProductDescriptionMappingService:
    def __init__(self) -> None:
        pass

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        data[time_series] = "FORMALDEHYDE 37% HCHO3"
        return data


class BisChemicalsMO4ProductDescriptionMappingService:
    def __init__(self) -> None:
        pass

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        data[time_series] = "FORMALDEHYDE 37% HCHO4"
        return data


class BisChemicalsPRFMProductDescriptionMappingService:
    def __init__(self) -> None:
        pass

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        data[time_series] = "Paraformaldehyde 91-93%"
        return data
