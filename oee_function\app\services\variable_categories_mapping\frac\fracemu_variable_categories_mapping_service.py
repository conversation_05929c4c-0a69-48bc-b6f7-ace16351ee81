from typing import Any

import pandas as pd

class FracEmuVariableCategoriesMappingService:
    def __init__(self) -> None:
        self._no_demand = "No Demand"
        self._turnarounds = "Turnarounds"
        self._inventory_control = "Inventory Control"
        self._planned_annual_turnaround = "Planned Annual Turnaround"
        self._loading = "Loading"
        self._availability = "Availability"

        self._kein = "KEIN"
        self._stillstand = "STILLSTAND"

        self._product_description = [
            self._kein,
            self._stillstand,
        ]

        self._mapping_subcatlevel2_dict = {
            self._kein: self._inventory_control,
            self._stillstand: self._planned_annual_turnaround,
        }

        self._mapping_subcatlevel1_dict = {
            self._kein: self._no_demand,
            self._stillstand: self._turnarounds,
        }

        self._mapping_eventcode_dict = {
            self._kein: self._no_demand,
            self._stillstand: self._turnarounds,
        }

        self._mapping_metriccode_dict = {
            self._kein: self._loading,
            self._stillstand: self._availability,
        }

    def map(self, cat_column: str, property: str) -> str:
        property_dict_mapping = {
            "subCategoryLevel2": self._mapping_subcatlevel2_dict,
            "subCategoryLevel1": self._mapping_subcatlevel1_dict,
            "eventCode": self._mapping_eventcode_dict,
            "metricCode": self._mapping_metriccode_dict,
        }
        property_dict = property_dict_mapping.get(property)
        return property_dict.get(cat_column)
    
    def classify_frankfurt_event(
        self,
        row: pd.Series,
        events_hierarchy_mapper: dict[str, dict[str, Any]]
    ):
        has_variable_categories = events_hierarchy_mapper.get(
            row.event_definition
        ).get("variableCategories")

        definition_event = events_hierarchy_mapper.get(row.event_definition, {}).get('eventDefinition')
        row['def'] = definition_event

        matched_desc = None
        for prod_desc in self._product_description:
            if row["ProductDescription"].upper().startswith(prod_desc):
                matched_desc = prod_desc
                break

        if has_variable_categories and matched_desc:
            row['subcat_level1'] = self.map(matched_desc, 'subCategoryLevel1')
            row['event_code'] = self.map(matched_desc, 'eventCode')
            row['metric_code'] = self.map(matched_desc, 'metricCode')
            row['subcat_level2'] = self.map(matched_desc, 'subCategoryLevel2')

        else:
            row['subcat_level1'] = events_hierarchy_mapper.get(row.event_definition, {}).get('subCategoryLevel1')
            row['event_code'] = events_hierarchy_mapper.get(row.event_definition, {}).get('eventCode')
            row['metric_code'] = events_hierarchy_mapper.get(row.event_definition, {}).get('metricCode')
            row['subcat_level2'] = events_hierarchy_mapper.get(row.event_definition, {}).get('subCategoryLevel2')

        return row