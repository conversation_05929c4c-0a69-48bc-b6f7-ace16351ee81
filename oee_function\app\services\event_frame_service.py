import gc
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union

import numpy as np
import pandas as pd
from app.enums.entity_code import EntityCode
from app.enums.event_categorization import (
    EventCodeEnum,
    EventDefinitionEnum,
    MetricCodeEnum,
    SubCategoryLevelOneEnum,
)
from app.models.event_frame import Event, EventFrameLastExecutionCollection
from app.models.input_tag_configuration import InputTagConfiguration
from app.models.reporting_site import ReportingSite
from app.models.reporting_site_configuration import (
    EventHierarchyConfiguration,
    ReportingSiteConfiguration,
)
from app.repositories.bbct_repository import BbctRepository
from app.repositories.event_frame_repository import EventFrameRepository
from app.repositories.hourly_data_repository import HourlyDataRepository
from app.repositories.lead_product_repository import LeadProductRepository
from app.repositories.loss_category_repository import LossCategoryRepository
from app.repositories.material_repository import MaterialRepository
from app.repositories.mdr_repository import MdrRepository
from app.repositories.msdp_repository import MsdpRepository
from app.repositories.process_repository import ProcessRepository
from app.repositories.product_repository import ProductRepository
from app.repositories.product_transition_repository import (
    ProductTransitionRepository,
)
from app.repositories.reporting_site_repository import ReportingSiteRepository
from app.repositories.timeseries_repository import TimeSeriesRepository
from app.services.event_frame_delete_batch_id_product_id import (
    event_frame_delete_batch_id_product_id,
)
from app.services.event_frame_fix_events import EventFixingService
from app.services.event_frame_input_retriever import EventFrameInputRetriever
from app.services.event_frame_minor_stop_processor import (
    EventFrameMinorStopProcessor,
)
from app.services.event_frame_not_running_processor import (
    EventFrameNotRunningProcessor,
)
from app.services.event_frame_opened_events import fix_opened_events
from app.services.event_frame_product_swap_processor import (
    EventFrameProductSwapProcessor,
)
from app.services.event_identification.event_identification import (
    EventIdentification,
)
from app.services.event_identification.event_identification_factory import (
    EventIdentificationFactory,
)
from app.services.event_identification.hourly_data_compounding import (
    HourlyDataCompounding,
)
from app.services.events_delay_service import EventsDelayService
from app.services.rlt_short_duration_service import RltShortDurationService
from app.services.scrap_and_rework_service import ScrapAndReworkService
from app.services.tag_value_mapping.tag_value_mapping_factory import (
    TagValueMappingFactory,
)
from app.services.variable_categories_mapping.variable_categories_mapping_factory import (
    VariableCategoriesMapping,
    VariableCategoriesMappingFactory,
)
from app.utils.constants import EventFrameConstants
from app.utils.date_utils import DateUtils
from app.utils.hierarchical_events import HierarchicalEventsService
from app.utils.id_generation import IdGenerator
from app.utils.mappers import METRIC_CODE_TO_LOSS_CATEGORY
from app.utils.rlt_utils import RLT_DEFINITIONS

from ..infra.logger_adapter import get_logger
from .base_service import BaseService

log = get_logger()

REGEX_BY_REPORTING_SITE = {
    "STS-ENR": rf"^\d+$|^\d*[a-zA-Z]?\d*[a-zA-Z]?(\d|[a-zA-Z]){{0,{2}}}$"
}

RLT_DEFS = RLT_DEFINITIONS


class EventFrameService(BaseService):
    def __init__(
        self,
        reporting_site_repository: ReportingSiteRepository,
        time_series_repository: TimeSeriesRepository,
        bbct_repository: BbctRepository,
        event_frame_repository: EventFrameRepository,
        hourly_data_repository: HourlyDataRepository,
        default_instance_space: str,
        asset_hierarchy_instances_space: str,
        msdp_repository: MsdpRepository,
        mdr_repository: MdrRepository,
        lead_product_repository: LeadProductRepository,
        product_repository: ProductRepository,
        material_repository: MaterialRepository,
        process_repository: ProcessRepository,
        id_generator: IdGenerator[EntityCode],
        loss_category_repository: LossCategoryRepository,
        product_transition_repository: ProductTransitionRepository,
    ) -> None:
        self._event_frame_repository = event_frame_repository
        self._hourly_data_repository = hourly_data_repository
        self._input_retriever = EventFrameInputRetriever(
            reporting_site_repository,
            time_series_repository,
            bbct_repository,
            event_frame_repository,
            default_instance_space,
            msdp_repository,
            mdr_repository,
            product_transition_repository,
        )
        self._minor_stop_processor = EventFrameMinorStopProcessor()
        self.product_swap_processor = EventFrameProductSwapProcessor()
        self._not_running_processor = EventFrameNotRunningProcessor()
        self._product_repository = product_repository
        self._material_repository = material_repository
        self._process_repository = process_repository
        self._lead_product_repository = lead_product_repository
        self._asset_hierarchy_instances_space = asset_hierarchy_instances_space
        self._id_generator = id_generator
        self._loss_category_repository = loss_category_repository

    def transform_time_series_to_event_frames(
        self,
        reporting_site_external_id: str,
        start_time: Optional[datetime],
        end_time: Optional[datetime],
        only_lines: List[str],
        export_xls: bool = False,
        save_events: bool = True,
    ) -> None:
        # true if start_time is different from None (indicates historical data)
        is_historical_data = start_time is not None

        inputs = self._input_retriever.retrieve(
            reporting_site_external_id, start_time, end_time, only_lines
        )
        space = inputs.space
        configurations = inputs.configurations
        start_time = inputs.start_time
        end_time = inputs.end_time
        last_execution = inputs.last_execution_metadata
        bbct_data = inputs.bbct_data
        msdp_data = inputs.msdp_data
        mdr_data = inputs.mdr_data
        reporting_site = inputs.reporting_site
        product_transition = inputs.product_transition

        tag_configuration_by_reporting_line = (
            inputs.configurations.get_tag_configuration_by_reporting_line()
        )

        lead_products = self._lead_product_repository.get_all(
            filter={
                "and": [
                    {"refReportingLine": {"externalId": {"in": only_lines}}},
                    {"endDate": {"isNull": True}},
                ]
            }
        )

        identification_factory = EventIdentificationFactory(
            configurations,
            msdp=msdp_data,
            mdr=mdr_data,
            bbct=bbct_data,
            product_transition=product_transition,
            lead_products=lead_products,
        )
        tag_value_mapping = TagValueMappingFactory()

        product_names_to_search: set[str] = set()
        process_names_to_search: set[str] = set()

        shift_sites = inputs.configurations.shifts.copy()
        # Start interation by reporting line
        for (
            reporting_line,
            tag_configurations,
        ) in tag_configuration_by_reporting_line.items():
            event_frames_df: pd.DataFrame = pd.DataFrame()
            hourly_data: Optional[pd.DataFrame] = None

            if reporting_line in EventFrameConstants.IGNORE_LINES:
                log.info("Skipping line: " + reporting_line)
                continue

            if only_lines and reporting_line not in only_lines:
                continue

            events_hierarchy = (
                configurations.get_events_hierarchy_by_reporting_line(
                    reporting_line
                )
            )

            configurations.shifts = shift_sites

            for event in events_hierarchy:
                if event.event_hierarchy_shifts:
                    configurations.shifts = event.event_hierarchy_shifts.copy()
                    break

            event_identification_service = identification_factory.create(
                reporting_line
            )

            timeseries_by_alias = self._build_timeseries_by_alias_dict(
                tag_configurations=tag_configurations
            )

            data_cdf = self._input_retriever.retrieve_datapoints(
                configurations, start_time, end_time, reporting_line
            )

            if data_cdf.empty:
                log.info(f"No datapoints found for {reporting_line}.")
                continue

            data_timeseries_configurations = (
                self._input_retriever.retrieve_timeseries_configurations(
                    configurations, reporting_line
                )
            )

            data_timeseries_configurations = self._add_alias_column(
                timeseries_configurations=data_timeseries_configurations,
                rename_dict=timeseries_by_alias,
            )

            data_cdf = self._rename_from_external_id_to_alias(
                data=data_cdf,
                rename_dict=timeseries_by_alias,
            )

            data_cdf = self._fill_invalid_values_with_last_valid(data_cdf)

            for tag_config in tag_configurations:
                if not tag_config.tag_value_mapping:
                    continue

                tag_value_mapping_service = tag_value_mapping.create(
                    reporting_line=reporting_line,
                    tag_alias=tag_config.alias,
                )
                data_cdf = tag_value_mapping_service.map(
                    data=data_cdf,
                    time_series=tag_config.alias,
                )
            log.info("Transforming and fixing data quality")

            data_cdf = self.fix_null_values(
                data=data_cdf,
                subset=configurations.fix_null_values_subset,
                reporting_line=reporting_line,
            )

            data_cdf = EventFixingService.fill_product_from_lead_product(
                data_df=data_cdf,
                reporting_line=reporting_line,
                lead_product_repository=self._lead_product_repository,
                apply_to_columns=[
                    "Product",
                    "ProductID",
                    "ProductDescription",
                    "piTagValue",
                ],
            )

            data_cdf = self._apply_reporting_line_cutoff(
                data_cdf, last_execution, reporting_line, end_time
            )

            if data_cdf.empty:
                continue

            data_cdf = self._fix_product_data(
                data_cdf, configurations.shifts, reporting_line
            )

            data_cdf["dt"] = (
                data_cdf.index.to_series().diff().dt.total_seconds().fillna(0)
            )

            data_status = data_cdf.copy()

            data_cdf_dev = data_cdf.copy() if export_xls else None

            self.create_list_valid_batch_ids(
                REGEX_BY_REPORTING_SITE.get(reporting_site_external_id),
                data_status,
            )

            data_status.reset_index(inplace=True)

            pc_type = tag_configuration_by_reporting_line[reporting_line][
                0
            ].reporting_line.process_type.name

            events_definition_with_work_shift_rule = {
                event.event_hierarchy
                for event in events_hierarchy
                if event.work_shift_rule is not None
                and event.work_shift_rule
                and event.event_hierarchy
            }
            events_data_list = self._process_events(
                data_status,
                events_hierarchy,
                event_identification_service,
                configurations,
                last_execution,
                events_definition_with_work_shift_rule,
                reporting_line,
                end_time,
                pc_type,
            )

            if not events_data_list or all(
                df.empty for df in events_data_list
            ):
                log.info("No data to process")
                continue

            data = pd.concat(events_data_list, ignore_index=True)

            log.info("Checking and applying minor stops rules")

            data = self._minor_stop_processor.apply_minor_stop_rule(
                data, configurations, reporting_line
            )
            data = self.product_swap_processor.apply_product_swap_rule(
                data, configurations, reporting_line
            )
            data = self._not_running_processor.apply_not_running_rule(
                data, configurations, reporting_line
            )

            log.info("Creating Events IDs")

            data = self._create_events_ids(data, configurations)

            data_cdf.drop(columns=["dt"], inplace=True)

            log.info("Joining Product information to event frames")

            data = self._join_events_products(
                events_data=data,
                product_data=data_cdf,
                tag_configurations=tag_configurations,
            )

            reporting_unit = (
                configurations.get_reporting_unit_by_reporting_line(
                    reporting_line
                )
            )

            # TODO: Put this inside Enoree identification service or inside configs
            # ensure no Inactive Product comes for events of type 3b
            if reporting_site_external_id == "STS-ENR":
                mask = (data["event_definition"] == "3b") & (
                    data["Product"] == "Inactive"
                )
                data.drop(index=data[mask].index, inplace=True)

            data["original_total_duration_seconds"] = data[
                "total_duration_seconds"
            ]

            if reporting_line in {"RLN-SHERDPDR1"}:
                pc_type = "Continuous"

            if reporting_site.external_id in {"STS-NPT"}:
                pc_type = "Compounding"

            if pc_type == "Batch":
                data = EventFixingService.adjust_batch_loss_for_batch_hold(
                    data, events_hierarchy
                )
                lead_bbct = EventFixingService._get_lead_bbct_value(
                    reporting_line_id=reporting_line,
                    lead_product_repository=self._lead_product_repository,
                )

                log.info("Fixing event 3b duration based on BBCT value")
                data = event_identification_service.fix_3b_duration_based_BBCT(
                    data,
                    bbct_data,
                    lead_bbct,
                )

            elif pc_type == "Continuous" and reporting_line == "RLN-NARFLKD09":
                data = event_identification_service.implement_RLT(
                    data, data_cdf.reset_index()
                )

            elif (
                pc_type == "Compounding"
                and reporting_line
                not in EventFrameConstants.LINES_BY_PASS_COMPOUNDING
            ):
                hourly_data_service = HourlyDataCompounding(reporting_line)
                data, hourly_data = (
                    hourly_data_service.identify_events_typeIII(
                        data,
                        data_cdf,
                        mdr_data,
                        configurations,
                        data_timeseries_configurations,
                    )
                )

            """
            PT: Para as linhas de Geleen, a duraçao dos eventos do tipo 4 a duraçao deve ser baseada no BBCT
            EN: For Geleen lines, the duration of type 4 events should be based on BBCT
            """
            if reporting_line in {
                "RLN-GELVAE6K2",
                "RLN-GELVAE6K3",
                "RLN-GELVAE6K4",
            }:
                data = event_identification_service.fix_events_type4_duration_based_BBCT(
                    data, bbct_data
                )

            data = self._rename_categorization_columns(
                data, events_hierarchy, reporting_line, configurations
            )

            if pc_type == "Batch":
                self._fix_event_with_bbct_without_value(
                    data, reporting_line, configurations
                )

            if (
                pc_type == "Compounding"
                and reporting_line
                not in EventFrameConstants.LINES_BY_PASS_COMPOUNDING
            ):
                hourly_data = self._create_additional_columns(
                    hourly_data, configurations, reporting_line
                )

            self._fill_product_names_to_search(data, product_names_to_search)
            self._fill_process_names_to_search(data, process_names_to_search)

            data = self._create_additional_columns(
                data, configurations, reporting_line
            )

            data.drop_duplicates(
                subset=["start_time", "def", "refReportingLineId"],
                inplace=True,
            )

            event_frames_df = pd.concat(
                [event_frames_df, data], ignore_index=True
            )

            if event_frames_df.shape[0] == 0:
                log.info(f"No data to process for line {reporting_line}")
                del event_frames_df
                del data_cdf
                gc.collect()  # force garbage colector
                return

            event_frames_df = self._fill_material_data(
                event_frames_df, mdr_data, msdp_data, bbct_data
            )
            event_frames_df = self._fill_oee_product_data(
                event_frames_df=event_frames_df,
                product_names=list(product_names_to_search),
                reporting_site=reporting_site,
            )

            # PT-BR: Categoriza eventos RLT de baixa duração (< 15 minutos)
            # EN: Categorizes short duration RLT events (< 15 minutes)
            rlt_short_duration_service = RltShortDurationService(
                event_frames_df, pc_type
            )
            event_frames_df = (
                rlt_short_duration_service.categorize_short_duration_rlt()
            )

            event_frames_df = self._fill_oee_detail_data(
                event_frames_df=event_frames_df
            )

            self.add_transition_data(
                event_frames_df, product_transition.get(reporting_line, {})
            )

            # PT: Ajusta eventos hierárquicos
            # EN: Fix hierarchical events
            # Regras específicas por unidade (ex.: ignorar 4c para UNT-WASSCD)
            hierarchical_service = HierarchicalEventsService(
                unit=reporting_unit
            )
            event_frames_df = hierarchical_service.fix_hierarchical_events(
                event_frames_df
            )

            # Fix Scrap and Rework duration
            # EN: Checks if the unit is configured in UNITS_WITH_SCRAP_OR_REWORK
            # PT-BR: Verifica se a unidade está configurada em UNITS_WITH_SCRAP_OR_REWORK
            units_with_scrap_or_rework = [
                unit_config.get("unit")
                for unit_config in EventFrameConstants.UNITS_WITH_SCRAP_OR_REWORK
            ]
            if reporting_unit in units_with_scrap_or_rework:
                scrap_and_rework_service = ScrapAndReworkService(
                    event_frames_df=event_frames_df,
                    pc_type=pc_type,
                    hourly_data=hourly_data,
                )
                event_frames_df = scrap_and_rework_service.fix_duration(
                    reporting_unit, reporting_line
                )

                # DESATIVADO EM 15/01/2026 - Alinhado com Gustavo Bastista pois perderiamos a rastreabilidade de eventos que cruzam turnos
                # if reporting_site_external_id == "STS-WAS":
                #     event_frames_df = scrap_and_rework_service.fix_start_end_time(
                #         event_frames_df,
                #         configurations.shifts
                #     )

            # filtering events with duration less than 1 minute | us128890
            # and not RLT | us189157
            event_frames_df = event_frames_df.query(
                "total_duration_seconds > 60 or total_duration_seconds < -60 or `def` in @RLT_DEFINITIONS"
            )

            if event_frames_df.shape[0] == 0:
                log.info(f"No data to process for line {reporting_line}")
                del event_frames_df
                del data_cdf
                gc.collect()  # force garbage colector
                return

            # Delete Batch ID and Product ID for specific events | us116161
            event_frames_df = event_frame_delete_batch_id_product_id(
                event_frames_df
            )

            # Fix Opened Events
            event_frames_df = fix_opened_events(event_frames_df)

            # Fix Events - Apply Roles
            event_frames_df = (
                EventFixingService.fix_minor_stops_within_starting_up(
                    event_frames_df
                )
            )

            del data_cdf
            gc.collect()  # force garbage colector

            log.info(
                f"Check if events existing in FDM for line {reporting_line}, and if status is Unassigned, if so, update the status to Unassigned"
            )

            start_times = DateUtils.prepare_date_to_search(
                event_frames_df["start_time"]
            )

            existing_events_df = (
                self._event_frame_repository.get_existing_events(
                    reporting_line_id=reporting_line,
                    event_definition=set(event_frames_df["def"].tolist()),
                    start_date_time=start_times.tolist(),
                )
            )

            if existing_events_df.empty:
                existing_events_df = pd.DataFrame(
                    columns=[
                        "externalId",
                        "status",
                        "eventDefinition",
                        "startDateTime",
                    ]
                )

            # Create a temporary key for merging based on event definition and start time
            event_frames_df["merge_key"] = (
                event_frames_df["def"].astype(str)
                + "_"
                + start_times.astype(str)
            )
            existing_events_df["merge_key"] = (
                existing_events_df["eventDefinition"].astype(str)
                + "_"
                + existing_events_df["startDateTime"].astype(str)
            )

            # Merge using the temporary key instead of externalId
            event_frames_df = event_frames_df.merge(
                existing_events_df, on="merge_key", how="left", indicator=True
            )

            event_frames_df = event_frames_df.drop(
                columns=["_merge", "merge_key"]
            )

            # PT-BR: Aplica delay configurado aos eventos, removendo aqueles que ainda estão dentro do período de delay
            # EN: Applies configured delay to events, removing those still within the delay period
            events_delay_service = EventsDelayService(event_frames_df)
            event_frames_df = events_delay_service.apply_delay()

            # PT-BR: Auto-assina eventos RLT de baixa duração (< 15 minutos)
            # EN: Auto-assigns short duration RLT events (< 15 minutes)
            rlt_short_duration_service = RltShortDurationService(
                event_frames_df, pc_type
            )
            event_frames_df = (
                rlt_short_duration_service.auto_assign_short_duration_rlt()
            )

            # fix events before save, change values, manipulate data
            event_frames_df = EventFixingService.fix_events_before_save(
                event_frames_df=event_frames_df,
                pc_type=pc_type,
                is_historical_data=is_historical_data,
                msdp_data=msdp_data,
                mdr_data=mdr_data,
                bbct_data=bbct_data,
                reporting_site_configuration=configurations,
                timeseries_configuration=data_timeseries_configurations,
                lead_product_repository=self._lead_product_repository,
                reporting_line=reporting_line,
            )

            # Filter events with status Unassigned or NaN
            new_events = EventFrameService.filter_new_events(event_frames_df)

            if export_xls:
                self._export_dev_excel(
                    data_cdf=data_cdf_dev,
                    hourly_data=hourly_data,
                    event_frames_df=event_frames_df,
                    reporting_line=reporting_line,
                )

            if not save_events:
                log.info(
                    f"Writing events aborted for line {reporting_line} - save_events is set to False"
                )
            elif not new_events.empty:
                log.info(
                    f"Writing event frames to FDM for line {reporting_line} - Find {event_frames_df.shape[0]} but upsert {new_events.shape[0]} events"
                )
                # Check if there are events to be created
                # If event not exists in FDM, create it
                # If event exists in FDM, but status is Unassigned, update data
                # If event exists in FDM, but status is Assigned, do nothing
                self._event_frame_repository.create_events(
                    [
                        Event.from_event_row(
                            row, space, self._asset_hierarchy_instances_space
                        )
                        for row in event_frames_df.replace(
                            np.nan, None
                        ).to_dict("records")
                        if row["externalId"]
                        in new_events["externalId"]
                        .replace(np.nan, None)
                        .values
                    ]
                )

            log.info(
                f"Clear Memory using Garbage Collector for line {reporting_line}"
            )

            del event_frames_df
            del new_events
            del data
            del data_status
            del existing_events_df
            gc.collect()  # force garbage colector

            # END - Interation by reporting line

        log.info("Pipeline execution completed successfully")

    @staticmethod
    def filter_new_events(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Returns only events that are eligible to be created or updated.

        Excludes:
        - Signed events (status not NaN and not "Unassigned")
        - Manual events (isManual == True)
        - Deleted events (isDeleted == True)
        """

        valid_events = pd.Series(True, index=event_frames_df.index)

        # Not update events when status = assigned, except for auto-assigned events
        auto_assigned_events = event_frames_df.apply(
            Event.is_auto_assigned, axis=1
        )
        valid_events &= (
            event_frames_df["status"].isna()
            | (event_frames_df["status"] == "Unassigned")
            | auto_assigned_events
        )

        # exclude events when isManual = true
        if "isManual" in event_frames_df.columns:
            valid_events &= event_frames_df["isManual"] != True

        # exclude events when isDeleted = true
        if "isDeleted" in event_frames_df.columns:
            valid_events &= event_frames_df["isDeleted"] != True
        return event_frames_df[valid_events]

    def _export_dev_excel(
        self,
        data_cdf: Optional[pd.DataFrame],
        hourly_data: Optional[pd.DataFrame],
        event_frames_df: pd.DataFrame,
        reporting_line: str,
    ) -> None:
        """
        Exports dataframes to an Excel file for development purposes.
        Creates a file with three sheets: DATA, HOURLY_DATA, and EVENTS.
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"dev_export_{reporting_line}_{timestamp}.xlsx"

        log.info(f"[DEV MODE] Exporting data to Excel: {filename}")

        with pd.ExcelWriter(filename, engine="openpyxl") as writer:
            if data_cdf is not None:
                data_cdf_export = self._convert_datetime_columns_to_str(
                    data_cdf.copy()
                )
                if isinstance(data_cdf_export.index, pd.DatetimeIndex):
                    data_cdf_export.index = data_cdf_export.index.astype(str)
                data_cdf_export.to_excel(writer, sheet_name="DATA", index=True)

            if hourly_data is not None:
                hourly_data_export = self._convert_datetime_columns_to_str(
                    hourly_data.copy()
                )
                hourly_data_export.to_excel(
                    writer, sheet_name="HOURLY_DATA", index=False
                )
            else:
                pd.DataFrame().to_excel(
                    writer, sheet_name="HOURLY_DATA", index=False
                )

            event_frames_export = self._convert_datetime_columns_to_str(
                event_frames_df.copy()
            )
            event_frames_export.to_excel(
                writer, sheet_name="EVENTS", index=False
            )

        log.info(f"[DEV MODE] Excel file exported successfully: {filename}")

    @staticmethod
    def _convert_datetime_columns_to_str(df: pd.DataFrame) -> pd.DataFrame:
        """
        Converts all datetime columns (with or without timezone) to string format.
        """
        for col in df.columns:
            if pd.api.types.is_datetime64_any_dtype(df[col]):
                df[col] = df[col].astype(str)
        return df

    @staticmethod
    def _fill_invalid_values_with_last_valid(df: pd.DataFrame) -> pd.DataFrame:
        """
        Replaces invalid values (numeric 0, 0.0, string '0', None, NaN)
        with the last valid value in the specified columns ('Batch', 'BatchID', 'ProcessOrder', 'Product', 'ProductID')
        of the DataFrame.
        """
        # Define the columns to process
        cols_to_process = [
            "Batch",
            "BatchID",
            "ProcessOrder",
            "Product",
            "ProductID",
            "ProductDescription",
        ]
        cols_to_iterate = [
            col for col in df.columns.to_list() if col in cols_to_process
        ]
        values_to_check = [
            "",
            "0",
            "0.0",
        ]  # Check for zero values and empty strings

        for col in cols_to_iterate:
            df[col] = df[col].shift(-3)
            df["IsInvalid"] = df[col].isnull() | df[col].apply(
                lambda x: str(x).strip() in values_to_check
            )
            df.loc[df["IsInvalid"], col] = np.nan
            df[col].ffill(inplace=True)

        if "IsInvalid" in df.columns.to_list():
            df.drop(columns=["IsInvalid"], inplace=True)

        # Fix ProductDescription when missing whitin product swap
        if (
            EventFrameConstants.PRODUCT in df.columns
            and EventFrameConstants.PRODUCT_DESCRIPTION in df.columns
        ):
            rows = (
                df[EventFrameConstants.PRODUCT].notna()
                & df[EventFrameConstants.PRODUCT_DESCRIPTION].isna()
                & df[EventFrameConstants.PRODUCT_DESCRIPTION].shift(-1).notna()
            )
            df.loc[rows, EventFrameConstants.PRODUCT_DESCRIPTION] = df[
                EventFrameConstants.PRODUCT_DESCRIPTION
            ].shift(-1)[rows]

        return df

    def _process_events(
        self,
        data_status: pd.DataFrame,
        events_hierarchy: list[EventHierarchyConfiguration],
        event_identification_service: EventIdentification,
        configurations: ReportingSiteConfiguration,
        last_execution: EventFrameLastExecutionCollection,
        events_definition_with_work_shift_rule: set[str],
        reporting_line: str,
        end_time: datetime,
        pc_type: str,
    ):
        events_data_list: list[pd.DataFrame] = []
        for event_hierarchy in events_hierarchy:
            data_event = self._process_event(
                data_status,
                event_hierarchy,
                event_identification_service,
                configurations,
                last_execution,
                events_definition_with_work_shift_rule,
                reporting_line,
                end_time,
                pc_type,
            )
            if data_event is not None:
                events_data_list.append(data_event)
        return events_data_list

    def _process_event(
        self,
        data_status: pd.DataFrame,
        event_hierarchy: EventHierarchyConfiguration,
        event_identification_service: EventIdentification,
        configurations: ReportingSiteConfiguration,
        last_execution: EventFrameLastExecutionCollection,
        events_definition_with_work_shift_rule: set[str],
        reporting_line: str,
        end_time: datetime,
        pc_type: str,
    ) -> Optional[pd.DataFrame]:
        if (
            event_hierarchy.business_rule is None
            or not event_hierarchy.business_rule
        ):
            return None

        event_def = event_hierarchy.event_hierarchy
        if not event_def:
            return None
        log.info(f"Identification of events of type {event_def}")
        methods = (
            event_identification_service.get_events_identification_methods()
        )
        method = methods.get(event_def)
        if not method:
            return None

        event_data_status = self._apply_event_cutoff(
            data_status,
            last_execution,
            reporting_line,
            event_hierarchy.event_definition,
            end_time,
        )

        if event_data_status.empty:
            return None

        data_event = method(
            event_data_status,
            valid_batch_ids=self.valid_batch_ids,
        )

        if data_event is None or data_event.empty:
            return None

        assert type(data_event) is pd.DataFrame

        data_event = EventFixingService.event_frame_column_renaming_mapper(
            data_event
        )

        if {"start_time", "end_time", "total_duration_seconds"}.issubset(
            data_event.columns
        ):
            data_event["event_definition"] = event_def
            return data_event

        data_event.dropna(
            subset=[f"event{event_def}_start", f"event{event_def}_end"],
            inplace=True,
        )

        data_product = data_event.copy()

        data_event.set_index("index", inplace=True)

        event_description = self._get_event_description(
            event_definition=event_def,
            reporting_line=reporting_line,
            event_hierarchy=configurations.events_hierarchy,
        )

        data_event = self.create_event_dataframe(
            data=data_event,
            event_definition=event_def,
            pc_type=pc_type,
            event_description=event_description,
        )

        if data_event.empty:
            return None

        if event_def in events_definition_with_work_shift_rule:
            data_event = self._identify_working_shifts_transitions(
                data=data_event,
                event_definition=event_def,
                shifts=configurations.shifts,
            )

        if "total_duration_seconds" in data_product:
            data_event = data_event.merge(
                data_product[["index", "total_duration_seconds"]],
                how="left",
                left_on=f"event{event_def}_start",
                right_on="index",
            ).drop(columns=["index"])
        else:
            log.info("Calculating events duration")
            data_event = self.calculate_time_duration(
                data_event,
                start_col=f"event{event_def}_start",
                end_col=f"event{event_def}_end",
            )

        rename_map = {
            f"event{event_def}_start": "start_time",
            f"event{event_def}_end": "end_time",
        }
        data_event.rename(columns=rename_map, inplace=True)

        self._execute_extra_param(
            data_event,
            event_def,
            configurations,
            data_product,
        )

        return data_event

    def _get_event_description(
        self,
        event_definition: str,
        event_hierarchy: list[EventHierarchyConfiguration],
        reporting_line: str,
    ) -> str:
        for config in event_hierarchy:
            if (
                config.reporting_line
                and config.reporting_line.external_id.upper()
                == reporting_line.upper()
                and config.event_hierarchy
                and config.event_hierarchy.upper() == event_definition.upper()
            ):
                return config.event_definition or ""

        raise None

    def _apply_reporting_line_cutoff(
        self,
        data_cdf: pd.DataFrame,
        last_execution: EventFrameLastExecutionCollection,
        reporting_line: str,
        end_time: datetime,
    ):
        cutoff_date = last_execution.get_min_execution_by_reporting_line(
            reporting_line
        )

        if cutoff_date:
            data_cdf = data_cdf[
                data_cdf.index.to_series().between(cutoff_date, end_time)
            ]

        return data_cdf

    def _apply_event_cutoff(
        self,
        data_status: pd.DataFrame,
        last_execution: EventFrameLastExecutionCollection,
        reporting_line: str,
        event_definition: str,
        end_time: datetime,
    ):
        event_cutoff_date = last_execution.get_min_execution_by_reporting_line(
            reporting_line, event_definition
        )

        event_data_status = data_status.copy()
        if event_cutoff_date:
            event_data_status = event_data_status[
                data_status["index"].between(event_cutoff_date, end_time)
            ]
            event_data_status.reset_index(inplace=True)
        return event_data_status

    def _execute_extra_param(
        self,
        data_event: pd.DataFrame,
        event_def: str,
        configurations: ReportingSiteConfiguration,
        data_product: pd.DataFrame,
    ):
        extra_processing_param = configurations.get_extra_processing_params()
        if not extra_processing_param:
            return

        # merge information of Batch ID for end_time
        data_event = pd.merge_asof(
            data_event,
            data_product[["index", "BatchID"]],
            left_on="end_time",
            right_on="index",
        )
        data_event = data_event.rename(columns={"BatchID": "BatchID_end"})

        # merge information of Batch ID for start_time
        data_event = pd.merge_asof(
            data_event,
            data_product[["index", "BatchID"]],
            left_on="start_time",
            right_on="index",
        )
        data_event = data_event.rename(columns={"BatchID": "BatchID_start"})

        data_event.drop(columns=["index_x", "index_y"], inplace=True)

        if event_def == extra_processing_param[0]:
            data_event.loc[
                (
                    (data_event["BatchID_end"] == "Inactive")  # code inactive
                    | (data_event["BatchID_end"] == "shutdown")
                    | (data_event["BatchID_end"] == "")  # code empty
                ),
                "end_time",
            ] += timedelta(minutes=extra_processing_param[1])

        data_event.drop(columns=["BatchID_end", "BatchID_start"], inplace=True)

    def _get_calculation_period(
        self,
        last_execution: EventFrameLastExecutionCollection,
        start_time: Optional[datetime],
        end_time: Optional[datetime],
    ) -> tuple[datetime, datetime]:
        if not end_time:
            end_time = datetime.now(timezone.utc)

        if not start_time:
            start_time = last_execution.get_min_execution() or datetime(
                2019, 1, 1, 0, 0, 0, 0, timezone.utc
            )
        return (start_time, end_time)

    def _create_additional_columns(
        self,
        data: pd.DataFrame,
        configurations: ReportingSiteConfiguration,
        reporting_line_external_id: str,
    ) -> pd.DataFrame:
        extra_columns = configurations.get_ingestions_extra_props(
            reporting_line_external_id
        )
        for key, value in extra_columns.items():
            data[key] = value

        return data

    def _create_events_ids(
        self, data: pd.DataFrame, configurations: ReportingSiteConfiguration
    ) -> pd.DataFrame:
        """
        create the events ids, using the business rules that events splitted
        by the working shift or the day should be the same id

        :param data: dataframe with events start and end times
        :type data: pd.DataFrame
        :return: dataframe with the events ids according to the business rules
        :rtype: pd.DataFrame
        """
        # pre-allocate column

        events_ids_shifts = [
            pd.Timestamp(shift).time().replace(microsecond=000000)
            for shift in configurations.shifts
        ]
        data["EventID"] = 0
        # iterate over all the indexes to identify events
        for j in range(1, data.shape[0]):
            if data.loc[j - 1, "end_time"].time() in events_ids_shifts:  # type: ignore
                if data.loc[j - 1, "end_time"] == data.loc[j, "start_time"]:
                    data.loc[j, "EventID"] = data.loc[j - 1, "EventID"]
            else:
                data.loc[j, "EventID"] = data.loc[j - 1, "EventID"] + 1  # type: ignore

        return data

    def _fix_event_with_bbct_without_value(
        self,
        data: pd.DataFrame,
        reporting_line: str,
        configurations: ReportingSiteConfiguration,
    ):
        def apply_event_dependends_on_bbct(
            event_hierarchy: str, reporting_line: str
        ) -> bool:
            return configurations.event_dependends_on_bbct(
                reporting_line, event_hierarchy
            )

        production_without_bbct_filter = (
            data["event_definition"].apply(
                apply_event_dependends_on_bbct,
                reporting_line=reporting_line,
            )
        ) & (data["total_duration_seconds"] == 0)

        data.loc[
            production_without_bbct_filter,
            "total_duration_seconds",
        ] = data["original_total_duration_seconds"]

        data.loc[production_without_bbct_filter, "metric_code"] = pd.NA
        data.loc[production_without_bbct_filter, "event_code"] = pd.NA

    def _get_event_mapper(
        self,
        event_definition: str,
        prod_line_status: int,
        events_hierarchy_mapper: dict[str, dict[str, Any]],
        property_name: str,
        variable_categories_mapping_service: VariableCategoriesMapping,
    ):
        has_variable_categories = events_hierarchy_mapper.get(
            event_definition
        ).get("variableCategories")

        return (
            events_hierarchy_mapper.get(event_definition, {}).get(
                property_name
            )
            if not (
                has_variable_categories
                and property_name not in ["eventDefinition"]
            )
            else variable_categories_mapping_service.map(
                prod_line_status, property_name
            )
        ) or pd.NA

    def _rename_categorization_columns(
        self,
        data: pd.DataFrame,
        events_hierarchy: list[EventHierarchyConfiguration],
        reporting_line: str,
        configurations: ReportingSiteConfiguration,
    ) -> pd.DataFrame:
        variable_categories_mapping = VariableCategoriesMappingFactory()
        variable_categories_mapping_service = (
            variable_categories_mapping.create(reporting_line=reporting_line)
        )

        events_hierarchy_mapper = {
            event_hierarchy.event_hierarchy: event_hierarchy.model_dump(
                by_alias=True
            )
            for event_hierarchy in events_hierarchy
            if event_hierarchy.event_hierarchy
        }

        event_renamed_columns = {
            "def": "eventDefinition",
            "subcat_level1": "subCategoryLevel1",
            "subcat_level2": "subCategoryLevel2",
            "event_code": "eventCode",
            "metric_code": "metricCode",
        }

        if configurations.external_id == "OEERSC-STS-FLO":
            cat_columns = [
                col
                for col in data.columns
                if "Shift" in col and "Comment" not in col
            ]
            data = data.apply(
                lambda row: variable_categories_mapping_service.classify_florence_event(
                    row, cat_columns, events_hierarchy_mapper
                ),
                axis=1,
            )

        elif (
            configurations.external_id == "OEERSC-STS-FRA"
            and variable_categories_mapping_service is not None
        ):
            data = data.apply(
                lambda row: variable_categories_mapping_service.classify_frankfurt_event(
                    row, events_hierarchy_mapper
                ),
                axis=1,
            )

        else:
            for target_column, origin_column in event_renamed_columns.items():
                data[target_column] = data.apply(
                    lambda x: self._get_event_mapper(
                        event_definition=x.event_definition,
                        prod_line_status=(
                            x.ProductionLineStatus
                            if "ProductionLineStatus" in x
                            else None
                        ),
                        events_hierarchy_mapper=events_hierarchy_mapper,
                        property_name=origin_column,
                        variable_categories_mapping_service=variable_categories_mapping_service,
                    ),
                    axis=1,
                )

        return data

    def _fix_product_data(
        self,
        product_data: pd.DataFrame,
        shifts: list[pd.Timestamp],
        reporting_line: str,
    ) -> pd.DataFrame:
        """
        fix product data by implementing second-later data to avoid missing information.
        creates index with frequency of 1 hour.

        :param product_data: product information relation data
        :type product_data: pd.DataFrame
        :param shifts: List of shifts to determine the filling behavior
        :type shifts: list[pd.Timestamp]
        :return: Fixed dataframe
        :rtype: pd.DataFrame
        """

        # main step - Create index with o'clock times with frequency of one hour
        # create indexes
        date_max = product_data.index.max()
        date_min = product_data.index.min()

        list_shifts = shifts.copy()
        flag_classifier = False

        for date_str in list_shifts:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            if date_obj.minute == 30:
                flag_classifier = True
                break

        if flag_classifier:
            indices = pd.date_range(
                product_data.index[0].floor("D"),
                product_data.index[-1].floor("D") + pd.DateOffset(days=1),
                freq="30min",
            )
        else:
            indices = pd.date_range(
                product_data.index[0].floor("D"),
                product_data.index[-1].floor("D") + pd.DateOffset(days=1),
                freq="1h",
            )

        # Reindex product data to include the new index
        product_data = product_data.reindex(
            pd.concat([product_data.index.to_series(), indices.to_series()])
        )

        product_data = product_data.loc[
            ~product_data.index.duplicated(keep="first")
        ]

        # also add a index at 23:59:59 every day
        end_of_day_indices = pd.date_range(
            product_data.index[0].floor("D")
            + pd.DateOffset(hours=23, minutes=59, seconds=59),
            product_data.index[-1].floor("D")
            + pd.DateOffset(hours=23, minutes=59, seconds=59),
            freq="1D",
        )

        product_data = product_data.reindex(
            pd.concat(
                [
                    product_data.index.to_series(),
                    end_of_day_indices.to_series(),
                ]
            )
        )

        # Sort the index to ensure proper ordering
        product_data.sort_index(inplace=True)

        # Filter the data to be within the min and max bounds
        product_data = product_data[
            (product_data.index >= date_min) & (product_data.index <= date_max)
        ]

        # Handle all columns except "NetProduction"
        columns_except_net = [
            col
            for col in product_data.columns
            if col not in ["NetProduction", "TotalFeed"]
        ]
        product_data[columns_except_net] = product_data[
            columns_except_net
        ].ffill()

        # Remove duplicates from the index to ensure a unique index before using combine_first
        product_data = product_data.loc[
            ~product_data.index.duplicated(keep="first")
        ]

        daily_net_production = [
            "RLN-BAYVAMVAM",
            "RLN-BISCHMMO3",
            "RLN-BISCHMMO4",
            "RLN-BISCHMPFM",
            "RLN-CLKAANAAN",
            "RLN-CLKAASAAS",
            "RLN-CLKMS1MS1",
            "RLN-CLKVAMVAM",
            "RLN-NANANHANH",
        ]

        # Custom fill for "NetProduction"
        if reporting_line not in daily_net_production and (
            "NetProduction" in product_data.columns
            or "TotalFeed" in product_data.columns
        ):
            col_name = (
                "NetProduction"
                if "NetProduction" in product_data.columns
                else "TotalFeed"
            )
            net_col = product_data[col_name].copy()

            days_with_net_data = net_col.resample("D").apply(
                lambda x: x.count()
            )
            valid_days = days_with_net_data[days_with_net_data > 0].index

            # if day n has values, forward fill within that day, if day n+1 also has values, continue forward fill until day n+1 first value. repeat
            for day in valid_days:
                fill_start = day
                next_day = day + pd.Timedelta(days=1)
                fill_end = (
                    next_day - pd.Timedelta(seconds=1)
                    if next_day not in valid_days
                    else next_day
                    + pd.Timedelta(days=1)
                    - pd.Timedelta(seconds=1)
                )

                mask = (product_data.index >= fill_start) & (
                    product_data.index <= fill_end
                )
                net_col.loc[mask] = net_col.loc[mask].ffill()

            product_data[col_name] = net_col

        # Restore the dtypes for all columns
        dtypes = product_data.dtypes
        for column in product_data.columns:
            product_data[column] = product_data[column].astype(dtypes[column])

        # Drop duplicates to remove work shift index duplication
        product_data = product_data.loc[
            ~product_data.index.duplicated(keep="first")
        ]

        # Reset the index and set it back
        product_data.reset_index(inplace=True)
        product_data.set_index("index", inplace=True)

        return product_data

    def _join_events_products(
        self,
        events_data: pd.DataFrame,
        product_data: pd.DataFrame,
        tag_configurations: List[InputTagConfiguration],
    ) -> pd.DataFrame:
        """
        joins the table that contains the events descriptions and product related information

        :param events_data: events frame dataframe
        :type events_data: pd.DataFrame
        :param product_data: product information relation data
        :type product_data: pd.DataFrame
        :return: joint dataframe
        :rtype: pd.DataFrame
        """

        # join both tables
        data = pd.merge(
            left=events_data,
            right=product_data,
            how="left",
            left_on="start_time",
            right_on="index",
            suffixes=("", "_product"),
        )

        if (
            EventFrameConstants.PRODUCT_DESCRIPTION in data.columns
            and "ProductDescription_product" in data.columns
        ):
            data[EventFrameConstants.PRODUCT_DESCRIPTION] = data[
                EventFrameConstants.PRODUCT_DESCRIPTION
            ].combine_first(data["ProductDescription_product"])
            data.drop(columns=["ProductDescription_product"], inplace=True)

        col_main_array = EventFixingService.COLUMN_PRODUCT

        for col_main in col_main_array:
            col_aux = col_main + "_product"
            if col_aux in data.columns:
                data[col_main] = data[col_main].fillna(data[col_aux])
                data.drop(columns=[col_aux], inplace=True)

        # fill Products based on ID
        for tag in tag_configurations:
            if not tag.event_identification:
                data[tag.alias] = data.groupby("EventID")[tag.alias].ffill()
                data.fillna({tag.alias: ""}, inplace=True)

        return data

    def _split_intervals(
        self, row: pd.DataFrame, event_definition: str, shifts: list[str]
    ) -> list:
        """
        splits the event frames into intervals based on the business rules defined. An event should end (and another should start) when:
        :param row: row containing the start time and the end times
        :type row: pd.DataFrame
        :param event_definition: definition of the event we are splitting
        :type event_definition: str
        :return: list of the events within the original events
        :rtype: list
        """

        # get variables to start split analysis
        intervals = []
        start = row[f"event{event_definition}_start"]
        end = row[f"event{event_definition}_end"]
        current_time = start

        # iterate through each of the timespans within the
        # original time frame

        shifts_times: list[pd.Timestamp] = [
            pd.Timestamp(shift) for shift in shifts
        ]

        while current_time < end:
            # build a candidate list to be the next time
            candidates = []

            for shift_time in shifts_times:
                entry = current_time.replace(
                    hour=shift_time.hour,
                    minute=shift_time.minute,
                    second=0,
                    microsecond=000000,
                )  # type: ignore
                if shift_time.hour == 0 and shift_time.minute == 0:
                    entry = entry + pd.DateOffset(days=1)
                candidates.append(entry)
            candidates.append(end)

            # find the closest timestamp and ensure it is newer than
            # the present timestamp
            next_time = min(candidates)
            is_next_time_lower = next_time <= current_time

            while is_next_time_lower:
                candidates.remove(next_time)
                next_time = min(candidates)
                is_next_time_lower = next_time <= current_time

            # append to the list a tuple with the timestamp of start, the
            # selected end and the status code. Update the current time
            to_append = [current_time, next_time]
            for col in row.index:
                if col not in [
                    f"event{event_definition}_start",
                    f"event{event_definition}_end",
                ]:
                    to_append.append(row[col])
            intervals.append(tuple(to_append))
            current_time = next_time
        return intervals

    def _identify_working_shifts_transitions(
        self, data: pd.DataFrame, event_definition: str, shifts: list[str]
    ) -> pd.DataFrame:
        """
        identifies working shifts transitions during events

        :param data: events data with start and end times
        :type data: pd.DataFrame
        :param event_definition: definition of the event we are splitting
        :type event_definition: str
        :return: event data with working shifts transitions
        :rtype: pd.DataFrame
        """

        # split intervals according to business rules
        new_rows = data.apply(
            self._split_intervals,
            event_definition=event_definition,
            shifts=shifts,
            axis=1,
        ).explode()

        # create data frame
        events_data = pd.DataFrame(new_rows.tolist(), columns=data.columns)

        return events_data

    def _build_timeseries_by_alias_dict(
        self,
        tag_configurations: List[InputTagConfiguration],
    ) -> Dict[str, Union[str, List[str]]]:
        """
        builds the dictionary of timeseries by alias

        :param tag_configurations: list of tag configurations
        :type data: List[InputTagConfiguration]
        :return: dictionary of timeseries by alias
        :rtype: Dict[str, Union[str, List[str]]]
        """

        timeseries_by_alias = {}
        for tag_configuration in tag_configurations:
            time_series = tag_configuration.time_series
            alias = tag_configuration.alias

            existing_alias = timeseries_by_alias.get(time_series)
            if existing_alias:
                if isinstance(existing_alias, list):
                    timeseries_by_alias[time_series].append(alias)
                else:
                    timeseries_by_alias[time_series] = [existing_alias, alias]
            else:
                timeseries_by_alias[time_series] = alias

        return timeseries_by_alias

    def _rename_from_external_id_to_alias(
        self,
        data: pd.DataFrame,
        rename_dict: Dict[str, Union[str, List[str]]],
    ) -> pd.DataFrame:
        """
        renames the dataframe columns from external ID to alias

        :param data: dataframe to rename
        :type data: pd.DataFrame
        :param rename_dict: dictionary of timeseries by alias
        :type rename_dict: Dict[str, Union[str, List[str]]]
        :return: data renamed
        :rtype: pd.DataFrame
        """
        columns_to_remove = []
        for external_id, alias in rename_dict.items():
            if isinstance(alias, list):
                columns_to_remove.append(external_id)
                for name in alias:
                    data[name] = data[external_id]
            else:
                data.rename(columns={external_id: alias}, inplace=True)

        data.drop(columns=columns_to_remove, inplace=True)
        return data

    def _add_alias_column(
        self,
        timeseries_configurations: pd.DataFrame,
        rename_dict: Dict[str, Union[str, List[str]]],
    ) -> pd.DataFrame:
        """
        Adiciona a coluna 'alias' ao DataFrame timeseries_configurations com base nos external IDs presentes em rename_dict.

        :param timeseries_configurations: DataFrame com uma coluna 'externalId'
        :param rename_dict: Dicionário contendo o mapeamento de externalId para alias
        :return: DataFrame com a nova coluna 'alias'
        """
        timeseries_configurations["alias"] = timeseries_configurations[
            "external_id"
        ].map(rename_dict)
        return timeseries_configurations

    def _fill_oee_product_data(
        self,
        event_frames_df: pd.DataFrame,
        product_names: list[str],
        reporting_site: ReportingSite,
    ):
        # Verifica se a coluna 'Product' existe no DataFrame
        if "Product" not in event_frames_df.columns:
            # Se 'ProductDescription' existir, cria a coluna 'Product'
            if "ProductDescription" in event_frames_df.columns:
                event_frames_df["Product"] = event_frames_df[
                    "ProductDescription"
                ]
            else:
                # Se 'ProductDescription' também não existir, retorna o DataFrame original
                return event_frames_df

        oee_product_df = self._product_repository.get_oee_product_df(
            product_names=product_names,
            reporting_site=reporting_site,
            create_missing=True,
            id_generator=self._id_generator,
        ).rename(
            columns={
                "externalId": "oeeProductExternalId",
                "space": "oeeProductSpace",
                "name": "oeeProductName",
            }
        )

        event_frames_df["Product"] = event_frames_df["Product"].astype(str)

        return pd.merge(
            event_frames_df,
            oee_product_df,
            left_on="Product",
            right_on="oeeProductName",
            how="left",
        )

    def _fill_material_data(
        self,
        event_frames_df: pd.DataFrame,
        mdr_data: pd.DataFrame,
        msdp_data: pd.DataFrame,
        bbct_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Fill material information into the event_frames_df.

        Steps:
            1. Ensure 'Product' and 'ProductDescription' columns exist.
            2. Try to find Material data based on PI tag values.
            3. Fill material data based on product name/description if necessary.
        """
        if (
            EventFrameConstants.PRODUCT in event_frames_df.columns
            or EventFrameConstants.PRODUCT_DESCRIPTION
            in event_frames_df.columns
        ):
            event_frames_df = self._ensure_oee_product_columns(event_frames_df)
            event_frames_df = self._fill_oee_material_by_pi_tag_value(
                event_frame=event_frames_df,
                mdr=mdr_data,
                msdp=msdp_data,
                bbct=bbct_data,
            )
            event_frames_df = self._fill_oee_material_by_name_description(
                event_frames_df=event_frames_df
            )

        return event_frames_df

    def _ensure_oee_product_columns(
        self, event_frames_df: pd.DataFrame
    ) -> pd.DataFrame:
        if EventFrameConstants.PRODUCT not in event_frames_df.columns:
            if (
                EventFrameConstants.PRODUCT_DESCRIPTION
                in event_frames_df.columns
            ):
                event_frames_df = event_frames_df.copy()
                event_frames_df[EventFrameConstants.PRODUCT] = event_frames_df[
                    EventFrameConstants.PRODUCT_DESCRIPTION
                ]
            else:
                return event_frames_df
        else:
            if (
                EventFrameConstants.PRODUCT_DESCRIPTION
                not in event_frames_df.columns
            ):
                event_frames_df = event_frames_df.copy()
                event_frames_df[EventFrameConstants.PRODUCT_DESCRIPTION] = (
                    event_frames_df[EventFrameConstants.PRODUCT]
                )
        return event_frames_df

    def _fill_oee_material_by_name_description(
        self, event_frames_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Fill material information into the event_frames_df.

        Steps:
            1. Retrieve product and material data.
            2. Merge dataframes based on product name.
            3. Combine material matches by alias, name, and description.
        """

        # Retrieve OEE material data
        oee_material_df = self._material_repository.get_oee_material_df(
            event_frames_df[event_frames_df["oeeMaterialExternalId"].isna()]
        ).rename(
            columns={
                "externalId": "oeeMaterialExternalId",
                "space": "oeeMaterialSpace",
                "name": "oeeMaterialName",
                "description": "oeeMaterialDescription",
                "alias": "oeeMaterialAlias",
            }
        )

        # Normalize types
        event_frames_df = event_frames_df.copy()
        event_frames_df["Product"] = event_frames_df["Product"].astype(str)

        # Merge with materials using priority fallback
        return self._merge_oee_materials(event_frames_df, oee_material_df)

    def _fill_oee_material_by_pi_tag_value(
        self,
        event_frame: pd.DataFrame,
        mdr: pd.DataFrame,
        msdp: pd.DataFrame,
        bbct: pd.DataFrame,
    ):
        # Normalize types
        event_frame = event_frame.copy()
        event_frame["Product"] = event_frame["Product"].astype(str)
        event_frame["ProductDescription"] = event_frame[
            "ProductDescription"
        ].astype(str)

        if mdr is None and msdp is None and bbct is None:
            event_frame["oeeMaterialExternalId"] = pd.NA
            event_frame["oeeMaterialSpace"] = pd.NA
            return event_frame

        pi_tag_values = pd.DataFrame()
        for df in [mdr, msdp, bbct]:
            if df is not None:
                pi_tag_values = pd.concat(
                    [pi_tag_values, df], ignore_index=True
                )

        pi_tag_values = pi_tag_values.dropna(
            subset=["piTagValue", "refMaterial", "reportingLineExternalId"]
        )
        pi_tag_values["date_set_datetime_utc"] = pd.to_datetime(
            pi_tag_values["date_set_datetime"], utc=True
        )
        pi_tag_values = pi_tag_values.sort_values(
            by="date_set_datetime_utc", ascending=False
        )
        pi_tag_values = pi_tag_values.drop_duplicates(
            subset=["piTagValue", "reportingLineExternalId"], keep="first"
        )
        pi_tag_values["oeeMaterialExternalId"] = pi_tag_values[
            "refMaterial"
        ].apply(lambda x: x.get("externalId") if isinstance(x, dict) else None)
        pi_tag_values["oeeMaterialSpace"] = pi_tag_values["refMaterial"].apply(
            lambda x: x.get("space") if isinstance(x, dict) else None
        )
        pi_tag_values = pi_tag_values[
            [
                "piTagValue",
                "reportingLineExternalId",
                "oeeMaterialExternalId",
                "oeeMaterialSpace",
            ]
        ]

        return event_frame.merge(
            pi_tag_values,
            left_on=["Product", "refReportingLineId"],
            right_on=["piTagValue", "reportingLineExternalId"],
            how="left",
        ).combine_first(
            event_frame.merge(
                pi_tag_values,
                left_on=["ProductDescription", "refReportingLineId"],
                right_on=["piTagValue", "reportingLineExternalId"],
                how="left",
            )
        )

    def _merge_oee_materials(
        self, base_df: pd.DataFrame, material_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Fill material info based on sequential matching:
        1. oeeProductName == oeeMaterialAlias
        2. oeeProductName == oeeMaterialName
        3. oeeProductDescription == oeeMaterialDescription
        """

        result = base_df.copy()

        missing_material_mask = (
            result["oeeMaterialExternalId"].isna()
            | result["oeeMaterialSpace"].isna()
        )

        if not missing_material_mask.any():
            return result  # No rows need processing

        # Try match by alias
        merged = result.merge(
            material_df,
            left_on="Product",
            right_on="oeeMaterialAlias",
            how="left",
            suffixes=("", "_alias"),
        )
        alias_matched = (
            merged["oeeMaterialExternalId"].isna()
            & merged["oeeMaterialExternalId_alias"].notna()
        )
        merged.loc[
            alias_matched, ["oeeMaterialExternalId", "oeeMaterialSpace"]
        ] = merged.loc[
            alias_matched,
            ["oeeMaterialExternalId_alias", "oeeMaterialSpace_alias"],
        ].values

        # For rows with no match yet, try by name
        no_match = merged["oeeMaterialExternalId"].isna()
        if no_match.any():
            merged_name = result[no_match].merge(
                material_df,
                left_on="Product",
                right_on="oeeMaterialName",
                how="left",
                suffixes=("", "_name"),
            )
            merged.loc[no_match, material_df.columns] = merged_name[
                material_df.columns
            ].values
            name_matched = (
                merged["oeeMaterialExternalId"].isna()
                & merged_name["oeeMaterialExternalId_name"].notna()
            )
            merged.loc[
                name_matched, ["oeeMaterialExternalId", "oeeMaterialSpace"]
            ] = merged_name.loc[
                name_matched,
                ["oeeMaterialExternalId_name", "oeeMaterialSpace_name"],
            ].values

        # For rows still null, try by description
        no_match = merged["oeeMaterialExternalId"].isna()
        if no_match.any():
            merged_desc = result[no_match].merge(
                material_df,
                left_on="ProductDescription",
                right_on="oeeMaterialDescription",
                how="left",
                suffixes=("", "_desc"),
            )
            merged.loc[no_match, material_df.columns] = merged_desc[
                material_df.columns
            ].values
            desc_matched = (
                merged["oeeMaterialExternalId"].isna()
                & merged_desc["oeeMaterialExternalId_desc"].notna()
            )
            merged.loc[
                desc_matched, ["oeeMaterialExternalId", "oeeMaterialSpace"]
            ] = merged_desc.loc[
                desc_matched,
                ["oeeMaterialExternalId_desc", "oeeMaterialSpace_desc"],
            ].values

        merged = merged.drop(
            [
                col
                for col in merged.columns
                if col.endswith(("_alias", "_name", "_desc"))
            ],
            axis=1,
        )

        return merged

    def _fill_oee_detail_data(
        self,
        event_frames_df: pd.DataFrame,
    ):
        unit_ids = event_frames_df["refUnitId"].astype(str).unique().tolist()

        unit_process_dataframes = (
            self._process_repository.get_units_oee_process_df(
                unit_ids=unit_ids
            )
        )

        categories_and_suffixes_and_source_cols = [
            ("Metric Code", "_mt", "metric_code"),
            ("Event Code", "_ev", "event_code"),
            ("Subcat Level 1", "_sc1", "subcat_level1"),
            ("Subcat Level 2", "_sc2", "subcat_level2"),
        ]

        updated_frames = []

        for unit_id, unit_df in unit_process_dataframes.items():
            unit_event_frames_df = event_frames_df[
                event_frames_df["refUnitId"] == unit_id
            ]
            hierarchy_df = pd.DataFrame()

            prev_suffix = None
            for idx, (category, suffix, source_col) in enumerate(
                categories_and_suffixes_and_source_cols
            ):
                cat_df = unit_df[
                    unit_df["OEECategory"] == category
                ].add_suffix(suffix)
                prev_suffix = (
                    None
                    if idx == 0
                    else categories_and_suffixes_and_source_cols[idx - 1][1]
                )

                hierarchy_df = (
                    cat_df
                    if idx == 0
                    else hierarchy_df.merge(
                        cat_df,
                        how="left",
                        left_on=[f"externalId{prev_suffix}"],
                        right_on=[f"parentExternalId{suffix}"],
                    )
                ).drop_duplicates()

                all_prev_external_id_cols = [
                    f"externalId{suffix}"
                    for _, suffix, _ in categories_and_suffixes_and_source_cols[
                        :idx
                    ]
                ]

                unit_event_frames_df = unit_event_frames_df.merge(
                    hierarchy_df,
                    how="left",
                    left_on=[
                        source_col,
                        *all_prev_external_id_cols,
                    ],
                    right_on=[
                        f"name{suffix}",
                        *all_prev_external_id_cols,
                    ],
                    suffixes=(None, "_to_exclude"),
                )

            updated_frames.append(unit_event_frames_df)

        updated_event_frames_df = pd.concat(updated_frames, ignore_index=True)

        updated_event_frames_df = updated_event_frames_df.drop(
            [
                col
                for col in updated_event_frames_df
                if col.endswith("_to_exclude")
            ],
            axis=1,
        )

        event_frames_df = event_frames_df.merge(
            updated_event_frames_df,
            how="left",
            on=list(
                event_frames_df.columns.intersection(
                    updated_event_frames_df.columns
                )
            ),
        )

        loss_categories = (
            self._loss_category_repository.get_active_loss_categories()
        )

        event_frames_df["loss_category_ext_id"] = None
        event_frames_df["loss_category_space"] = None

        for key, value in METRIC_CODE_TO_LOSS_CATEGORY.items():
            category = [c for c in loss_categories if c.name == value]
            if len(category) > 0:
                event_frames_df.loc[
                    event_frames_df["metric_code"] == key,
                    "loss_category_ext_id",
                ] = category[0].external_id
                event_frames_df.loc[
                    event_frames_df["metric_code"] == key,
                    "loss_category_space",
                ] = category[0].space

        return event_frames_df

    @staticmethod
    def _fill_product_names_to_search(
        data: pd.DataFrame, product_names_to_search: set[str]
    ):
        # Verifica se a coluna 'Product' existe no DataFrame
        if "Product" not in data.columns:
            if "ProductDescription" in data.columns:
                data["Product"] = data["ProductDescription"]
            else:
                return
        product_names_to_search.update(
            data["Product"].astype(str).replace("", pd.NA).dropna().unique()
        )

    @staticmethod
    def _fill_process_names_to_search(
        data: pd.DataFrame, process_names_to_search: set[str]
    ):
        for col in [
            "metric_code",
            "event_code",
            "subcat_level1",
            "subcat_level2",
        ]:
            process_names_to_search.update(data[col].unique())

    def add_transition_data(
        self,
        data: pd.DataFrame,
        product_transition: dict[str, dict[str, int]],
    ) -> None:
        self.add_is_transition(data)
        self.add_extra_duration(data)
        self.add_from_and_to_products(data)
        self.add_expected_duration(data, product_transition)

    @staticmethod
    def add_is_transition(data: pd.DataFrame) -> None:
        transition_def = data["def"] == EventDefinitionEnum.NOT_RUNNING.value
        transition_sub_cat_one = (
            data["subcat_level1"] == SubCategoryLevelOneEnum.TRANSITION.value
        )
        transition_event_code = (
            data["event_code"]
            == EventCodeEnum.PRODUCT_AND_SUPPLY_OPTIMIZATION.value
        )
        transition_metric_code = (
            data["metric_code"] == MetricCodeEnum.AVAILABILITY.value
        )

        data["IsTransition"] = (
            transition_def
            & transition_sub_cat_one
            & transition_event_code
            & transition_metric_code
        )

    @staticmethod
    def add_extra_duration(data: pd.DataFrame) -> None:
        data.sort_values(by="start_time", ascending=True, inplace=True)
        data["ExtraDuration"] = 0.0
        last_position = len(data) - 1

        for index in data.index[data["IsTransition"]]:
            extra_duration = 0.0
            position = data.index.get_loc(index) + 1

            if position <= last_position:
                row = data.iloc[position]
                is_not_running = (
                    row["def"] == EventDefinitionEnum.NOT_RUNNING.value
                )

                while is_not_running and position < last_position:
                    total_duration_seconds = row["total_duration_seconds"]
                    extra_duration += total_duration_seconds
                    position += 1
                    row = data.iloc[position]
                    is_not_running = (
                        row["def"] == EventDefinitionEnum.NOT_RUNNING.value
                    )

            data.at[index, "ExtraDuration"] = extra_duration

    @staticmethod
    def add_from_and_to_products(data: pd.DataFrame) -> None:
        data.sort_values(by="start_time", ascending=True, inplace=True)
        data["FromProduct"] = pd.NA
        data["FromProductExtId"] = pd.NA
        data["FromProductSpace"] = pd.NA
        data["ToProduct"] = pd.NA
        data["ToProductExtId"] = pd.NA
        data["ToProductSpace"] = pd.NA

        for index in data.index[data["IsTransition"]]:
            to_product = data.loc[index, "ProductDescription"]
            to_product_ext_id = data.loc[index, "oeeMaterialExternalId"]
            to_product_space = data.loc[index, "oeeMaterialSpace"]
            position = data.index.get_loc(index) - 1
            row = data.iloc[position]
            from_product = row["ProductDescription"]
            from_product_ext_id = row["oeeMaterialExternalId"]
            from_product_space = row["oeeMaterialSpace"]
            is_equal = from_product == to_product

            while is_equal:
                position = position - 1
                row = data.iloc[position]
                from_product = row["ProductDescription"]
                from_product_ext_id = row["oeeMaterialExternalId"]
                from_product_space = row["oeeMaterialSpace"]
                is_equal = from_product == to_product

            data.at[index, "FromProduct"] = from_product
            data.at[index, "FromProductExtId"] = from_product_ext_id
            data.at[index, "FromProductSpace"] = from_product_space
            data.at[index, "ToProduct"] = to_product
            data.at[index, "ToProductExtId"] = to_product_ext_id
            data.at[index, "ToProductSpace"] = to_product_space

    @staticmethod
    def add_expected_duration(
        data: pd.DataFrame, product_transition: dict[str, dict[str, int]]
    ) -> None:
        data["ExpectedDuration"] = data.apply(
            lambda row: (
                product_transition.get(row["FromProduct"], {}).get(
                    row["ToProduct"], np.nan
                )
            ),
            axis=1,
        )
