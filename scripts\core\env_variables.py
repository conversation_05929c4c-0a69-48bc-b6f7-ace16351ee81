import os
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from core.models import CogniteProjects

from dotenv import load_dotenv

python_env = os.getenv("PYTHON_ENV") or "dev"


def load_variables():
    try:
        load_dotenv(override=True)
        load_dotenv(f".env.{python_env}", override=True)
    except Exception:
        print(f"Could not load variables for env {python_env}")


class AuthVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=(".env", ".env." + python_env), env_prefix="auth_", extra="ignore"
    )

    client_id: str = Field(alias="auth_client_id")
    tenant_id: str
    secret: str = Field(alias="auth_secret")
    scopes_str: str = Field(alias="auth_scopes")
    token_uri: str
    cicd_client_id: Optional[str]
    cicd_secret: Optional[str]
    token_override: Optional[str] = Field(None)

    @property
    def scopes(self) -> List[str]:
        return self.scopes_str.split(" ")


class CogniteVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=(".env", ".env." + python_env), env_prefix="cognite_", extra="ignore"
    )
    base_uri: str
    client_name: str
    data_set_id: int
    project: CogniteProjects
    data_model_external_id: str
    data_model_space: str
    data_model_version: str
    default_data_model_instances_space: str


class EnvVariables:
    """
    class to store all Environment variables
    """

    def __init__(self) -> None:
        load_variables()
        self.auth = AuthVariables()
        self.cognite = CogniteVariables()
