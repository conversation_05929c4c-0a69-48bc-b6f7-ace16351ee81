from typing import Any, Optional

from pydantic import Field

from .material import Material
from .node import Node
from .reporting_line import ReportingLine


class LeadProductBbct(Node):
    product: Optional[str] = Field(default=None, alias="product")
    pi_tag_value: Optional[str] = Field(default=None, alias="pITagValue")
    best_batch_cycle_time_mt: Optional[float] = Field(default=None, alias="bestBatchCycleTimeMT")
    best_batch_cycle_time_hr: Optional[float] = Field(default=None, alias="bestBatchCycleTimeHr")

class LeadProductMdr(Node):
    product: Optional[str] = Field(default=None, alias="product")
    pi_tag_value: Optional[str] = Field(default=None, alias="piTagValue")


class LeadProductMsdp(Node):
    product_group: Optional[str] = Field(default=None, alias="productGroup")
    pi_tag_value: Optional[str] = Field(default=None, alias="piTagValue")


class LeadProduct(Node):
    reporting_line: ReportingLine = Field(alias="refReportingLine")
    material: Optional[Material] = Field(default=None, alias="refMaterial")
    bbct: Optional[LeadProductBbct] = Field(default=None, alias="refOEEBBCT")
    mdr: Optional[LeadProductMdr] = Field(default=None, alias="refOEEMDR")
    msdp: Optional[LeadProductMsdp] = Field(default=None, alias="refOEEMSDP")

    @classmethod
    def from_cognite_response(cls, item: dict[str, Any]) -> "LeadProduct":
        return LeadProduct(
            externalId=item["externalId"],
            space=item["space"],
            refReportingLine=ReportingLine(**item["refReportingLine"]),
            refMaterial=Material(**item["refMaterial"])
            if item.get("refMaterial")
            else None,
            refOEEBBCT=LeadProductBbct(**item["refOEEBBCT"])
            if item.get("refOEEBBCT")
            else None,
            refOEEMDR=LeadProductMdr(**item["refOEEMDR"])
            if item.get("refOEEMDR")
            else None,
            refOEEMSDP=LeadProductMsdp(**item["refOEEMSDP"])
            if item.get("refOEEMSDP")
            else None,
        )

