from typing import Optional

from pydantic import Field

from .business_segment import BusinessSegment
from .node import Node
from .reporting_unit import ReportingUnit


class ProcessType(Node):
    name: Optional[str] = Field(None)


class legacyBusinessLine(Node):
    name: Optional[str] = Field(None)
    maps_to: Optional[BusinessSegment] = Field(None, alias="mapsTo")


class ReportingLine(Node):
    name: Optional[str] = Field(None)

    reporting_unit: Optional[ReportingUnit] = Field(
        default=None, alias="reportingUnit"
    )

    process_type: Optional[ProcessType] = Field(
        default=None, alias="processType"
    )

    legacy_business_line: Optional[legacyBusinessLine] = Field(
        default=None, alias="legacyBusinessLine"
    )
