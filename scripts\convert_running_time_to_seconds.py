import os
import sys

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from zoneinfo import ZoneInfo
from core.env_variables import EnvVariables
from core.cognite_client_factory import CogniteClientFactory
from pydantic import BaseModel
from datetime import datetime
from typing import Any
from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    InstancesApplyResult,
)


class EventFilter(BaseModel):
    external_ids: list[str] | None
    reporting_site_ids: list[str] | None
    reporting_line_ids: list[str] | None
    start_date: datetime
    end_date: datetime


class OEEEvent(BaseModel):
    external_id: str
    space: str
    total_running_time: float | None


class ConvertRunningTimeToSeconds:
    """Script responsible to convert the events running time from hour to seconds."""

    def __init__(self):
        self.__env_variables = EnvVariables()
        self.__cognite_client = CogniteClientFactory.create(self.__env_variables)
        self.__data_model = self.__cognite_client.data_modeling.data_models.retrieve(
            ids=(
                self.__env_variables.cognite.data_model_space,
                "OEESOL",
                self.__env_variables.cognite.data_model_version,
            )
        ).latest_version()

    def __build_filter(self, event_filter: EventFilter) -> list[dict]:
        query_filter = [
            {"totalRunningTime": {"isNull": False}},
            {"totalRunningTime": {"lte": 24}},
        ]

        if event_filter.external_ids:
            query_filter.append({"externalId": {"in": event_filter.external_ids}})

        if event_filter.reporting_site_ids:
            query_filter.append(
                {"refSite": {"externalId": {"in": event_filter.reporting_site_ids}}}
            )

        if event_filter.reporting_line_ids:
            query_filter.append(
                {
                    "refReportingLine": {
                        "externalId": {"in": event_filter.reporting_line_ids}
                    }
                }
            )

        query_filter.append(
            {"startDateTime": {"gte": event_filter.start_date.isoformat()}}
        )

        query_filter.append({"endDateTime": {"lte": event_filter.end_date.isoformat()}})

        return query_filter

    def __get_query(self) -> str:
        return """
            query listOEEEvent($first: Int, $after: String, $filter: _ListOEEEventFilter ) {
                listOEEEvent(first: $first, after: $after, filter: $filter) {
                    items {
                        externalId
                        space
                        totalRunningTime
                    }
                    pageInfo {
                        hasNextPage
                        endCursor
                    }
                }
            }
        """

    def __get_events(self, event_filter: EventFilter) -> list[OEEEvent]:
        events = []
        query_filter = self.__build_filter(event_filter)
        query = self.__get_query()
        has_next_page = True
        after = None

        while has_next_page:
            result = self.__cognite_client.data_modeling.graphql.query(
                self.__data_model.as_id(),
                query,
                variables={
                    "first": 1000,
                    "after": after,
                    "filter": {"and": query_filter},
                },
            )

            items: list[dict[str, Any]] = result["listOEEEvent"]["items"]
            page_info: dict[str, str | bool] = result["listOEEEvent"]["pageInfo"]

            for item in items:
                external_id = item.get("externalId", None)
                space = item.get("space", None)
                if external_id and space:
                    events.append(
                        OEEEvent(
                            external_id=external_id,
                            space=space,
                            total_running_time=item.get("totalRunningTime", None),
                        )
                    )

            has_next_page = page_info["hasNextPage"]
            after = page_info["endCursor"]

        return events

    def __convert_running_time_to_seconds(
        self, events: list[OEEEvent]
    ) -> list[OEEEvent]:
        updated_events: list[OEEEvent] = []

        for event in events:
            if event.total_running_time and event.total_running_time <= 24:
                updated_events.append(
                    OEEEvent(
                        space=event.space,
                        external_id=event.external_id,
                        total_running_time=event.total_running_time * 3600,
                    )
                )

        return updated_events

    def __convert_to_instance_nodes(
        self, updated_events: list[OEEEvent]
    ) -> list[NodeApply]:
        view_list = self.__cognite_client.data_modeling.views.list(
            space=self.__env_variables.cognite.data_model_space,
            all_versions=False,
            limit=-1,
        )
        view = [view for view in view_list if view.external_id == "OEEEvent"]
        view_id = view[0].as_id()

        return [
            NodeApply(
                space=event.space,
                external_id=event.external_id,
                sources=[
                    NodeOrEdgeData(
                        view_id, {"totalRunningTime": event.total_running_time}
                    )
                ],
            )
            for event in updated_events
        ]

    def __update_events(self, instance_nodes: list[NodeApply]) -> InstancesApplyResult:
        return self.__cognite_client.data_modeling.instances.apply(instance_nodes)

    def run(self, event_filter: EventFilter) -> None:
        print(
            f"Running running time convertion to seconds in {self.__cognite_client.config.project}."
        )
        events = self.__get_events(event_filter)
        updated_events = self.__convert_running_time_to_seconds(events)
        instance_nodes = self.__convert_to_instance_nodes(updated_events)
        update_result = self.__update_events(instance_nodes)
        print(f"{len(update_result.nodes)} event(s) updated.")


if __name__ == "__main__":
    tzinfo = ZoneInfo(key="America/Chicago")
    convert = ConvertRunningTimeToSeconds()
    event_filter = EventFilter(
        external_ids=None,
        reporting_site_ids=None,
        reporting_line_ids=None,
        start_date=datetime(2025, 1, 1, 0, 0, 0, 0, tzinfo=tzinfo),
        end_date=datetime(2025, 10, 30, 0, 0, 0, 0, tzinfo=tzinfo),
    )
    convert.run(event_filter)
