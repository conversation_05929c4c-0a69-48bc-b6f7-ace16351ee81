from datetime import time
from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value


class CLKVAMEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._day_data = None

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus < 2000 for 5 minutes
        data["event1a_start"] = self.__maintains_transition(data)

        # event trigger end - ProductionLineStatus > 2000
        data = data.assign(event1a_end=(data["ProductionLineStatus"] > 2000))

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        ).assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type II included
        :rtype: pd.DataFrame
        """

        data["under_2000_five_minutes"] = self.__maintains_transition(data)

        data["dt"] = data["dt"] / 60

        # Counts the duration of the ProductionLineStatus < 2000
        data["ProductionLineStatus_duration"] = data.groupby(
            (
                (data["ProductionLineStatus"].shift(1) >= 2000)
                & (data["ProductionLineStatus"] < 2000)
            ).cumsum()
        )["dt"].cumsum()
        data.loc[data["ProductionLineStatus"] >= 2000, "ProductionLineStatus_duration"] = 0

        # event trigger start - ProductionLineStatus stays < 2000 for at least 5 minutes and transits to > 2000
        # and TanklevelControl <= 0
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus_duration"].shift(1) >= 5)
                & (data["ProductionLineStatus"] > 2000)
                & (data["TankLevelControl"] <= 0)
            )
        )

        # event trigger end - ProductionLineStatus > 2000 and TanklevelControl > 0
        # or ProductionLineStatus < 2000 for 5 minutes
        data = data.assign(
            event2a_end=(
                (
                    (data["ProductionLineStatus"] > 2000)
                    & (data["TankLevelControl"] > 0)
                )
                | (
                    data["under_2000_five_minutes"] == True # Not Running starts
                )
            )
        )

        # correct start and end flags
        data = data.assign(
            event2a_start=(
                (data["event2a_start"] == True)
                & (data["event2a_start"].shift(1) != True)
            )
        ).assign(
            event2a_end=(
                (data["event2a_end"] == True)
                & (data["event2a_end"].shift(1) != True)
            )
        )

        return data
    
    def identify_events_typeIIb(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data=data, col="NetProduction")

        data["RLT"] = ((data["MSDP"] * data["running_time"] / 24) - data["NetProduction"]) / (
            data["MSDP"] / 24
        )

        data = data.assign(
            event3a_end=(
                (data["RLT"] > 0)
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["running_time"] > 0.25)
                & (data["is_noon"])

            )
        )

        data = data.assign(
            event3a_start=(data["event3a_end"] == True)
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data=data, col="NetProduction")

        data["RLT"] = ((data["MSDP"] * data["running_time"] / 24) - data["NetProduction"]) / (
            data["MSDP"] / 24
        )

        data = data.assign(
            event3b_end=(
                (data["RLT"] < 0)
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["running_time"] > 0.25)
                & (data["is_noon"])
            )
        )

        data = data.assign(
            event3b_start=(data["event3b_end"] == True)
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIIc(self):
        pass

    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IVa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """

        data["under_2000_five_minutes"] = self.__maintains_transition(data)

        # event trigger start - ProductionLineStatus > 2000 and TanklevelControl goes from > 0 to <= 0     
        data = data.assign(
            event4a_start=(
                (data["ProductionLineStatus"] > 2000)
                & (data["TankLevelControl"].shift(1) > 0)
                & (data["TankLevelControl"] <= 0)
            )
        )

        # event trigger end - ProductionLineStatus > 2000 and TanklevelControl > 0
        # or ProductionLineStatus < 2000 for 5 minutes
        data = data.assign(
            event4a_end=(
                (
                    (data["ProductionLineStatus"] > 2000)
                    & (data["TankLevelControl"] > 0)
                )
                | (
                    data["under_2000_five_minutes"] == True # Not Running starts
                )
            )
        )

        # correct start and end flags
        data = data.assign(
            event4a_start=(
                (data["event4a_start"] == True)
                & (data["event4a_start"].shift(1) != True)
            )
        ).assign(
            event4a_end=(
                (data["event4a_end"] == True)
                & (data["event4a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIVb(self):
        pass

    def __create_day_data(self, data: pd.DataFrame, col: str) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        if self._day_data is not None:
            return self._day_data.copy()
        
        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None
            if first_timestamp.time() == mid_night_time
            else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            (
                (start_cutoff is not None)
                & (data["index"].dt.date > start_cutoff)
            )
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        if data.empty:
            return data

        data.set_index("index", inplace=True)

        LBS_TO_MT_DIVISION_FACTOR = 2204.62

        data[col] = data[col] / LBS_TO_MT_DIVISION_FACTOR

        data.reset_index(inplace=True)

        running_time_key = "running_time"

        # Modified running_time calculation
        running_condition = ((data["ProductionLineStatus"] > 2000) & (data["TankLevelControl"] > 0))

        data["condition_running_time"] = running_condition

        data["duration_in_seconds_without_conditions"] = (data["index"].diff().dt.total_seconds().fillna(0))

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (running_condition)
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "ProductDescription"],
            dropna=False,
        ).agg(
            {
                col: "first",
                "duration_in_seconds": "sum",
                "ProductionLineStatus": "mean",
            }
        )

         # reset multiindex, timestamp and product become columns
        data = data.reset_index().rename(columns={"index": "timestamp"})
        
        data[running_time_key] = (
            data.groupby(pd.Grouper(key="timestamp", freq="1h"))["duration_in_seconds"].transform("sum") / 3600
        )
        
        data.drop(columns=["duration_in_seconds"], inplace=True)

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        data[["MSDP", "SCHR"]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=["msdp", "scheduledRate"],
            axis=1,
            result_type="expand"
        )

        data = (
            data.groupby(
                [pd.Grouper(key="timestamp", freq="D"), data["ProductDescription"]],
            )
            .agg(
                {
                    "MSDP": "first",
                    "SCHR": "first",
                    col: "first",
                    "running_time": "sum",
                },
            )
            .reset_index()
        )

        data["is_noon"] = (
            (data["timestamp"].dt.hour == 0)
            & (data["timestamp"].dt.minute == 0)
            & (data["timestamp"].dt.second == 0)
            & (data["timestamp"].dt.microsecond == 0)
        )
        
        if col == "NetProduction":
            data[col] = data[col].shift(-1)

        data = data.rename(columns={"timestamp": "index"}).drop(
            columns=["ProductDescription"]
        )

        self._day_data = data

        return data

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        return msdp_util_get_msdp_value(row, msdp_data, data_aux)

    def __maintains_transition(self, df: pd.DataFrame) -> pd.Series:
        """
        mantains the transition of the events

        :param df: DataFrame containing the ProductionLineStatus
        :type df: pd.DataFrame
        :return: Series indicating if the ProductionLineStatus was under 2000 for 5 minutes
        :rtype: pd.Series
        """

        df["under_2000_five_minutes"] = False

        df["transition"] = (
            (df["ProductionLineStatus"].shift(1) >= 2000)
            & (df["ProductionLineStatus"] < 2000)
        )
        
        # Set the transition datapoint as start of the event if the next 5 minutes are all below 2000
        df.set_index("index", inplace=True)
        for timestamp in df.index[df["transition"]]:
            transition_end = timestamp + pd.Timedelta(minutes=5)

            mask = (df.index >= timestamp) & (df.index <= transition_end)

            if not mask.any():
                continue

            if(df.loc[mask, "ProductionLineStatus"] < 2000).all():
                df.at[timestamp, "under_2000_five_minutes"] = True

        df.reset_index(inplace=True)

        return df["under_2000_five_minutes"]