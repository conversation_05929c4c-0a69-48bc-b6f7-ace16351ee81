from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import DataModelId, ViewId


class ViewRepository:
    def __init__(
        self, cognite_client: CogniteClient, data_model_id: DataModelId
    ) -> None:
        self._cognite_client = cognite_client
        self._data_model_id = data_model_id
        self._cache: dict[str, ViewId] = {}

    def get_view_id(self, view_external_id: str) -> ViewId:
        if self._cache:
            return self._cache[view_external_id]

        data_models = self._cognite_client.data_modeling.data_models.retrieve(
            self._data_model_id
        )

        if len(data_models) != 1:
            raise ValueError(
                f"Could not find data model {self._data_model_id.external_id}"
            )

        views = data_models[0].views

        self._cache = {view.external_id: view for view in views}

        return self._cache[view_external_id]
