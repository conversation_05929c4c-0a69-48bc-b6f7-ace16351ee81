import math
from typing import Any, Optional

import pandas as pd
from pydantic import Field

from .node import Node

ENTITY_CODE = "OEEHD"


class HourlyData(Node):
    ref_region: str = Field(alias="refRegion")
    ref_country: str = Field(alias="refCountry")
    ref_site: str = Field(alias="refSite")
    ref_unit: str = Field(alias="refUnit")
    ref_oee_product: Optional[str] = Field("", alias="refOEEProduct")
    ref_product: Optional[str] = Field("", alias="Product")
    batch: Optional[str] = Field("", alias="Batch")
    ref_business_segment: Optional[str] = Field("", alias="refBusinessSegment")
    ref_reporting_line: str = Field(alias="refReportingLine")
    start_date_time: str = Field(alias="startDateTime")
    end_date_time: str = Field(alias="endDateTime")
    loading_time_seconds: Optional[float] = Field(
        None, alias="loadingTimeSeconds"
    )
    operating_time_seconds: Optional[float] = Field(
        None, alias="operatingTimeSeconds"
    )
    minor_stops_seconds: Optional[float] = Field(
        None, alias="minorStopsSeconds"
    )
    production: float = Field(alias="production")
    hourly_rate: float = Field(alias="hourlyRatePerH")
    unit_of_measurement: str = Field(alias="refUnitOfMeasurement")
    running_time_seconds: float = Field(alias="runningTimeSeconds")
    rate_loss_time_h: Optional[float] = Field(None, alias="rateLossTimeH")
    rate_loss_time_h_no_demand: Optional[float] = Field(
        None, alias="rateLossTimeHNoDemand"
    )
    ref_process_type: Optional[str] = Field("", alias="refProcessType")
    line_type: Optional[str] = Field("", alias="lineType")

    @staticmethod
    def generate_external_id_prefix(
        reporting_line_name: str,
    ) -> str:
        return (f"{ENTITY_CODE}-{reporting_line_name}").replace(" ", "")

    @classmethod
    def from_event_row(cls, row: dict[str, Any], space: str) -> "HourlyData":
        external_id_prefix = HourlyData.generate_external_id_prefix(
            str(row["refReportingLine"])
        )
        external_id = (
            external_id_prefix + "-" + str(row["start_time"])
        ).replace(" ", "")

        HourlyData.generate_external_id_prefix(str(row["refReportingLine"]))

        if not row.get("BatchID"):
            batch_id = ""
        else:
            batch_id = (
                ""
                if isinstance(row["BatchID"], float)
                and math.isnan(row["BatchID"])
                else row["BatchID"]
            )

        return cls(
            externalId=external_id,
            space=space,
            refRegion=row["refRegion"],
            refCountry=row["refCountry"],
            refSite=row["refSite"],
            refUnit=row["refUnit"],
            refOEEProduct=row["ProductDescription"]
            if row.get("ProductDescription")
            else "",
            Product=row["Product"] if row.get("Product") else "",
            Batch=batch_id,
            refBusinessSegment=row["refBusinessSegment"],
            refReportingLine=row["refReportingLine"],
            startDateTime=pd.to_datetime(row["start_time"])
            .replace(microsecond=0)
            .isoformat(),
            endDateTime=pd.to_datetime(row["end_time"])
            .replace(microsecond=0)
            .isoformat(),
            loadingTimeSeconds=row["loading_time_seconds"]
            if row.get("loading_time_seconds")
            else None,
            operatingTimeSeconds=row["operating_time_seconds"]
            if row.get("operating_time_seconds")
            else None,
            minorStopsSeconds=row["minor_stops_seconds"]
            if row.get("minor_stops_seconds")
            else None,
            production=row["production"],
            hourlyRatePerH=row["hourly_rate"],
            refUnitOfMeasurement=row["measurement_unit"],
            runningTimeSeconds=row["running_duration"],
            rateLossTimeH=row["rate_loss_time_h"]
            if row.get("rate_loss_time_h")
            else None,
            rateLossTimeHNoDemand=row["rate_loss_time_h_No_Demand"]
            if row.get("rate_loss_time_h_No_Demand")
            else None,
            refProcessType=row["refProcessType"],
            lineType=row["lineType"],
        )
