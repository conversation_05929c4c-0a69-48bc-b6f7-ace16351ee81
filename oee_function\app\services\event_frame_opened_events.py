import numpy as np
import pandas as pd


def adjust_group(group):
    true_events = group[group['isOpened'] == True]
    
    if not true_events.empty:
        
        last_true_idx = true_events.index[-1]
        
        group.loc[:, 'isOpened'] = False
        
        group.loc[last_true_idx, 'isOpened'] = True
        
    return group

def fix_opened_events(data):
    # Fixing opened events
    data['isOpened'] = data['isOpened'].astype(str)
    data['isOpened'] = data['isOpened'].apply(lambda x: x.lower() == 'true' if x.lower() in ['true', 'false'] else False)

    # Minimal duration for opened events
    minimal_duration_for_opened_events_minutes = 15
    minimal_duration_seconds = minimal_duration_for_opened_events_minutes * 60

    # Filtering opened events with duration less than minimal_duration_seconds
    opened_events_filter = (data['isOpened']) & (data['total_duration_seconds'] <= minimal_duration_seconds)

    data = data[~opened_events_filter]
    
    # Groups events by 'event_definition'
    # For each group, only the last record will have isOpened as True, the others will be False.
    data = data.groupby('event_definition', group_keys=False).apply(adjust_group)

    return data
