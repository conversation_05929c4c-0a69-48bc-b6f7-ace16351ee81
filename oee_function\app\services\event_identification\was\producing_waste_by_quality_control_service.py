from typing import Optional

import pandas as pd
from app.models.lead_product import LeadProduct
from app.services.event_identification.was.washington_works_event_identification_service import (
    WashingtonWorksEventIdentificationService,
)
from app.utils.event_detection_utils import keep_only_first_occurrence


class ProducingWasteByQualityControlService:
    """
    EN:
    Service to identify Producing Waste by Quality Control events **per day**
    based on cumulative counter tags (e.g., DivertTotalizer).

    PT-BR:
    Serviço para identificar eventos de Producing Waste by Quality Control **por dia**
    baseado em tags de contadores cumulativos (ex: DivertTotalizer).

    Main rules / Regras principais:

    EN:
    - Receives a DataFrame of time series and a list of counter tags.
    - For each day, calculates how much each tag **effectively increased** that day,
      considering resets (when the counter returns to zero).
    - Sums the increases from all tags to obtain a total daily value.
    - Creates a daily event (from 00:00 until 00:00 of the next day) whenever
      the total daily value is greater than zero.

    PT-BR:
    - Recebe um DataFrame de séries temporais e uma lista de tags de contadores.
    - Para cada dia, calcula quanto cada tag **aumentou efetivamente** naquele dia,
      considerando resets (quando o contador volta a zero).
    - Soma os aumentos de todas as tags para obter um valor diário total.
    - Cria um evento diário (de 00:00 até 00:00 do dia seguinte) sempre que o
      valor diário total for maior que zero.
    """

    @staticmethod
    def process(
        data: pd.DataFrame,
        producing_waste_tags: list[str],
        mdr: pd.DataFrame,
        lead_product: Optional[LeadProduct],
        event_number_type: str = "4c",
    ) -> pd.DataFrame:
        """
        EN:
        Identifies daily Producing Waste by Quality Control events.

        PT-BR:
        Identifica eventos diários de Producing Waste by Quality Control.

        Logic per tag (example with a `DivertTotalizer` tag):
        Lógica por tag (exemplo com uma tag `DivertTotalizer`):

        EN:
        - Considers the cumulative series of the tag over time.
        - Calculates, for each sample, the positive increment relative to the previous value.
        - When there is a reset (current value < previous value), the increment at that point is 0;
          new increments after the reset are counted normally.
        - The sum of increments within a day represents the "scrap" of that day.
        - For multiple tags, the daily scraps of each tag are summed.

        PT-BR:
        - Considera a série cumulativa da tag ao longo do tempo.
        - Calcula, para cada amostra, o incremento positivo em relação ao valor anterior.
        - Quando há reset (valor atual < valor anterior), o incremento daquele ponto é 0;
          os novos incrementos após o reset são contabilizados normalmente.
        - A soma dos incrementos dentro de um dia representa o "scrap" daquele dia.
        - Para várias tags, os scraps diários de cada tag são somados.

        Simplified example (one tag, daily maximum values):
        Exemplo simplificado (uma tag, valores diários máximos):

        EN:
        - Day 02/10: 0
        - Day 03/10: max 105   -> scrap day 03/10 = 105
        - Day 04/10: max 112   -> scrap day 04/10 = 7   (112 - 105)
        - Day 05/10: max 153   -> scrap day 05/10 = 41  (153 - 112)
        - After 06/10 without increases -> daily scrap = 0 -> no events

        PT-BR:
        - Dia 02/10: 0
        - Dia 03/10: max 105   -> scrap dia 03/10 = 105
        - Dia 04/10: max 112   -> scrap dia 04/10 = 7   (112 - 105)
        - Dia 05/10: max 153   -> scrap dia 05/10 = 41  (153 - 112)
        - Após 06/10 sem aumentos -> scrap diário = 0 -> sem eventos

        Events created:
        Eventos criados:

        EN:
        - 03/10 00:00 until 04/10 00:00 (scrap 105)
        - 04/10 00:00 until 05/10 00:00 (scrap 7)
        - 05/10 00:00 until 06/10 00:00 (scrap 41)

        PT-BR:
        - 03/10 00:00 até 04/10 00:00 (scrap 105)
        - 04/10 00:00 até 05/10 00:00 (scrap 7)
        - 05/10 00:00 até 06/10 00:00 (scrap 41)

        :param data: EN: Time series with "index" column (timestamp) and tag columns
                     PT-BR: Série temporal com coluna "index" (timestamp) e colunas de tags
        :type data: pd.DataFrame
        :param producing_waste_tags: EN: List of tag names to monitor
                                    PT-BR: Lista de nomes de tags a monitorar
        :type producing_waste_tags: list[str]
        :param mdr: EN: DataFrame with MDRs (kept for compatibility / future use)
                    PT-BR: DataFrame com MDRs (mantido para compatibilidade / uso futuro)
        :type mdr: pd.DataFrame
        :param lead_product: EN: Lead product (kept for compatibility / future use)
                            PT-BR: Lead product (mantido para compatibilidade / uso futuro)
        :type lead_product: Optional[LeadProduct]
        :param event_number_type: EN: Event type suffix (e.g., "4c")
                                  PT-BR: Sufixo do tipo de evento (ex: "4c")
        :type event_number_type: str
        :return: EN: Original DataFrame with daily event columns marked
                 PT-BR: DataFrame original com colunas de evento diário marcadas
        :rtype: pd.DataFrame
        """
        df = data.copy()

        if "index" not in df.columns:
            return df

        # EN: Ensures temporal ordering
        # PT-BR: Garante ordenação temporal
        df.sort_values(by="index", ascending=True, inplace=True)

        event_col_start = f"event{event_number_type}_start"
        event_col_end = f"event{event_number_type}_end"

        # EN: Initializes event columns
        # PT-BR: Inicializa colunas de evento
        df[event_col_start] = False
        df[event_col_end] = False

        # EN: Initializes metric columns
        # PT-BR: Inicializa colunas de métricas
        df["ProducingWasteDuration"] = 0.0
        df["TotalProducingWaste"] = 0.0
        df["total_duration_seconds"] = 0.0
        df["MDR"] = 0.0

        # EN: Auxiliary column for normalized date (day)
        # PT-BR: Coluna auxiliar de data normalizada (dia)
        df["event_date"] = df["index"].dt.normalize()

        # EN: Gets MDR from lead product for duration calculation
        # PT-BR: Obtém MDR do lead product para cálculo de duração
        lead_mdr = (
            WashingtonWorksEventIdentificationService.get_lead_product_mdr(
                mdr, lead_product
            )
        )

        # EN: Series with total daily scrap (summing all tags)
        # PT-BR: Série com scrap total diário (somando todas as tags)
        daily_scrap_total = None

        for tag in producing_waste_tags:
            if tag not in df.columns:
                continue

            # EN: Ensures numeric type
            # PT-BR: Garante tipo numérico
            values = pd.to_numeric(df[tag], errors="coerce").fillna(0)

            # EN: Increment calculation per sample **per day**:
            #     - We use diff() within each day (event_date) to ensure that
            #       the "initial value of the day" is the reference.
            #     - If current value >= previous in the same day: increment = current - previous
            #     - If current value < previous (reset within the day): increment = 0;
            #       increases after the reset will be captured as positive deltas.
            # PT-BR: Cálculo de incremento por amostra **por dia**:
            #        - Usamos diff() dentro de cada dia (event_date) para garantir que
            #          o "valor inicial do dia" seja a referência.
            #        - Se valor atual >= anterior no mesmo dia: incremento = atual - anterior
            #        - Se valor atual < anterior (reset dentro do dia): incremento = 0;
            #          os aumentos após o reset serão capturados como deltas positivos.
            deltas = values.groupby(df["event_date"]).diff()
            deltas = deltas.clip(lower=0).fillna(0)

            df[f"__delta_{tag}"] = deltas

            # EN: Daily scrap for this tag
            # PT-BR: Scrap diário para esta tag
            daily_scrap_tag = (
                df.groupby("event_date")[f"__delta_{tag}"].sum().astype(float)
            )

            if daily_scrap_total is None:
                daily_scrap_total = daily_scrap_tag
            else:
                daily_scrap_total = daily_scrap_total.add(
                    daily_scrap_tag, fill_value=0.0
                )

        # EN: If no tag was processed, returns without events
        # PT-BR: Se nenhuma tag foi processada, retorna sem eventos
        if daily_scrap_total is None:
            # EN: Fills MDR even without events
            # PT-BR: Preenche MDR mesmo sem eventos
            df["MDR"] = lead_mdr if not pd.isna(lead_mdr) else 0.0
            df.drop(
                columns=[c for c in df.columns if c.startswith("__delta_")],
                errors="ignore",
                inplace=True,
            )
            df.drop(columns=["event_date"], errors="ignore", inplace=True)
            return df

        # EN: Days with scrap > 0 generate events
        # PT-BR: Dias com scrap > 0 geram eventos
        days_with_events = daily_scrap_total[daily_scrap_total > 0].index

        # EN: Stores already processed indices to avoid duplication
        # PT-BR: Armazena os índices já processados para evitar duplicação
        processed_start_indices = set()
        processed_end_indices = set()

        for day in days_with_events:
            next_day = day + pd.Timedelta(days=1)

            # EN: Mask of rows belonging to the event day
            # PT-BR: Máscara das linhas pertencentes ao dia do evento
            mask_current_day = df["event_date"] == day

            if not mask_current_day.any():
                continue

            # EN: Total scrap of the day (sum of all tags)
            # PT-BR: Scrap total do dia (soma de todas as tags)
            scrap_total_day = daily_scrap_total[day]

            # EN: Calculates duration in seconds: (scrap_total / MDR) * 3600
            #     If MDR is not available or invalid, uses 0
            # PT-BR: Calcula duração em segundos: (scrap_total / MDR) * 3600
            #        Se MDR não disponível ou inválido, usa 0
            if pd.isna(lead_mdr) or lead_mdr <= 0:
                duration_seconds = 0.0
            else:
                duration_seconds = (scrap_total_day / lead_mdr) * 3600

            # EN: Event start: first sample of the day (ordered by timestamp)
            #     This ensures that even with timezone changes, we get the first chronological row
            # PT-BR: Início do evento: primeira amostra do dia (ordenada por timestamp)
            #        Isso garante que mesmo com mudança de fuso horário, pegamos a primeira linha cronológica
            day_rows = df[mask_current_day].sort_values(by="index")
            first_idx = day_rows.index[0]

            # EN: Event end: first sample of the next day (normalized), if it exists;
            #     otherwise, last sample of the current day (ordered by timestamp).
            # PT-BR: Fim do evento: primeira amostra do dia seguinte (normalizado), se existir;
            #        caso contrário, última amostra do dia atual (ordenada por timestamp).
            mask_next_day = df["event_date"] == next_day

            if mask_next_day.any():
                # EN: If there is data from the next day, uses the first row of that day (ordered)
                # PT-BR: Se há dados do dia seguinte, usa a primeira linha desse dia (ordenada)
                next_day_rows = df[mask_next_day].sort_values(by="index")
                last_idx = next_day_rows.index[0]
            else:
                # EN: If there is no data from the next day, searches for the first row after this day
                # PT-BR: Se não há dados do dia seguinte, procura a primeira linha após este dia
                mask_after_day = df["index"] >= next_day
                if mask_after_day.any():
                    after_day_rows = df[mask_after_day].sort_values(by="index")
                    last_idx = after_day_rows.index[0]
                else:
                    # EN: If there is no data after this day, uses the last row of the current day (ordered)
                    # PT-BR: Se não há nenhum dado após este dia, usa a última linha do dia atual (ordenada)
                    last_idx = day_rows.index[-1]

            # EN: Avoids duplication: only marks if not yet processed
            # PT-BR: Evita duplicação: só marca se ainda não foi processado
            if first_idx not in processed_start_indices:
                df.at[first_idx, event_col_start] = True
                processed_start_indices.add(first_idx)

            if last_idx not in processed_end_indices:
                df.at[last_idx, event_col_end] = True
                processed_end_indices.add(last_idx)

            # EN: Fills metrics only in start/end rows (not in all event rows)
            #     This avoids filling the same value multiple times
            # PT-BR: Preenche métricas apenas nas linhas de start/end (não em todas as linhas do evento)
            #        Isso evita preencher múltiplas vezes o mesmo valor
            df.at[first_idx, "TotalProducingWaste"] = scrap_total_day
            df.at[first_idx, "ProducingWasteDuration"] = duration_seconds
            df.at[first_idx, "total_duration_seconds"] = duration_seconds
            df.at[first_idx, "MDR"] = (
                lead_mdr if not pd.isna(lead_mdr) else 0.0
            )
            df.at[first_idx, "MDR_PRODUCING_WASTE"] = (
                lead_mdr if not pd.isna(lead_mdr) else 0.0
            )

            df.at[last_idx, "TotalProducingWaste"] = scrap_total_day
            df.at[last_idx, "ProducingWasteDuration"] = duration_seconds
            df.at[last_idx, "MDR"] = lead_mdr if not pd.isna(lead_mdr) else 0.0

        # EN: Cleans auxiliary columns
        # PT-BR: Limpa colunas auxiliares
        df.drop(
            columns=[c for c in df.columns if c.startswith("__delta_")],
            errors="ignore",
            inplace=True,
        )
        df.drop(columns=["event_date"], errors="ignore", inplace=True)

        # EN: Ensures only first occurrence of start/end
        # PT-BR: Garante somente primeira ocorrência de start/end
        df[event_col_start] = keep_only_first_occurrence(df, event_col_start)
        df[event_col_end] = keep_only_first_occurrence(df, event_col_end)

        # EN: Filters only rows where there is start or end of event
        # PT-BR: Filtra apenas linhas onde há início ou fim de evento
        event_mask = df[event_col_start] | df[event_col_end]
        df_filtered = df[event_mask].copy()

        # EN: Removes duplicates by normalized day (solves timezone problem)
        #     If there are multiple rows of the same normalized day, keeps only the last one
        #     (which is usually the most recent due to timezone change)
        # PT-BR: Remove duplicatas por dia normalizado (resolve problema de fuso horário)
        #        Se houver múltiplas linhas do mesmo dia normalizado, mantém apenas a última
        #        (que geralmente é a mais recente devido à mudança de fuso horário)
        df_filtered["event_date"] = df_filtered["index"].dt.normalize()

        # EN: Groups by normalized day and keeps only the last row of each day
        #     This solves the problem when there is a timezone change on the same day
        # PT-BR: Agrupa por dia normalizado e mantém apenas a última linha de cada dia
        #        Isso resolve o problema quando há mudança de fuso horário no mesmo dia
        df_filtered = (
            df_filtered.sort_values(by="index")
            .groupby("event_date", as_index=False)
            .last()
        )

        # EN: Normalizes timestamps to 00:00 of the corresponding day (keeps timezone)
        #     Example: 2025-11-02 23:00:00-05:00 -> 2025-11-02 00:00:00-05:00
        # PT-BR: Normaliza os timestamps para 00:00 do dia correspondente (mantém fuso horário)
        #        Exemplo: 2025-11-02 23:00:00-05:00 -> 2025-11-02 00:00:00-05:00
        df_filtered["index"] = df_filtered["index"].dt.normalize()

        # EN: Removes auxiliary column event_date
        # PT-BR: Remove coluna auxiliar event_date
        df_filtered.drop(columns=["event_date"], errors="ignore", inplace=True)

        # EN: Reorders by index
        # PT-BR: Reordena por index
        df_filtered.sort_values(by="index", inplace=True)

        # EN: Removes first and last row for safety
        # PT-BR: Remove primeira e última linha por segurança
        if len(df_filtered) > 2:
            df_filtered = df_filtered.iloc[1:-1].copy()
        elif len(df_filtered) > 0:
            # EN: If there are only 1 or 2 rows, returns empty to avoid problems
            # PT-BR: Se tiver apenas 1 ou 2 linhas, retorna vazio para evitar problemas
            df_filtered = df_filtered.iloc[0:0].copy()

        return df_filtered
