import logging
import os
import sys
import pandas as pd
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables
import time


SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_cognite_client(variables: EnvVariables) -> CogniteClient:
    """
    Cria o CogniteClient usando sua fábrica de clientes (CogniteClientFactory).
    Ajuste este método caso use outra forma de instanciar CogniteClient.
    """
    return CogniteClientFactory.create(variables)

def run():
    # 1) Carrega variáveis de ambiente (API keys, espaços, etc.)
    variables = EnvVariables()
    client = create_cognite_client(variables)

    # 2) Constrói o DataModelId (espaço + externalId + versão) para o GraphQL
    data_model_id = DataModelId(
        variables.cognite.data_model_space,
        variables.cognite.data_model_external_id,
        variables.cognite.data_model_version,
    )

    # 3) Monta a query GraphQL com placeholder para o cursor "after"
    graphql_query = """
    query MyQuery($after: String) {
      listOEEEvent(
        filter: { startDateTime: { gte: "2025-01-01T00:00:00+00:00" } }
        after: $after
        first: 1000
      ) {
        items {
          externalId
          startDateTime
          refOEEEventDetail(
                filter: {
                    and: [
                        {refEventCode: {externalId: {isNull: true}}}, 
                        {refSubCategoryL1: {externalId: {isNull: false}}}
                    ]
                }
                first: 1000
            ) {
                items {
                externalId
                }
            }
        }
        pageInfo {
          endCursor
          hasNextPage
        }
      }
    }
    """

    # 4) Variável que controlará a paginação
    cursor_after = None  # Agora iniciamos com None, não string vazia

    # 5) Lista onde vamos acumular cada linha: uma tupla (eventExternalId, eventStartDateTime, detailExternalId)
    collected_rows: list[dict[str, str]] = []

    # 6) Looping de paginação
    while True:
        # 6.1) Prepara o payload das variáveis. Se cursor_after for None, envia None,
        #      caso contrário, envia a string do cursor.
        variables_graphql = {"after": cursor_after}

        display_cursor = cursor_after[:10] + "..." if isinstance(cursor_after, str) else "None"
        logger.info(f"Executando GraphQL com cursor after = {display_cursor}")

        # 6.2) Executa a query
        time.sleep(0.3)  # Adiciona um pequeno delay para evitar rate limiting
        response = client.data_modeling.graphql.query(
            data_model_id,
            graphql_query,
            variables_graphql
        )

        # 6.3) Extrai o objeto listOEEEvent da resposta
        list_oee_event = response.get("listOEEEvent", {})
        items = list_oee_event.get("items", [])
        page_info = list_oee_event.get("pageInfo", {})

        # 6.4) Itera sobre cada evento retornado
        for event_node in items:
            event_external_id = event_node.get("externalId")
            event_start_dt = event_node.get("startDateTime")

            # A sublista refOEEEventDetail já vem filtrada apenas com detalhes sem eventCode
            detail_node_group = event_node.get("refOEEEventDetail", {})
            detail_items = detail_node_group.get("items", [])

            # Se existir pelo menos um detalhe cujo refEventCode.externalId seja nulo,
            # detail_items terá dicionários. Caso contrário, será lista vazia.
            for detail in detail_items:
                detail_external_id = detail.get("externalId")
                collected_rows.append({
                    "eventExternalId": event_external_id,
                    "eventStartDateTime": event_start_dt,
                    "detailExternalId": detail_external_id
                })

        # 6.5) Atualiza o cursor e verifica se há próxima página
        has_next = page_info.get("hasNextPage", False)
        end_cursor = page_info.get("endCursor")
        display_end = end_cursor[:10] + "..." if isinstance(end_cursor, str) else "None"
        logger.info(f"hasNextPage = {has_next}, endCursor = {display_end}")

        if not has_next or end_cursor is None:
            # Sai do loop quando não houver mais páginas ou endCursor inválido
            break

        # Caso ainda haja página, atualiza cursor e itera novamente
        cursor_after = end_cursor

    # 7) Ao final do laço, `collected_rows` tem todas as linhas desejadas. 
    # Agora convertemos para DataFrame do pandas.
    df_result = pd.DataFrame(collected_rows)

    if df_result.empty:
        logger.info("Nenhum detalhe de evento com eventCode nulo foi encontrado após 01/01/2025.")
    else:
        # Converte a coluna de data para datetime64, se quiser manipulá-la como timestamp
        df_result["eventStartDateTime"] = pd.to_datetime(df_result["eventStartDateTime"])
        # Exibe algumas linhas no log
        logger.info(f"Total de linhas coletadas: {len(df_result)}")
        logger.info(df_result.head(10))

        # Exemplo: salvar em CSV (opcional)
        # df_result.to_csv("oee_details_sem_eventCode.csv", index=False)
        # df_result.to_excel("oee_details_sem_eventCode.xlsx", index=False)

if __name__ == "__main__":
    run()
