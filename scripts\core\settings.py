import os
from typing import Dict

from dotenv import load_dotenv

common_variables = [
    "COGNITE_BASE_URI",
    "COGNITE_PROJECT",
    "COGNITE_CLIENT_NAME",
    "COGNITE_DATA_MODEL_EXTERNAL_ID",
    "COGNITE_DATA_MODEL_VERSION",
    "COGNITE_DATA_MODEL_SPACE",
    "COGNITE_DEFAULT_DATA_MODEL_INSTANCES_SPACE",
    "COGNITE_ASSET_HIERARCHY_INSTANCES_SPACE",
    "COGNITE_GRAPHQL_BASE_URI",
    "COGNITE_DATA_SET_ID",
    "AUTH_CLIENT_ID",
    "AUTH_TENANT_ID",
    "AUTH_SECRET",
    "AUTH_SCOPES",
    "AUTH_TOKEN_URI",
]

functions_variables: Dict[str, list[str]] = {}


def load_variables(function_name: str):
    load_dotenv()

    processed_variables = {}
    function_variables: list[str] = functions_variables.get(function_name) or []

    for var in common_variables + function_variables:
        value = os.getenv(var)
        if value is None:
            raise Exception(f"Key {var} not present in the Environment Variables")
        processed_variables[var] = value

    return processed_variables
