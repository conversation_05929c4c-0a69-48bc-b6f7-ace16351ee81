from typing import Any, Optional

from pydantic import Field

from .node import Node
from .reporting_line import ReportingLine


class InputTagConfiguration(Node):
    reporting_line: ReportingLine = Field(alias="reportingLine")
    time_series: str = Field(alias="timeSeries")
    alias: str = Field(alias="alias")
    event_identification: Optional[bool] = Field(
        False, alias="eventIdentification"
    )
    tag_value_mapping: Optional[bool] = Field(False, alias="tagValueMapping")

    @classmethod
    def from_cdf_response(
        cls, item: dict[str, Any]
    ) -> "InputTagConfiguration":
        return cls(
            externalId=item["externalId"],
            space=item["space"],
            reportingLine=ReportingLine(**item["reportingLine"]),
            timeSeries=item["timeSeries"]["externalId"]
            if item["timeSeries"]
            else "",
            alias=item["alias"],
            eventIdentification=item["eventIdentification"] or False,
            tagValueMapping=item["tagValueMapping"] or False,
        )
