from typing import Any

import pandas as pd

from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.models.lead_product import LeadProductBbct

class NanLTFEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
    ) -> None:
        super().__init__()
        if mdr is not None:
            self._valid_prod_ids = list(mdr.productId.unique())
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "4a": self.identify_events_typeIVa,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - 
        # 750HS01001.PV_D = "Stopped" AND 750XY01000_SOC.L1_BATCH_NUM_X reports a Batch ID without the letter 'T' (e.g. 02T1898408)
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] == "Stopped")
                & (data["ProductionLineStatus"].shift(1) != "Stopped")
                & (~data["BatchID"].astype(str).str.contains("T"))
            )
        )

        # event trigger end:
        # 750HS01001.PV_D = "RUNNING" or 750XY01000_SOC.L1_BATCH_NUM_X reports a Batch ID with the letter 'T' (e.g. 02T1898408) or time = 8:30pm or time = 8:30am or time = 12am 
        data = data.assign(
            event1a_end=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["ProductionLineStatus"].shift(1) != "Running")
                )
                | (
                    (data["BatchID"].str.contains('T'))
                    & (data["BatchID"] != data["BatchID"].shift(1))
                )
            )
        )
        
        data = data.assign(
            event1a_start=(
                (
                    (data["ProductionLineStatus"] == "Stopped")
                    & (data["ProductionLineStatus"].shift(1) != "Stopped")
                )
                & (~data["BatchID"].astype(str).str.contains("T"))
                & (
                    (data["event1a_start"].shift(1) is not True)
                    | (data["event1a_start"].shift(1).isna())
                )
            )
        )
        
        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIa event
        :rtype: pd.DataFrame
        """

        # event trigger start:
        # 750HS01001.PV_D = "RUNNING" AND 750XY01000_SOC.L1_BATCH_NUM_X reports a Batch ID with the letter 'T' (e.g. 02T1898408)
        data = data.assign(
            raw_condition_start=(
                (data["ProductionLineStatus"] == "Running")
                & (data["BatchID"].astype(str).str.contains("T"))
            )
        )
        
        data["event2a_start"] = (
            data["raw_condition_start"]
            & (~data["raw_condition_start"].shift(1, fill_value=False))
            & (data.index != data.index[0])
        )

        # event trigger end
        # 750HS01001.PV_D = "Stopped" or 750XY01000_SOC.L1_BATCH_NUM_X reports a Batch ID that is without the letter 'T' or time = 8:30pm or time = 8:30am or time = 12am 
        data = data.assign(
            raw_condition_end=(
                (data["ProductionLineStatus"] == "Stopped")
                | (~data["BatchID"].astype(str).str.contains("T"))
            )
        )

        data["event2a_end"] = data["raw_condition_end"] & (~data["raw_condition_end"].shift(fill_value=False))

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIc event
        :rtype: pd.DataFrame
        """

        # first form of event start:
        # 750HS01001.PV_D = "Stopped" AND 750XY01000_SOC.L1_BATCH_NUM_X reports a Batch ID with the letter 'T' (e.g. 02T1898408)
        data = data.assign(
            event2c_start=(
                (data["ProductionLineStatus"] == "Stopped")
                & (data["BatchID"].astype(str).str.contains("T"))
            )
        )

        # event end
        # (750HS01001.PV_D  = "RUNNING" AND Batch ID == Batch ID at start of the Event) or (750HS01001.PV_D  = "RUNNING" AND Batch ID != Batch ID at start of the Event) or (Batch ID reports a Batch ID without the letter 'T') or time = 8:30pm or time = 8:30am or time = 12am  
        data = data.assign(
            event2c_end=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["event2c_start"].shift(1) == True)
                )
                | (
                    (data["ProductionLineStatus"] == "Running")
                    & (~data["BatchID"].astype(str).str.contains("T"))
                    & (data["ProductionLineStatus"].shift(1) != "Running")
                )
                | (
                    (~data["BatchID"].astype(str).str.contains("T"))
                    & (data["BatchID"] != data["BatchID"].shift(1))
                )
            )
        )

        # fix event trigger start
        data = data.assign(
            event2c_start=(
                (
                    (data["ProductionLineStatus"] != "Running")
                    & (data["BatchID"].astype(str).str.contains("T"))
                )
                & (
                    (data["event2c_start"].shift(1) != True)
                    | (data["event2c_start"].shift(1).isna())
                )
            )
        )

        data = data[(data["event2c_start"] == True) | (data["event2c_end"] == True)]

        data = data.assign(
            event2c_start=(
                (data["event2c_start"] == True)
                & (data["BatchID"] == data["BatchID"].shift(-1))
            )
        )

        data = data.assign(
            event2c_end=(
                (data["event2c_end"] == True)
                & (data["BatchID"] == data["BatchID"].shift(1))
            )
        )

        return data

    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IId

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IId event
        :rtype: pd.DataFrame
        """

        # first form of event start 
        # 750HS01001.PV_D = "Stopped" AND 750XY01000_SOC.L1_BATCH_NUM_X reports a Batch ID with the letter 'T' (e.g. 02T1898408)
        data = data.assign(
            event2d_start=(
                (data["ProductionLineStatus"] == "Stopped")
                & (data["BatchID"].astype(str).str.contains("T"))
            )
        )

        # event end 
        # (750HS01001.PV_D  = "RUNNING" AND Batch ID == Batch ID at start of the Event) or (750HS01001.PV_D  = "RUNNING" AND Batch ID != Batch ID at start of the Event) or (Batch ID reports a Batch ID without the letter 'T') or time = 8:30pm or time = 8:30am or time = 12am  
        data = data.assign(
            event2d_end=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["event2d_start"].shift(1) == True)
                )
                | (
                    (data["ProductionLineStatus"] == "Running")
                    & (~data["BatchID"].astype(str).str.contains("T"))
                    & (data["ProductionLineStatus"].shift(1) != "Running")
                )
                | (
                    (~data["BatchID"].astype(str).str.contains("T"))
                    & (data["BatchID"] != data["BatchID"].shift(1))
                )
            )
        )

        # correct events starts to show only the start timestamp
        data = data.assign(
            event2d_start=(
                (
                    (data["ProductionLineStatus"] == "Stopped")
                    & (data["BatchID"].astype(str).str.contains("T"))
                )
                & (
                    (data["event2d_start"].shift(1) != True)
                    | (data["event2d_start"].shift(1).isna())
                )
            )
        )

        data = data[(data["event2d_start"] == True) | (data["event2d_end"] == True)]

        data = data.assign(
            event2d_start=(
                (data["event2d_start"] == True)
                & (data["BatchID"] != data["BatchID"].shift(-1))
                & (data["BatchID"].shift(-1).isna() == False)
            )
        )

        data = data.assign(
            event2d_end=(
                (data["event2d_end"] == True)
                & (data["BatchID"] != data["BatchID"].shift(1))
            )
        )

        return data

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self):
        pass
    
    def identify_events_typeIVa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IVa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IVa event
        :rtype: pd.DataFrame
        """

        # first form of event start - ProductionLineStatus = 'RUNNING' (1) and LineSpeed <= 10
        data = data.assign(
            event4a_start=(
                data["ProductionLineStatus"] == "Running")
                & (data["LineSpeed"] <= 10
            )
        )

        # event end - ProductionLineStatus = 'STOPPED' (0) or LineSpeed > 10
        data = data.assign(
            event4a_end=(
                (data["ProductionLineStatus"] == "Stopped")
                & (data["ProductionLineStatus"].shift(1) != "Stopped")
            )
            | (
                (data["LineSpeed"] > 10) 
                & (data["LineSpeed"].shift(1) <= 10)
            )
        )

        # correct events starts to show only the start timestamp
        data = data.assign(
            event4a_start=(
                (data["ProductionLineStatus"] == "Running")
                & (data["LineSpeed"] <= 10)
                & (
                    (data["event4a_start"].shift(1) != True)
                    | (data["event4a_start"].shift(1).isnull())
                )
            )
        )
                    
        return data

    def get_bbct_value(self, row: pd.Series, bbct_data: pd.DataFrame) -> float:
        """
        retrieves the BBCT from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        # extract filter parameter
        prod_id = row["Product"]
        event = row["event_definition"]
        start_time_year = row["start_time"].year
        start_time_month = row["start_time"].month

        # create reference date to find BBCT
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # if the event does not depend on BBCT values, return 0
        if not self._reporting_site_configuration.event_dependends_on_bbct(
            self._reporting_line_external_id,
            event,
        ):
            return 0

        # first filter - filter by year and month of the start date and
        # by the productid
        filter_1 = (
            (bbct_data["productId"].str.lower() == prod_id.lower())
            & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            & (bbct_data["year"] == start_time_year)
            & (bbct_data["month"] == start_time_month)
        )
        aux = bbct_data.loc[filter_1, :]

        # if aux is empty, we need to test the second filter
        if aux.shape[0] == 0:
            filter_2 = (bbct_data["productId"].str.lower() == prod_id.lower()) & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            aux = bbct_data.loc[filter_2, :]

            # if still the aux is empty, then we don't have matches, return 0
            if aux.shape[0] == 0:
                return 0

            # ensure ordering by the most recent year, considering the event start date
            t = (aux["timestamp"] - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # fill values to get the most recent date preceding the date of event
            aux.fillna(method="ffill", inplace=True)
            aux["bbct"].fillna(0, inplace=True)

            # ensure ordering by the most recent year, considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # extract value of BBCT
        return aux["bbct"].head(1).values[0]

    def fix_3b_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame, lead_bbct: LeadProductBbct
    ) -> pd.DataFrame:
        """
        applies the business that demands subtraction of the
        Best Batch Cycle time from every event of type 3b

        :param data: event data
        :type data: pd.DataFrame
        :return: data with the right duration of the events
        :rtype: pd.DataFrame
        """
        # apply extraction of the BBCT value
        data["bbct"] = data.apply(
            self.get_bbct_value,
            bbct_data=bbct_data,
            axis=1,
        )

        # fix events 3b duration
        data["total_duration_seconds"] -= data["bbct"]

        data = data.query("total_duration_seconds != 0")

        # fill NaN values with 0 when data does not have BBCT
        data["total_duration_seconds"].fillna(0, inplace=True)

        # events which products does not have bbct is reported with duration zero
        data.loc[
            ((data["event_definition"] == "3b") & (data["bbct"] == 0)),
            "total_duration_seconds",
        ] = 0

        # drop created column
        data.drop(columns=["bbct"], inplace=True)

        return data