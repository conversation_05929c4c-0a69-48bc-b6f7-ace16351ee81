from typing import Any

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.utils.constants import Constants as const
from app.utils.uom_conversion import mlb_to_mt, kg_to_mt
from app.utils.event_detection_utils import detect_sustained_transition

class BisGurContinuosEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)

        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp,
            multi_product=True
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "2e": self.identify_events_typeIIe,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def __fix_missing_production_status_line(
        self, data: pd.DataFrame
    ) -> pd.DataFrame:
        keys = [
            "ProductionLineStatus1",
            "ProductionLineStatus2",
            "ProductionLineStatus3",
            "ProductionLineStatus4",
            "ProductionLineStatus5",
            "ProductionLineStatus6",
        ]
        return data.assign(**{key: data[key].fillna(0) for key in keys})

    # product_list = list(BisGurProductMappingService()._mapping_dict.values())
    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        data = self.__fix_missing_production_status_line(data)

        start_key = "event1a_start"
        end_key = "event1a_end"

        # event trigger start - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 < 100
        # AND Product is not in ['Trial 6', 'Trial 12', 'Trial Max']
        data[start_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            < 100
        ) & (~data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))

        data[end_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            >= 100
        ) | (data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))
        # correct start and end flags
        # Correct start flags
        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )

        # Correct end flags
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """
        data = self.__fix_missing_production_status_line(data)

        # event trigger start - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 >= 100
        # and Product is in ['Trial 6', 'Trial 12', 'Trial Max']
        start_key = "event2a_start"
        end_key = "event2a_end"
        data[start_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            >= 100
        ) & (data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))

        # event trigger end - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 < 100
        # OR Product is not in ['Trial 6', 'Trial 12', 'Trial Max']
        data[end_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            < 100
        ) | (data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))

        # correct start and end flags

        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))

        return data

    def identify_events_typeIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        data = self.__fix_missing_production_status_line(data)
        start_key = "event2c_start"
        end_key = "event2c_end"

        # event trigger start - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 < 100
        # AND Product is in ['Trial 6', 'Trial 12', 'Trial Max']
        data[start_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            < 100
        ) & (data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))

        # event trigger end - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 >= 100
        # AND Product is not in ['Trial 6', 'Trial 12', 'Trial Max']
        data[end_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            >= 100
        ) | (~data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))
        # correct start and end flags
        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))

        # Product is equal to the product at the start of the event
        data[start_key] = data[start_key] & (
            data[const.PRODUCT] == data[const.PRODUCT].shift(-1)
        )
        data[end_key] = data[end_key] & (
            data[const.PRODUCT] == data[const.PRODUCT].shift(1)
        )

        return data

    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IId

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type II included
        :rtype: pd.DataFrame
        """

        data = self.__fix_missing_production_status_line(data)
        start_key = "event2d_start"
        end_key = "event2d_end"

        # event trigger start - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 >= 100
        # AND Product is in ['Trial 6', 'Trial 12', 'Trial Max']
        data[start_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            < 100
        ) & (data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))

        # event trigger end - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 >= 100
        # AND Product is not in ['Trial 6', 'Trial 12', 'Trial Max']
        data[end_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            >= 100
        ) | (~data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))
        # correct start and end flags
        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))
        # Product diffs to the product at the start of the event
        data[start_key] = data[start_key] & (
            data[const.PRODUCT] != data[const.PRODUCT].shift(-1)
        )
        data[end_key] = (data[end_key]) & (
            data[const.PRODUCT] != data[const.PRODUCT].shift(1)
        )

        return data

    def identify_events_typeIIe(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__fix_missing_production_status_line(data)

        start_key = "event2e_start"
        end_key = "event2e_end"
        # if ProductionLineStatus6 == 4 it corresponds to Run
        # if ProductionLineStatus6 == 5 it corresponds to Stop

        data["lineSum"] = (
            data["ProductionLineStatus1"]
            + data["ProductionLineStatus2"]
            + data["ProductionLineStatus3"]
        )

        data[start_key] = (
            detect_sustained_transition(
                df=data, 
                col="ProductionLineStatus5", 
                threshold=1, 
                duration="5min", 
                condition="lt"
            )
            & (data["ProductionLineStatus6"] == 5)
            & (data["lineSum"] >= 100) 
            & (~data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))
        )

        data.reset_index(inplace=True, drop=False)

        data[end_key] = (
            data["lineSum"] < 100
        ) | (data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))

        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))

        return data

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa
        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data = self.__fix_missing_production_status_line(data)

        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        data.rename(columns={const.PRODUCT: const.PRODUCT_DESCRIPTION}, inplace=True)
        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE], shift_value=1)
        data.rename(columns={const.PRODUCT_DESCRIPTION: const.PRODUCT}, inplace=True)
        
        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb
        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data = self.__fix_missing_production_status_line(data)

        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        data.rename(columns={const.PRODUCT: const.PRODUCT_DESCRIPTION}, inplace=True)
        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE], shift_value=1)
        data.rename(columns={const.PRODUCT_DESCRIPTION: const.PRODUCT}, inplace=True)
        
        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIc
        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data = self.__fix_missing_production_status_line(data)

        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        data.rename(columns={const.PRODUCT: const.PRODUCT_DESCRIPTION}, inplace=True)
        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE], shift_value=1)
        data.rename(columns={const.PRODUCT_DESCRIPTION: const.PRODUCT}, inplace=True)
        
        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]
        return day_data

    def calculate_net_production_fn(self, data: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced
        
        total_produced_key = "total_produced"

        data[total_produced_key] = (
            (
                data["ProductionLineStatus1"]
                * (data["ProductionLineStatus1"] > 10)
            )
            + (
                data["ProductionLineStatus2"]
                * (data["ProductionLineStatus2"] > 10)
            )
            + (
                data["ProductionLineStatus3"]
                * (data["ProductionLineStatus3"] > 10)
            )
        )
        per_hour_to_per_sec_factor = 1 / 3600

        data[total_produced_key] = (
            kg_to_mt(data[total_produced_key])
            * per_hour_to_per_sec_factor
            * data["dt"]
        )

        net_production = mlb_to_mt(data[total_produced_key])

        self._total_produced = net_production

        return net_production
    
    def not_running_fn(self, data: pd.DataFrame) -> pd.Series:
        if not self._not_running.empty:
            return self._not_running
        
        running_condition = (
            ((
            data["ProductionLineStatus1"]
            + data["ProductionLineStatus2"]
            + data["ProductionLineStatus3"]
        ) >= 100) 
            & (~data[const.PRODUCT].isin(["Trial 6", "Trial 12", "Trial Max"]))
        ) & (
            (data["ProductionLineStatus5"] >= 1) | (data["ProductionLineStatus6"] != 5) # shutting down condition
        )

        not_running = (~running_condition)
        
        self._not_running = not_running
        
        return not_running
