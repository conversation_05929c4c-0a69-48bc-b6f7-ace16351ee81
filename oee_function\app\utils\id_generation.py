from datetime import datetime, timezone
from enum import Enum
from typing import Generic, TypeVar

_E = TypeVar("_E", bound=Enum)


class IdGenerator(Generic[_E]):
    def __init__(self, ref_time: datetime = datetime.now(tz=timezone.utc)):
        self._sequence_map: dict[str, int] = {}
        self._ref_time = ref_time.strftime("%Y%m%d%H%M%S")

    def next_id(self, enum: _E):
        self._validate_enum(enum)
        self._create_sequence_if_not_exists(enum)
        self._sequence_map[enum.name] += 1
        return f"{enum.value}-{self._ref_time}-{self._sequence_map[enum.name]}"

    def _create_sequence_if_not_exists(self, enum: _E):
        if enum.name not in self._sequence_map:
            self._sequence_map[enum.name] = 0

    def _validate_enum(self, enum: _E):
        if not isinstance(enum, Enum):
            raise ValueError("enum must be an Enum")


if __name__ == "__main__":
    ref_time = datetime(2021, 9, 21, 12, 34, 56)

    class ExampleClass(Enum):
        ExampleProperty = "EX1"
        AnotherExampleProperty = "EX2"

    id_generator = IdGenerator[ExampleClass](ref_time)
    print(id_generator.next_id(ExampleClass.ExampleProperty))  # -> EX1-20210921123456-1
    print(id_generator.next_id(ExampleClass.ExampleProperty))  # -> EX1-20210921123456-2
    print(
        id_generator.next_id(ExampleClass.AnotherExampleProperty)
    )  # -> EX2-20210921123456-1
