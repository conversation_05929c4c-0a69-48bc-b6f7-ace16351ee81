from datetime import <PERSON><PERSON><PERSON>
from typing import List, <PERSON><PERSON>, Any

import pandas as pd


class HierarchicalEventsService:
    """
    User Story  v2 208027: [Events] Make the hierarchy of events automatically recognizable into the code for Compounding site type
    
    PT-BR:
    Service responsável por ajustar eventos hierár<PERSON><PERSON>, garantindo que eventos
    de menor prioridade não sobreponham eventos de maior prioridade.
    
    A hierarquia é definida pela ordem dos tipos de eventos: o primeiro tipo
    tem maior prioridade e os subsequentes são ajustados em relação aos anteriores.
    
    EN:
    Service responsible for adjusting hierarchical events, ensuring that events
    of lower priority do not overlap events of higher priority.
    
    The hierarchy is defined by the order of event types: the first type
    has the highest priority and the subsequent types are adjusted relative to the previous ones.
    """

    def __init__(self, event_types: List[str] = None):
        """
        PT-BR: Inicializa o serviço de eventos hierárquicos.
        EN: Initializes the hierarchical events service.
        
        Args:
            event_types: Lista de tipos de eventos em ordem de prioridade
                        (primeiro = maior prioridade). Padrão: ["1", "2", "4"]
        """
        self._event_types = event_types or ["1", "2", "4"]

    def fix_hierarchical_events(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        PT-BR: Ajusta eventos hierárquicos removendo ou cortando sobreposições entre
        eventos de diferentes prioridades.
        
        O primeiro tipo de evento na lista de prioridades é mantido intacto.
        Os demais tipos são ajustados para não sobrepor os tipos de maior prioridade.
        
        EN: Adjusts hierarchical events by removing or cutting overlaps between
        events of different priorities.
        
        The first event type in the priority list is kept intact.
        The subsequent types are adjusted to not overlap the higher priority types.
        
        Args:
            data: DataFrame containing events with columns:
                - event_definition: event type
                - start_time: event start
                - end_time: event end
                - rlt: (optional) event relation
                - other event-specific columns
        
        Returns:
            DataFrame with adjusted events, without improper overlaps between different priority levels.
        """
        if data.empty:
            return data

        # PT-BR: Ajusta o DataFrame de entrada criando evento mock inicial para simular/forçar o corte de eventos
        # EN: Adjust the input DataFrame by creating an initial mock event to simulate/force the event cut
        # data = self._create_data_mock_initial(data)
        # data = self._create_data_mock_middle(data)
        # data = self._create_data_mock_wrap(data)
        # data = self._create_data_mock_4a_wrap(data)

        # Ajusta os tempos dos eventos baseado na hierarquia
        data = self._adjust_event_times_by_hierarchy(data)
        
        return data

    def _extract_event_type(self, event_definition: str) -> str:
        """
        PT-BR:
        Extrai o tipo de evento do event_definition.
        
        EN: Extracts the event type from the event_definition.
        Returns the event type (ex: "1", "2", "4") from the event_definition.
        """
        if not event_definition or pd.isna(event_definition):
            return None
        return str(event_definition)[0] if len(str(event_definition)) > 0 else None

    def _events_overlap(self, start1: pd.Timestamp, end1: pd.Timestamp,
                        start2: pd.Timestamp, end2: pd.Timestamp) -> bool:
        """
        PT-BR:
        Verifica se dois intervalos de tempo se sobrepõem.
        
        EN: Checks if two time intervals overlap.
        
        Returns:
            True if the intervals overlap, False otherwise.
        """
        return start1 < end2 and start2 < end1

    def _adjust_event_against_higher_priority(
        self,
        lower_event: pd.Series,
        higher_priority_events: pd.DataFrame
    ) -> List[pd.Series]:
        """
        PT-BR:
        Ajusta um evento de menor prioridade em relação a eventos de maior prioridade.
        
        Este método implementa a lógica de ajuste baseada nos exemplos fornecidos:
        - Se o evento menor está completamente dentro do maior -> excluir
        - Se o evento menor começa antes e termina durante -> ajustar fim
        - Se o evento menor começa durante e termina depois -> ajustar início
        - Se o evento menor envolve completamente o maior -> dividir em 2 eventos
        
        EN: Adjusts a lower priority event relative to higher priority events.
        Implements the adjustment logic based on the examples provided:
        - If the lower event is completely inside the higher event -> exclude
        - If the lower event starts before and ends during -> adjust end
        - If the lower event starts during and ends after -> adjust start
        - If the lower event completely wraps the higher event -> split into 2 events
        
        Returns:
            PT-BR: Lista de Séries pandas com os eventos ajustados (pode ser vazia, 1 ou 2 eventos)
            EN: List of pandas Series with adjusted events (can be empty, 1 or 2 events)
        """
        lower_start = lower_event["start_time"]
        lower_end = lower_event["end_time"]
        
        # PT-BR: Lista de intervalos bloqueados pelos eventos de maior prioridade
        # EN: List of intervals blocked by higher priority events
        blocked_intervals = []
        
        for _, higher_event in higher_priority_events.iterrows():
            higher_start = higher_event["start_time"]
            higher_end = higher_event["end_time"]
            
            if self._events_overlap(lower_start, lower_end, higher_start, higher_end):
                blocked_intervals.append((higher_start, higher_end))
        
        # PT-BR: Se não há sobreposição, retorna o evento original
        # EN: If there is no overlap, return the original event
        if not blocked_intervals:
            return [lower_event]
        
        # PT-BR: Ordena os intervalos bloqueados por start_time
        # EN: Sorts the blocked intervals by start_time
        blocked_intervals.sort(key=lambda x: x[0])
        
        # PT-BR: Merge de intervalos sobrepostos
        # EN: Merges overlapping intervals
        merged_intervals = []
        for start, end in blocked_intervals:
            if not merged_intervals:
                merged_intervals.append((start, end))
            else:
                last_start, last_end = merged_intervals[-1]
                if start <= last_end:
                    # PT-BR: Intervalos se sobrepõem, merge
                    # EN: Intervals overlap, merge
                    merged_intervals[-1] = (last_start, max(last_end, end))
                else:
                    merged_intervals.append((start, end))
        
        # PT-BR: Gera os intervalos livres (não bloqueados)
        # EN: Generates free intervals (not blocked)
        free_intervals = []
        current_start = lower_start
        
        for blocked_start, blocked_end in merged_intervals:
            # PT-BR: Se há espaço antes do intervalo bloqueado
            # EN: If there is space before the blocked interval
            if current_start < blocked_start:
                free_intervals.append((current_start, blocked_start))
            current_start = max(current_start, blocked_end)
        
        # PT-BR: Se há espaço após o último intervalo bloqueado
        # EN: If there is space after the last blocked interval
        if current_start < lower_end:
            free_intervals.append((current_start, lower_end))
        
        # PT-BR: Cria eventos para cada intervalo livre
        # EN: Creates events for each free interval
        adjusted_events = []
        for free_start, free_end in free_intervals:
            # PT-BR: Ignora intervalos muito pequenos (menos de 1 segundo)
            # EN: Ignores very small intervals (less than 1 second)
            if (free_end - free_start).total_seconds() < 1:
                continue
            
            new_event = lower_event.copy()
            new_event["start_time"] = free_start
            new_event["end_time"] = free_end
            # PT-BR: Recalcula total_duration_seconds se existir
            # EN: Recalculates total_duration_seconds if it exists
            if "total_duration_seconds" in new_event:
                new_duration = (free_end - free_start).total_seconds()
                new_event["total_duration_seconds"] = new_duration
                # PT-BR: Ajusta original_total_duration_seconds para o mesmo valor
                # EN: Adjusts original_total_duration_seconds to the same value
                if "original_total_duration_seconds" in new_event:
                    new_event["original_total_duration_seconds"] = new_duration
            adjusted_events.append(new_event)
        
        return adjusted_events

    def _adjust_event_times_by_hierarchy(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        PT-BR:
        Ajusta os tempos dos eventos baseado na hierarquia definida.
        
        Processa eventos em ordem de hierarquia (do maior para o menor peso),
        ajustando eventos de menor prioridade para não sobrepor eventos de maior prioridade.
        
        EN: Adjusts event times based on the defined hierarchy.
        Processes events in order of hierarchy (from highest to lowest priority),
        adjusting lower priority events to not overlap higher priority events.
        
        Returns:
            DataFrame with adjusted events, without improper overlaps.
        """
        if data.empty:
            return data
        
        # PT-BR: Garante que start_time e end_time são datetime
        # EN: Ensures that start_time and end_time are datetime
        data = data.copy()
        data["start_time"] = pd.to_datetime(data["start_time"])
        data["end_time"] = pd.to_datetime(data["end_time"])
        
        # PT-BR: Extrai o tipo de evento para cada linha
        # EN: Extracts the event type for each line
        data["_event_type"] = data["event_definition"].apply(self._extract_event_type)
        
        # PT-BR: Separa eventos que estão na hierarquia e os que não estão
        # EN: Separates events that are in the hierarchy and those that are not
        hierarchical_events = data[data["_event_type"].isin(self._event_types)].copy()
        non_hierarchical_events = data[~data["_event_type"].isin(self._event_types)].copy()
        
        # PT-BR: Se não há eventos hierárquicos, retorna os dados originais sem a coluna auxiliar
        # EN: If there are no hierarchical events, returns the original data without the auxiliary column
        if hierarchical_events.empty:
            return data.drop(columns=["_event_type"], errors="ignore")
        
        # PT-BR: Ordena por start_time para processamento sequencial
        # EN: Sorts by start_time for sequential processing
        hierarchical_events = hierarchical_events.sort_values("start_time").reset_index(drop=True)
        
        # PT-BR: Processa eventos em ordem de hierarquia (do maior para o menor)
        # EN: Processes events in order of hierarchy (from highest to lowest priority)
        adjusted_events = []
        
        for priority_index, event_type in enumerate(self._event_types):
            # PT-BR: Eventos do tipo atual
            # EN: Current type events
            current_type_events = hierarchical_events[hierarchical_events["_event_type"] == event_type].copy()
            
            if current_type_events.empty:
                continue
            
            # PT-BR: Se é o primeiro tipo (maior prioridade), mantém intacto
            # EN: If it is the first type (highest priority), keeps it intact
            if priority_index == 0:
                adjusted_events.extend([row for _, row in current_type_events.iterrows()])
                continue
            
            # PT-BR: Para tipos de menor prioridade, ajusta em relação aos de maior prioridade
            # EN: For types of lower priority, adjusts relative to the higher priority
            higher_priority_types = self._event_types[:priority_index]
            higher_priority_events = hierarchical_events[
                hierarchical_events["_event_type"].isin(higher_priority_types)
            ].copy()
            
            # PT-BR: Ajusta cada evento do tipo atual
            # EN: Adjusts each event of the current type
            for _, lower_event in current_type_events.iterrows():
                adjusted = self._adjust_event_against_higher_priority(
                    lower_event,
                    higher_priority_events
                )
                adjusted_events.extend(adjusted)
        
        # PT-BR: Converte lista de séries para DataFrame
        # EN: Converts list of series to DataFrame
        if adjusted_events:
            adjusted_df = pd.DataFrame(adjusted_events)
            adjusted_df = adjusted_df.drop(columns=["_event_type"], errors="ignore")
        else:
            adjusted_df = hierarchical_events.drop(columns=["_event_type"], errors="ignore").iloc[0:0]
        
        # PT-BR: Adiciona eventos não hierárquicos de volta
        # EN: Adds non-hierarchical events back
        if not non_hierarchical_events.empty:
            non_hierarchical_events = non_hierarchical_events.drop(columns=["_event_type"], errors="ignore")
            result_df = pd.concat([adjusted_df, non_hierarchical_events], ignore_index=True)
        else:
            result_df = adjusted_df
        
        # PT-BR: Ordena por start_time
        # EN: Sorts by start_time
        if not result_df.empty:
            result_df = result_df.sort_values("start_time").reset_index(drop=True)
        
        return result_df

    def _create_data_mock_initial(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        PT-BR:
        Cria um evento mock inicial baseado no primeiro evento "2a" encontrado.
        
        Localiza o primeiro evento com event_definition contendo "2a" e cria um novo
        evento idêntico, mas com:
        - event_definition = "1a"
        - start_time = 1 hora antes do start_time do evento "2a"
        - end_time = 15 minutos antes do end_time do evento "2a"
        
        EN: Creates an initial mock event based on the first "2a" event found.
        Finds the first event with event_definition containing "2a" and creates a new event identical, but with:
        - event_definition = "1a"
        - start_time = 1 hour before the start_time of the "2a" event
        - end_time = 15 minutes before the end_time of the "2a" event
        
        Returns:
            DataFrame with the mock event added, if a "2a" event is found.
            Otherwise, returns the original DataFrame without changes.
        """
        if data.empty:
            return data

        # PT-BR: Localiza o primeiro evento com event_definition contendo "2a"
        # EN: Finds the first event with event_definition containing "2a"
        events_2a = data[data["event_definition"].str.contains("2a", na=False)]
        
        if events_2a.empty:
            return data
        
        first_event_2a = events_2a.iloc[0]

        # PT-BR: Cria uma cópia do evento
        # EN: Creates a copy of the event
        mock_event = first_event_2a.copy()

        # PT-BR: Ajusta event_definition para "1a"
        # EN: Adjusts event_definition to "1a"
        mock_event["event_definition"] = "1a"

        # PT-BR: Ajusta start_time: 1 hora antes do start_time do evento "2a"
        # EN: Adjusts start_time: 1 hour before the start_time of the "2a" event
        mock_event["start_time"] = first_event_2a["start_time"] - timedelta(hours=1)

        # PT-BR: Ajusta end_time: 15 minutos antes do end_time do evento "2a"
        # EN: Adjusts end_time: 15 minutes before the end_time of the "2a" event
        mock_event["end_time"] = first_event_2a["end_time"] - timedelta(minutes=15)

        # PT-BR: Converte para DataFrame e adiciona ao DataFrame original
        # EN: Converts to DataFrame and adds to the original DataFrame
        mock_event_df = pd.DataFrame([mock_event])
        result = pd.concat([data, mock_event_df], ignore_index=True)

        return result

    def _create_data_mock_middle(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        PT-BR:
        Cria um evento mock baseado no primeiro evento "2a" encontrado.
        
        Localiza o primeiro evento com event_definition contendo "2a" e cria um novo
        evento idêntico, mas com:
        - event_definition = "1a"
        - start_time = meio do evento "2a" (start_time + (end_time - start_time) / 2)
        - end_time = 1 hora após o end_time do evento "2a"
        
        EN: Creates a mock event based on the first "2a" event found.
        Finds the first event with event_definition containing "2a" and creates a new event identical, but with:
        - event_definition = "1a"
        - start_time = middle of the "2a" event (start_time + (end_time - start_time) / 2)
        - end_time = 1 hour after the end_time of the "2a" event
        
        Returns:
            DataFrame with the mock event added, if a "2a" event is found.
            Otherwise, returns the original DataFrame without changes.
        """
        if data.empty:
            return data

        # PT-BR: Localiza o primeiro evento com event_definition contendo "2a"
        # EN: Finds the first event with event_definition containing "2a"
        events_2a = data[data["event_definition"].str.contains("2a", na=False)]
        
        if events_2a.empty:
            return data


        first_event_2a = events_2a.iloc[0]
        start_time_2a = pd.to_datetime(first_event_2a["start_time"])
        end_time_2a = pd.to_datetime(first_event_2a["end_time"])

        # PT-BR: Calcula o meio do evento 2a
        # EN: Calculates the middle of the "2a" event
        duration = end_time_2a - start_time_2a
        middle_time = start_time_2a + (duration / 2)

        # PT-BR: Cria uma cópia do evento
        # EN: Creates a copy of the event
        mock_event = first_event_2a.copy()

        # PT-BR: Ajusta event_definition para "1a"
        # EN: Adjusts event_definition to "1a"
        mock_event["event_definition"] = "1a"

        # PT-BR: Ajusta start_time: meio do evento "2a"
        # EN: Adjusts start_time to the middle of the "2a" event
        mock_event["start_time"] = middle_time

        # PT-BR: Ajusta end_time: 1 hora após o end_time do evento "2a"
        # EN: Adjusts end_time to 1 hour after the end_time of the "2a" event
        mock_event["end_time"] = end_time_2a + timedelta(hours=1)

        # PT-BR: Recalcula total_duration_seconds se existir
        # EN: Recalculates total_duration_seconds if it exists
        if "total_duration_seconds" in mock_event:
            new_duration = (mock_event["end_time"] - mock_event["start_time"]).total_seconds()
            mock_event["total_duration_seconds"] = new_duration
            # PT-BR: Ajusta original_total_duration_seconds para o mesmo valor
            # EN: Adjusts original_total_duration_seconds to the same value
            if "original_total_duration_seconds" in mock_event:
                mock_event["original_total_duration_seconds"] = new_duration

        # PT-BR: Converte para DataFrame e adiciona ao DataFrame original
        # EN: Converts to DataFrame and adds to the original DataFrame
        mock_event_df = pd.DataFrame([mock_event])
        result = pd.concat([data, mock_event_df], ignore_index=True)

        return result

    def _create_data_mock_wrap(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        PT-BR:
        Cria um evento mock baseado no primeiro evento "2a" encontrado.
        
        Localiza o primeiro evento com event_definition contendo "2a" e cria um novo
        evento idêntico, mas com:
        - event_definition = "1a"
        - start_time = 1 hora antes do start_time do evento "2a"
        - end_time = 1 hora após o end_time do evento "2a"
        
        Este método cria um evento que envolve completamente o evento "2a",
        começando antes e terminando depois.
        
        EN: Creates a mock event based on the first "2a" event found.
        Finds the first event with event_definition containing "2a" and creates a new event identical, but with:
        - event_definition = "1a"
        - start_time = 1 hour before the start_time of the "2a" event
        - end_time = 1 hour after the end_time of the "2a" event
        
        This method creates an event that completely wraps the "2a" event, starting before and ending after.
        
        Returns:
            DataFrame with the mock event added, if a "2a" event is found.
            Otherwise, returns the original DataFrame without changes.
        """
        if data.empty:
            return data

        # PT-BR: Localiza o primeiro evento com event_definition contendo "2a"
        # EN: Finds the first event with event_definition containing "2a"
        events_2a = data[data["event_definition"].str.contains("2a", na=False)]

        if events_2a.empty:
            return data


        first_event_2a = events_2a.iloc[0]
        start_time_2a = pd.to_datetime(first_event_2a["start_time"])
        end_time_2a = pd.to_datetime(first_event_2a["end_time"])

        # EN: Creates a copy of the event
        mock_event = first_event_2a.copy()

        # EN: Adjusts event_definition to "1a"
        mock_event["event_definition"] = "1a"

        # EN: Adjusts start_time to 1 hour before the start_time of the "2a" event
        mock_event["start_time"] = start_time_2a - timedelta(hours=1)

        # EN: Adjusts end_time to 1 hour after the end_time of the "2a" event
        mock_event["end_time"] = end_time_2a + timedelta(hours=1)

        # EN: Recalculates total_duration_seconds if it exists
        if "total_duration_seconds" in mock_event:
            new_duration = (mock_event["end_time"] - mock_event["start_time"]).total_seconds()
            mock_event["total_duration_seconds"] = new_duration
            # PT-BR: Ajusta original_total_duration_seconds para o mesmo valor
            # EN: Adjusts original_total_duration_seconds to the same value
            if "original_total_duration_seconds" in mock_event:
                mock_event["original_total_duration_seconds"] = new_duration

        # EN: Converts to DataFrame and adds to the original DataFrame
        mock_event_df = pd.DataFrame([mock_event])
        result = pd.concat([data, mock_event_df], ignore_index=True)

        return result

    def _create_data_mock_4a_wrap(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        PT-BR:
        Cria um evento mock do tipo "4a" baseado no primeiro evento "2a" encontrado.
        
        Localiza o primeiro evento com event_definition contendo "2a" e cria um novo
        evento idêntico, mas com:
        - event_definition = "4a"
        - start_time = 1 hora antes do start_time do evento "2a"
        - end_time = 1 hora após o end_time do evento "2a"
        
        Este método cria um evento "4a" que envolve completamente o evento "2a",
        começando antes e terminando depois.
        
        EN: 
        Creates a mock event of type "4a" based on the first "2a" event found.
        Finds the first event with event_definition containing "2a" and creates a new event identical, but with:
        - event_definition = "4a"
        - start_time = 1 hour before the start_time of the "2a" event
        - end_time = 1 hour after the end_time of the "2a" event
        
        This method creates a "4a" event that completely wraps the "2a" event, starting before and ending after.
        
        Returns:
            DataFrame with the mock event added, if a "2a" event is found.
            Otherwise, returns the original DataFrame without changes.
        """
        
        if data.empty:
            return data

        # PT-BR: Localiza o primeiro evento com event_definition contendo "2a"
        # EN: Finds the first event with event_definition containing "2a"
        events_2a = data[data["event_definition"].str.contains("2a", na=False)]

        if events_2a.empty:
            return data

        first_event_2a = events_2a.iloc[0]

        # EN: Ensures that start_time and end_time are datetime
        start_time_2a = pd.to_datetime(first_event_2a["start_time"])
        end_time_2a = pd.to_datetime(first_event_2a["end_time"])

        # EN: Creates a copy of the event
        mock_event = first_event_2a.copy()

        # EN: Adjusts event_definition to "4a"
        mock_event["event_definition"] = "4a"

        # EN: Adjusts start_time to 1 hour before the start_time of the "2a" event
        mock_event["start_time"] = start_time_2a - timedelta(hours=1)

        # EN: Adjusts end_time to 1 hour after the end_time of the "2a" event
        mock_event["end_time"] = end_time_2a + timedelta(hours=1)

        # EN: Recalculates total_duration_seconds if it exists
        if "total_duration_seconds" in mock_event:
            new_duration = (mock_event["end_time"] - mock_event["start_time"]).total_seconds()
            mock_event["total_duration_seconds"] = new_duration
            # PT-BR: Ajusta original_total_duration_seconds para o mesmo valor
            # EN: Adjusts original_total_duration_seconds to the same value
            if "original_total_duration_seconds" in mock_event:
                mock_event["original_total_duration_seconds"] = new_duration

        # EN: Converts to DataFrame and adds to the original DataFrame
        mock_event_df = pd.DataFrame([mock_event])
        result = pd.concat([data, mock_event_df], ignore_index=True)

        return result
