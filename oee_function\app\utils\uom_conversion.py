import pandas as pd

# Conversion factors
MLB_TO_TONS = 0.453592
LBS_TO_KG_FACTOR = 0.453592
KG_TO_MT_DIVISION_FACTOR = 1000
LBS_TO_MT_DIVISION_FACTOR = 2204.62
HOURS_TO_SECONDS_FACTOR = 3600


# Conversion helpers
def mlb_to_mt(m_pounds: pd.DataFrame) -> pd.DataFrame:
    return m_pounds * MLB_TO_TONS


def mt_to_lb(tons: pd.DataFrame) -> pd.DataFrame:
    return tons * LBS_TO_MT_DIVISION_FACTOR


def lb_to_mt(pounds: pd.DataFrame) -> pd.DataFrame:
    return pounds / LBS_TO_MT_DIVISION_FACTOR


def kg_to_mt(kg: pd.DataFrame) -> pd.DataFrame:
    return kg / KG_TO_MT_DIVISION_FACTOR


def lbs_to_kg(lbs: pd.Series) -> pd.Series:
    return lbs * LBS_TO_KG_FACTOR
