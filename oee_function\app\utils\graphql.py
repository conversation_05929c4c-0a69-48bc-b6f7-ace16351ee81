from typing import Any, Optional, Union
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.data_models import DataModelId


def generate_query(list_name: str, selected_items: str) -> str:
    return f"""
        query Query($first: Int, $after: String, $filter: _{list_name[0].upper() + list_name[1:]}Filter) {{
            {list_name}(first: $first, after: $after, filter: $filter) {{
                items {{
                    {selected_items}
                }}
                pageInfo {{
                    hasNextPage
                    endCursor
                }}
            }}
        }}
        """


def query_all(
    client: CogniteClient,
    data_model_id: DataModelId,
    query: str,
    list_name: str,
    filter: Optional[dict[str, Any]] = None,
) -> list[Any]:
    variables = {
        "filter": filter,
        "first": 1000,
        "after": None,
    }
    items = []
    retry_count = 0
    while True:
        result = []
        try:
            response = client.data_modeling.graphql.query(
                id=data_model_id, query=query, variables=variables
            )
            result = response[list_name]
        except Exception as e:
            print("Error in query_all: ", e)

            if retry_count > 5:
                raise e
            retry_count += 1

            continue

        if result is None or "items" not in result:
            break

        items.extend(unwrap_query_result(item) for item in result["items"])

        pageInfo = result["pageInfo"]

        if not pageInfo["hasNextPage"] or pageInfo["endCursor"] is None:
            break

        variables["after"] = pageInfo["endCursor"]

    return items


def unwrap_query_result(result: dict[str, Any]) -> Union[None, dict[str, Any]]:
    if result is None:
        return None

    unwrapped_result = {}
    for key, value in result.items():
        if not isinstance(value, dict):
            unwrapped_result[key] = value
            continue

        if "items" in value:
            unwrapped_result[key] = [
                unwrap_query_result(v) for v in value["items"] or []
            ]
            continue

        unwrapped_result[key] = unwrap_query_result(value)

    return unwrapped_result
