from app.services.variable_categories_mapping.bis.biscmp_variable_categories_mapping_service import (
    BisCmpVariableCategoriesMappingService,
)
from app.services.variable_categories_mapping.flo.flocmp_variable_categories_mapping_service import (
    FloCmpVariableCategoriesMappingService,
)
from app.services.variable_categories_mapping.variable_categories_mapping import (
    VariableCategoriesMapping,
)
from app.services.variable_categories_mapping.win.wincmp_variable_categories_mapping_service import (
    WinCmpVariableCategoriesMappingService,
)
from app.services.variable_categories_mapping.frac.fracemu_variable_categories_mapping_service import (
    FracEmuVariableCategoriesMappingService,
)


class VariableCategoriesMappingFactory:
    def __init__(self) -> None:
        self._mapper = {
            "RLN-BISCMP133": BisCmpVariableCategoriesMappingService(),
            "RLN-BISCMPL70": BisCmpVariableCategoriesMappingService(),
            "RLN-BISCMPN92": BisCmpVariableCategoriesMappingService(),
            "RLN-BISCMPO92": BisCmpVariableCategoriesMappingService(),
            "RLN-BISCMPLN2": BisCmpVariableCategoriesMappingService(),
            "RLN-BISCMPLN3": BisCmpVariableCategoriesMappingService(),
            # Interrupted lines
            # "RLN-BISCMP517": BisCmpVariableCategoriesMappingService(),
            # "RLN-BISCMP518": BisCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP1001": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP1101": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP1501": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP1701": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP1901": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP2001": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP2101": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP2201": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP2301": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP4001": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP4003": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP4004": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP4005": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP4006": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP7001": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP7002": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP7003": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP7004": FloCmpVariableCategoriesMappingService(),
            "RLN-FLOCMP701": FloCmpVariableCategoriesMappingService(),
            "RLN-FRACNV101": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV102": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV103": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV105": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV106": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV107": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV108": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV109": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV112": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV113": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV118": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV119": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV121": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV123": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV125": FracEmuVariableCategoriesMappingService(),
            "RLN-FRACNV128": FracEmuVariableCategoriesMappingService(),
            "RLN-FRAVAERX1": FracEmuVariableCategoriesMappingService(),
            "RLN-FRAVAERX2": FracEmuVariableCategoriesMappingService(),
            "RLN-FRAVAERX3": FracEmuVariableCategoriesMappingService(),
            "RLN-FRAVAERX4": FracEmuVariableCategoriesMappingService(),
            "RLN-FRAVAERX5": FracEmuVariableCategoriesMappingService(),
            "RLN-FRAVAERX6": FracEmuVariableCategoriesMappingService(),
            "RLN-WINLFTL01": WinCmpVariableCategoriesMappingService(),
            "RLN-WINLFTL03": WinCmpVariableCategoriesMappingService(),
            "RLN-WINLFTL04": WinCmpVariableCategoriesMappingService(),
            "RLN-WINCFRL05": WinCmpVariableCategoriesMappingService(),
            "RLN-WINLFTL10": WinCmpVariableCategoriesMappingService(),
            "RLN-WINLFTL11": WinCmpVariableCategoriesMappingService(),
            "RLN-WINLFTL12": WinCmpVariableCategoriesMappingService(),
        }

    def create(self, reporting_line: str) -> VariableCategoriesMapping:
        return self._mapper.get(f"{reporting_line}")
