SELECT
  (CASE  
  	WHEN Vcode IN ("A0","A1","R1") THEN 
  		(CASE Arbpl 
  				WHEN "RX60002" THEN "OEE:GELVAE-E6K2-Rework"
  				WHEN "RX60003" THEN "OEE:GELVAE-E6K3-Rework"
  				WHEN "RX60004" THEN "OEE:GELVAE-E6K4-Rework"
  		END)
  	WHEN Vcode = "R2" THEN 
  		(CASE Arbpl 
  				WHEN "RX60002" THEN "OEE:GELVAE-E6K2-Scrap"
  				WHEN "RX60003" THEN "OEE:GELVAE-E6K3-Scrap"
  				WHEN "RX60004" THEN "OEE:GELVAE-E6K4-Scrap"
  		END)
  	WHEN Vcode = "R3" THEN 
  		(CASE Arbpl 
  				WHEN "RX60002" THEN "OEE:GELVAE-E6K2-ProducingWaste"
  				WHEN "RX60003" THEN "OEE:GELVAE-E6K3-ProducingWaste"
  				WHEN "RX60004" THEN "OEE:GELVAE-E6K4-ProducingWaste"
  		END)
  END) AS externalId,
  to_timestamp(`report_date`, 'yyyy-MM-dd') AS timestamp,
  cast(replace(`Lmengeist`,',', '') AS double) AS value
FROM
  `SAP-COR`.`PD-STATSRES-ZPQMBQL2`
WHERE
	Werk = 2055 -- Plant
	AND Vcode in ("A0", "A1", "R1", "R2", "R3")
  	AND Arbpl in ("RX60002","RX60003","RX60004")
