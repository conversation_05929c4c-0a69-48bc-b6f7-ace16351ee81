import pandas as pd
import numpy as np
from typing import Any, Optional
from app.models.lead_product import LeadProduct
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.was.washington_works_event_identification_service import (
    WashingtonWorksEventIdentificationService,
)
from app.utils.uom_conversion import lbs_to_kg
from app.infra.logger_adapter import get_logger

log = get_logger()


class WasMpwCmpEventIdentificationService(WashingtonWorksEventIdentificationService):
    """
    Service responsible to identify events in the lines Z1
    and Z2 of the MPW compounding unit of Washington site.
    """

    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: Optional[pd.DataFrame],
        product_transition: Optional[dict[str, dict[str, dict[str, int]]]],
        lead_products: Optional[list[LeadProduct]],
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._mdr = mdr
        self._product_transition = product_transition
        self._not_running_with_transitions = pd.DataFrame()
        self._not_running_during_product_trial = pd.DataFrame()
        self._products_pre_defined_set: set[str] = self.extract_products_list_from_mdr(
            mdr
        )
        self._lead_product = next(
            (
                x
                for x in (lead_products or [])
                if x.reporting_line.external_id == reporting_line_external_id
            ),
            None,
        )

        self.events_identification_methods = {
            "1a": self.identify_events_type_one_a,
            "1c": self.identify_events_type_one_c,
            "2a": self.identify_events_type_two_a,
            "2c": self.identify_events_type_two_c,
            "2d": self.identify_events_type_two_d,
            "4a": self.identify_events_type_four_a,
            "4b": self.identify_events_type_four_b,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_type_one_a(self, data: pd.DataFrame, **args) -> None:
        """
        Identifies Not Running events (type Ia).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate end and the end time of type Ia events
        :rtype: pd.DataFrame
        """
        processed_data = self.process_not_running_with_transitions(data)

        if processed_data.empty:
            return data

        if "event1c_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1c_start"], inplace=True)

        if "event1c_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1c_end"], inplace=True)

        return processed_data

    def identify_events_type_one_c(self, data: pd.DataFrame, **args) -> None:
        """
        Identify Not Running events (type Ic).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of type Ic events
        :rtype: pd.DataFrame
        """
        processed_data = self.process_not_running_with_transitions(data)

        if processed_data.empty:
            return data

        if "event1a_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1a_start"], inplace=True)

        if "event1a_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1a_end"], inplace=True)

        return processed_data

    def identify_events_type_two_a(self, data: pd.DataFrame, **args) -> None:
        """
        Identify Product Trial events (type IIa).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of type IIa events.
        :rtype: pd.DataFrame
        """
        event2a_start = self.product_trial_start_fn(data)
        consecutive_start = event2a_start.shift(1) & event2a_start
        event2a_start.loc[consecutive_start] = False

        event2a_end = self.product_trial_end_fn(data)
        consecutive_end = event2a_end.shift(1) & event2a_end
        event2a_end.loc[consecutive_end] = False

        data = data.assign(event2a_start=event2a_start, event2a_end=event2a_end)

        return data

    def identify_events_type_two_c(self, data: pd.DataFrame, **args) -> None:
        """
        Identify Not Running During Product Trial events (type IIc).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of type IIc events.
        :rtype: pd.DataFrame
        """
        processed_data = self.process_not_running_during_product_trial(data)

        if processed_data.empty:
            return data

        if "event2d_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event2d_start"], inplace=True)

        if "event2d_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event2d_end"], inplace=True)

        return processed_data

    def identify_events_type_two_d(self, data: pd.DataFrame, **args) -> None:
        """
        Identify Not Running events (type IId).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of type II2 events.
        :rtype: pd.DataFrame
        """
        processed_data = self.process_not_running_during_product_trial(data)

        if processed_data.empty:
            return data

        if "event2c_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event2c_start"], inplace=True)

        if "event2c_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event2c_end"], inplace=True)

        return processed_data

    def identify_events_type_four_a(self, data: pd.DataFrame, **args) -> None:
        """
        Identify Producing Waste events (type IVa).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of type IVa events.
        :rtype: pd.DataFrame
        """
        start_trigger = self.producing_waste_start_fn(data)
        event4a_start = (
            ~start_trigger.shift(periods=1, fill_value=False)
        ) & start_trigger

        end_trigger = self.producing_waste_end_fn(data)
        event4a_end = (~end_trigger.shift(periods=1, fill_value=False)) & end_trigger

        data = data.assign(event4a_start=event4a_start, event4a_end=event4a_end)

        return data

    def identify_events_type_four_b(self, data: pd.DataFrame, **args) -> None:
        """
        Identify Producing Waste by Quality Control events (type IVb).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of type IVb events.
        :rtype: pd.DataFrame
        """
        data_copy = data.copy()

        producing_waste_tags = ["PlopTotalizer", "DivertTotalizer"]

        freq = "12h"

        offset = pd.Timedelta(hours=5, minutes=30)

        triggers = {
            "NotRunning": {
                "start": self.not_running_start_fn,
                "end": self.not_running_end_fn,
            },
            "ProductTrial": {
                "start": self.product_trial_start_fn,
                "end": self.product_trial_end_fn,
            },
            "NotRunningDuringProductTrial": {
                "start": self.not_running_during_product_trial_start_fn,
                "end": self.not_running_during_product_trial_end_fn,
            },
            "ProducingWaste": {
                "start": self.producing_waste_start_fn,
                "end": self.producing_waste_end_fn,
            },
        }

        return self.process_producing_waste_by_quality_control(
            data_copy,
            producing_waste_tags,
            self._mdr,
            self._lead_product,
            freq,
            offset,
            triggers,
            "4b"
        )

    def not_running_start_fn(self, df: pd.DataFrame) -> pd.Series:
        # Start Trigger:
        # Feed Flow ≤ 1lb/h AND
        # Product Description Tag reports a Product code that is ON a predefined list
        return (df["FeedRate"] <= 1) & (
            df["ProductDescription"].isin(self._products_pre_defined_set)
        )

    def not_running_end_fn(self, df: pd.DataFrame) -> pd.Series:
        # End Trigger:
        # Feed Flow > 1lb/h OR
        # Product Description Tag reports a Product code that is NOT on a predefined list
        return (df["FeedRate"] > 1) | (
            ~df["ProductDescription"].isin(self._products_pre_defined_set)
        )

    def product_trial_start_fn(self, df: pd.DataFrame) -> pd.Series:
        # Start Trigger:
        # Feed Rate > 1lb/h AND
        # Product Description Tag reports a Product code that is NOT on a predefined list
        return (df["FeedRate"] > 1) & (
            ~df["ProductDescription"].isin(self._products_pre_defined_set)
        )

    def product_trial_end_fn(self, df: pd.DataFrame) -> pd.Series:
        # End Trigger:
        # Feed Rate <= 1lb/h OR
        # Product Description Tag reports a Product code that is in a predefined list
        return (df["FeedRate"] <= 1) | (
            df["ProductDescription"].isin(self._products_pre_defined_set)
        )

    def not_running_during_product_trial_start_fn(self, df: pd.DataFrame) -> pd.Series:
        # Start Trigger:
        # Feed Flow <= 1lb/h AND
        # Product Description Tag reports a Product code that is NOT on a predefined list
        return (df["FeedRate"] <= 1) & (
            ~(df["ProductDescription"].isin(self._products_pre_defined_set))
        )

    def not_running_during_product_trial_end_fn(self, df: pd.DataFrame) -> pd.Series:
        # End Trigger:
        # Feed Flow > 1lb/h OR
        # Product Description Tag reports a Product code that is on a predefined list
        return (df["FeedRate"] > 1) | (
            df["ProductDescription"].isin(self._products_pre_defined_set)
        )

    def producing_waste_start_fn(self, df: pd.DataFrame) -> pd.Series:
        # Start Trigger:
        # Divert Valve = 1 AND
        # Feed Flow > 1 AND
        # Product Descrition tag reports a Product code that is on a predefined list
        return (
            (df["DivertValve"] == 1)
            & (df["FeedRate"] > 1)
            & df["ProductDescription"].isin(self._products_pre_defined_set)
        )

    def producing_waste_end_fn(self, df: pd.DataFrame) -> pd.Series:
        # End Trigger:
        # Divert Valve = 0 OR
        # Feed Flow ≤ 1 OR
        # Product Descrition tag reports a Product code that is NOT on a predefined list
        return (
            (df["DivertValve"] == 0)
            | (df["FeedRate"] <= 1)
            | (~df["ProductDescription"].isin(self._products_pre_defined_set))
        )

    def process_not_running_with_transitions(self, df: pd.DataFrame) -> pd.DataFrame:
        if not self._not_running_with_transitions.empty:
            return self._not_running_with_transitions.copy()

        self._not_running_with_transitions: pd.DataFrame = (
            self.identify_not_running_with_transitions(
                self._reporting_line_external_id,
                df,
                self.not_running_start_fn,
                self.not_running_end_fn,
                self._products_pre_defined_set,
                self._product_transition,
                48,
            )
        )

        return self._not_running_with_transitions.copy()

    def process_not_running_during_product_trial(
        self, df: pd.DataFrame
    ) -> pd.DataFrame:
        if not self._not_running_during_product_trial.empty:
            return self._not_running_during_product_trial.copy()

        self._not_running_during_product_trial: pd.DataFrame = (
            self.identify_not_running_during_product_trial(
                df,
                self.not_running_during_product_trial_start_fn,
                self.not_running_during_product_trial_end_fn,
            )
        )

        return self._not_running_during_product_trial.copy()


class WasMpwSettings:
    """
    Settings for WASMPW event identification.
    """

    def __init__(
        self,
        reporting_line_external_id: str,
        mdr: Optional[pd.DataFrame],
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._products_pre_defined_set: set[str] = self.extract_products_list_from_mdr(
            mdr
        )

    def extract_products_list_from_mdr(self, df: pd.DataFrame | None) -> set[str]:
        products: set[str] = set()

        if df is None:
            return products

        if "refMaterial" not in df.columns.to_list():
            log.info("refMaterial column not found in the dataset.")
            return products

        for value in df["refMaterial"]:
            if not isinstance(value, dict):
                log.info("Invalid data type in refMaterial.")
                continue

            description = value.get("description", None)

            if description is not None:
                products.add(description)

        return products

    def prepare_hourly_data(self, data: pd.DataFrame) -> pd.DataFrame:
        def total_feed_aggregation(serie: pd.Series) -> float:
            first = serie.iloc[0]
            last = serie.iloc[-1]
            aggregated: float = np.nan

            if first < last:
                aggregated = last - first
            elif first > last:
                max_index = serie.idxmax()
                max = serie.loc[max_index]
                aggregated = (max - first) + last

            return aggregated

        data["isRunning"] = (data["FeedRate"] > 1) & (
            data["ProductDescription"].isin(self._products_pre_defined_set)
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "ProductDescription"], dropna=False
        ).agg(
            {
                "index": "first",
                "isRunning": "all",
                "TotalFeed": total_feed_aggregation,
                "Product": "first",
                "BatchID": "first",
                "running_duration": "sum",
                "ProductDescription": "first",
                "ProcessOrder": "first",
            }
        )

        data["TotalFeed"] = lbs_to_kg(data["TotalFeed"])

        return data
