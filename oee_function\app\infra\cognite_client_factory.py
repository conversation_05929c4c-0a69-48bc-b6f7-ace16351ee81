from cognite.client import ClientConfig, CogniteClient
from cognite.client.credentials import (
    CredentialProvider,
    OAuthClientCredentials,
    Token,
)

from .env_variables import EnvVariables


# class to factor the Cognite Client
class CogniteClientFactory:
    @staticmethod
    def _create_credentials(env_variables: EnvVariables) -> CredentialProvider:
        """
        creates the credential provider based on the passed
        environment variables

        :param env_variables: environment variables
        :type env_variables: EnvVariables
        :return: a credential provider that can Token or OAuthCredentials
        :rtype: CredentialProvider
        """

        # get auth variables and check the token override option
        auth_variables = env_variables.auth
        if auth_variables.token_override:
            return Token(auth_variables.token_override)

        # standard is to return OAuth Credentials
        auth_client = OAuthClientCredentials(
            token_url=auth_variables.token_uri,
            client_id=auth_variables.client_id,
            client_secret=auth_variables.secret,
            scopes=auth_variables.scopes,
        )
        
        authorization_header = auth_client.authorization_header()
        
        print(f"Token: {authorization_header[1]}")
        
        return auth_client

    @staticmethod
    def _create_client_config(env_variables: EnvVariables) -> ClientConfig:
        """
        creates the configuration options based on the environment variables

        :param env_variables: environment variables
        :type env_variables: EnvVariables
        :return: configuration dictionary for the client
        :rtype: ClientConfig
        """

        # extract cognite variables
        cognite_variables = env_variables.cognite
        return ClientConfig(
            client_name=cognite_variables.client_name,
            project=env_variables.cognite.project,
            credentials=CogniteClientFactory._create_credentials(
                env_variables
            ),
            base_url=cognite_variables.base_uri,
        )

    @staticmethod
    def create(env_variables: EnvVariables) -> CogniteClient:
        """
        creates the Cognite Client based on the environment variables

        :param env_variables: environment variables
        :type env_variables: EnvVariables
        :return: cognite client
        :rtype: CogniteClient
        """

        return CogniteClient(
            config=CogniteClientFactory._create_client_config(env_variables)
        )
