from typing import Any

import pandas as pd

from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.hourly_data_compounding import (
    HourlyDataCompounding,
)


class ForParCompoundingEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
    ) -> None:
        super().__init__()
        if mdr is not None:
            self._valid_prod_ids = list(mdr.productId.unique())
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def is_product_trial(self, batch_ids: pd.Series) -> pd.Series:
        """
        PT-BR:
        Identifica produtos de trial.
        Um produto é considerado trial quando o identificador não inicia com "000".
        
        EN:
        Identifies trial products.
        A product is considered trial when the identifier does not start with "000".

        :param batch_ids: Series with the batch/product codes.
        :type batch_ids: pd.Series
        :return: Series boolean indicating if each batch is trial.
        :rtype: pd.Series
        """

        batch_as_str = batch_ids.fillna("").astype(str).str.strip()
        has_value = batch_as_str != ""

        return has_value & (~batch_as_str.str.startswith("000"))

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        is_product_trial = self.is_product_trial(data["BatchID"])

        # event trigger start -  ProductionLineStatus = 'STOPPED' (0) and Batch = Batch ID without a letter T
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] != 3)
                & (data["ProductionLineStatus"].shift(1) == 3)
                & (~is_product_trial)
            )
        )
        # event trigger end   - ProductionLineStatus == 'RUNNING' (0) or Product = Product ID that is NOT ON list
        data = data.assign(
            event1a_end=(
                (
                    (data["ProductionLineStatus"] == 3)
                    & (data["ProductionLineStatus"].shift(1) != 3)
                )
                | (is_product_trial)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIa event
        :rtype: pd.DataFrame
        """

        is_product_trial = self.is_product_trial(data["BatchID"])

        running_trial = (data["ProductionLineStatus"] == 3) & is_product_trial
        prev_running_trial = running_trial.shift(1, fill_value=False)

        data = data.assign(
            event2a_start=running_trial & (~prev_running_trial),
            event2a_end=(~running_trial) & (prev_running_trial),
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIc event
        :rtype: pd.DataFrame
        """

        is_product_trial = self.is_product_trial(data["BatchID"])

        # Start Trigger: ProductionLineStatus != 3 AND BatchID reports a Trial Product
        data = data.assign(
            event2c_start=(
                (data["ProductionLineStatus"] != 3)
                & (is_product_trial)
            )
        )

        # Save Product ID at start for end trigger validation
        data = data.assign(
            _product_id_at_start=data["Product"].where(data["event2c_start"]),
        )
        data["_product_id_at_start"] = data["_product_id_at_start"].ffill()

        # End Trigger: (ProductionLineStatus = 3 AND Product ID == Product ID at start)
        # OR (ProductionLineStatus = 3 AND (Product ID != Product ID at start OR Batch ID não é Trial))
        data = data.assign(
            event2c_end=(
                (
                    (data["ProductionLineStatus"] == 3)
                    & (data["_product_id_at_start"].notna())
                    & (data["Product"] == data["_product_id_at_start"])
                )
                | (
                    (data["ProductionLineStatus"] == 3)
                    & (
                        (
                            (data["_product_id_at_start"].notna())
                            & (data["Product"] != data["_product_id_at_start"])
                        )
                        | (~is_product_trial)
                    )
                )
            )
        )

        # Fix event trigger start - remove consecutive duplicates
        data = data.assign(
            event2c_start=(
                (
                    (data["ProductionLineStatus"] != 3)
                    & (is_product_trial)
                )
                & (data["event2c_start"].shift(1) != True)
            )
        )

        # Filter only true booleans to get the correct events
        data = data[
            (data["event2c_start"] == True) | (data["event2c_end"] == True)
        ]

        # Final Validation: Batch ID no end == Batch ID no start
        data = data.assign(
            event2c_start=(
                (data["event2c_start"] == True)
                & (data["BatchID"] == data["BatchID"].shift(-1))
            )
        )

        data = data.assign(
            event2c_end=(
                (data["event2c_end"] == True)
                & (data["BatchID"] == data["BatchID"].shift(1))
            )
        )

        # Clean auxiliary columns
        data = data.drop(columns=["_product_id_at_start"], errors="ignore")

        return data

    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IId

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IId event
        :rtype: pd.DataFrame
        """

        is_product_trial = self.is_product_trial(data["BatchID"])

        # Start Trigger: ProductionLineStatus != 3 AND BatchID reports a Trial Product
        data = data.assign(
            event2d_start=(
                (data["ProductionLineStatus"] != 3)
                & (is_product_trial)
            )
        )

        # Save Product ID at start for end trigger validation
        data = data.assign(
            _product_id_at_start=data["Product"].where(data["event2d_start"]),
        )
        data["_product_id_at_start"] = data["_product_id_at_start"].ffill()

        # End Trigger: (ProductionLineStatus = 3 AND Product ID == Product ID at start)
        # OR (ProductionLineStatus = 3 AND (Product ID != Product ID at start OR Batch ID não é Trial))
        data = data.assign(
            event2d_end=(
                (
                    (data["ProductionLineStatus"] == 3)
                    & (data["_product_id_at_start"].notna())
                    & (data["Product"] == data["_product_id_at_start"])
                )
                | (
                    (data["ProductionLineStatus"] == 3)
                    & (
                        (
                            (data["_product_id_at_start"].notna())
                            & (data["Product"] != data["_product_id_at_start"])
                        )
                        | (~is_product_trial)
                    )
                )
            )
        )

        # Fix event trigger start - remove consecutive duplicates
        data = data.assign(
            event2d_start=(
                (
                    (data["ProductionLineStatus"] != 3)
                    & (is_product_trial)
                )
                & (data["event2d_start"].shift(1) != True)
            )
        )

        # Filter only true booleans to get the correct events
        data = data[
            (data["event2d_start"] == True) | (data["event2d_end"] == True)
        ]

        # Final Validation: Batch ID no end != Batch ID no start
        data = data.assign(
            event2d_start=(
                (data["event2d_start"] == True)
                & (data["BatchID"] != data["BatchID"].shift(-1))
            )
        )

        data = data.assign(
            event2d_end=(
                (data["event2d_end"] == True)
                & (data["BatchID"] != data["BatchID"].shift(1))
            )
        )

        # Clean auxiliary columns
        data = data.drop(columns=["_product_id_at_start"], errors="ignore")

        return data

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self):
        pass
