from typing import Any

import pandas as pd

from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.hourly_data_compounding import (
    HourlyDataCompounding,
)


class EvaCompoundingEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
    ) -> None:
        super().__init__()
        if mdr is not None:
            self._valid_prod_ids = list(mdr.productId.unique())
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start -  ProductionLineStatus = 'STOPPED' (0) and Batch = Batch ID without a letter T
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] == "Stopped")
                & (data["ProductionLineStatus"].shift(1) != "Stopped")
                & (~data["BatchID"].astype(str).str.contains("T"))
            )
        )
        # event trigger end   - ProductionLineStatus == 'RUNNING' (0) or Product = Product ID that is NOT ON list
        data = data.assign(
            event1a_end=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["ProductionLineStatus"].shift(1) != "Running")
                )
                | (
                    (data["BatchID"].astype(str).str.contains("T"))
                    & (data["BatchID"] != data["BatchID"].shift(1))
                )
            )
        )

        data = data.assign(
            event1a_start=(
                (
                    (data["ProductionLineStatus"] == "Stopped")
                    & (data["ProductionLineStatus"].shift(1) != "Stopped")
                )
                & (~data["BatchID"].astype(str).str.contains("T"))
                & (
                    (data["event1a_start"].shift(1) is not True)
                    | (data["event1a_start"].shift(1).isna())
                )
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIa event
        :rtype: pd.DataFrame
        """

        # first form of event start - ProductionLineStatus = 'RUNNING' (1) and Batch = Batch ID with letter T
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus"] == "Running")
                & (data["BatchID"].astype(str).str.contains("T"))
            )
        )

        # event end - ProductionLineStatus = 'STOPPED' (0) or Batch = Batch ID without a letter T
        data = data.assign(
            event2a_end=(
                (
                    (data["ProductionLineStatus"] == "Stopped")
                    | (~data["BatchID"].astype(str).str.contains("T"))
                )
                & (data["event2a_start"].shift(1) is True)
            )
        )

        # fix event trigger start
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus"] == "Running")
                & (data["ProductionLineStatus"].str.contains("T"))
                & (
                    (data["event2a_start"].shift(1) is not True)
                    | (data["event2a_start"].shift(1).isna())
                )
            )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIc event
        :rtype: pd.DataFrame
        """

        # first form of event start - ProductionLineStatus = 'STOPPED' (0) and Batch = Batch ID with letter T
        data = data.assign(
            event2c_start=(
                (data["ProductionLineStatus"] == "Stopped")
                & (data["BatchID"].astype(str).str.contains("T"))
            )
        )

        # event end - ProductionLineStatus = 'RUNNING' (1) and Batch = Batch ID of start event
        data = data.assign(
            event2c_end=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["event2c_start"].shift(1) is True)
                )
                | (
                    (data["ProductionLineStatus"] == "Running")
                    & (~data["BatchID"].astype(str).str.contains("T"))
                    & (data["ProductionLineStatus"].shift(1) != "Running")
                )
                | (
                    (~data["BatchID"].astype(str).str.contains("T"))
                    & (data["BatchID"] != data["BatchID"].shift(1))
                )
            )
        )

        # fix event trigger start

        data = data.assign(
            event2c_start=(
                (
                    (data["ProductionLineStatus"] != "Running")
                    & (~data["Product"].isin(self._valid_prod_ids))
                )
                & (data["event2c_start"].shift(1) is not True)
            )
        )

        data = data.assign(
            event2c_start=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["BatchID"].astype(str).str.contains("T"))
                )
                & (
                    (data["event2c_start"].shift(1) is True)
                    | (data["event2c_start"].shift(1).isna())
                )
            )
        )

        data = data[(data["event2c_start"] == True) | (data["event2c_end"] == True)]

        data = data.assign(
            event2c_start=(
                (data["event2c_start"] == True)
                & (data["BatchID"] == data["BatchID"].shift(-1))
            )
        )

        data = data.assign(
            event2c_end=(
                (data["event2c_end"] == True)
                & (data["BatchID"] == data["BatchID"].shift(1))
            )
        )

        return data

    def identify_events_typeIId(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IId

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IId event
        :rtype: pd.DataFrame
        """

        # first form of event start - ProductionLineStatus = 'STOPPED' (0) and Batch = Batch ID with letter T
        data = data.assign(
            event2d_start=(
                (data["ProductionLineStatus"] == "Stopped")
                & (data["BatchID"].astype(str).str.contains("T"))
            )
        )

        # event end - ProductionLineStatus = 'RUNNING' (1) and Batch != Batch ID of start event
        data = data.assign(
            event2d_end=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["event2d_start"].shift(1) == True)
                )
                | (
                    (data["ProductionLineStatus"] == "Running")
                    & (~data["ProductionLineStatus"].astype(str).str.contains("T"))
                    & (data["ProductionLineStatus"].shift(1) != "Running")
                )
                | (
                    (~data["BatchID"].astype(str).str.contains("T"))
                    & (data["BatchID"] != data["BatchID"].shift(1))
                )
            )
        )

        # correct events starts to show only the start timestamp
        data = data.assign(
            event2d_start=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["BatchID"].astype(str).str.contains("T"))
                )
                & (
                    (data["event2d_start"].shift(1) != True)
                    | (data["event2d_start"].shift(1).isna())
                )
            )
        )

        data = data[(data["event2d_start"] == True) | (data["event2d_end"] == True)]

        data = data.assign(
            event2d_start=(
                (data["event2d_start"] == True)
                & (data["BatchID"] != data["BatchID"].shift(-1))
            )
        )

        data = data.assign(
            event2d_end=(
                (data["event2d_end"] == True)
                & (data["BatchID"] != data["BatchID"].shift(1))
            )
        )

        return data

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self):
        pass
