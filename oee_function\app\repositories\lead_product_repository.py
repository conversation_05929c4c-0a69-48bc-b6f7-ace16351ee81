from typing import Optional, Any
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from app.utils.graphql import generate_query, query_all
from ..models.lead_product import LeadProduct
from ..infra.logger_adapter import get_logger

log = get_logger()


class LeadProductRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        data_model_id: DataModelId,
    ) -> None:
        self._cognite_client = cognite_client
        self._data_model_id = data_model_id
        self._list_name = "listOEELeadProduct"

    def get_all(
        self, filter: dict[str, Any] | None = None
    ) -> Optional[list[LeadProduct]]:
        selected_items = """
            externalId
            space
            refReportingSite {
                externalId
                space
            }
            refReportingLine {
                externalId
                space
            }
            refMaterial {
                externalId
                space
            }
            refOEEMSDP {
                externalId
                space
            }
            refOEEMDR {
                externalId
                space
            }
            refOEEBBCT {
                externalId
                space
            }
            startDate
            endDate
            leadMSDP
        """

        query_result = query_all(
            client=self._cognite_client,
            data_model_id=self._data_model_id,
            list_name=self._list_name,
            query=generate_query(self._list_name, selected_items),
            filter=filter,
        )

        if not query_result:
            return None

        return [LeadProduct.from_cognite_response(result) for result in query_result]

    def get_lead_product(
        self, reporting_line_external_id: str
    ) -> Optional[LeadProduct]:
        if not reporting_line_external_id:
            raise ValueError("Reporting Line External ID is required.")

        query_result = self._cognite_client.data_modeling.graphql.query(
            self._data_model_id,
            self.build_query(),
            {"reportingLine": reporting_line_external_id},
        )["listOEELeadProduct"]["items"]

        if not query_result:
            return None

        log.info(
            f"Lead product found for reporting line {reporting_line_external_id} result: {query_result}"
        )
        return LeadProduct.from_cognite_response(query_result[0])

    def build_query(self) -> str:
        return """
            query QueryLeadProduct($reportingLine: ID!) {
                listOEELeadProduct(
                    first: 1
                    filter: {
                        and: [
                            {refReportingLine: {externalId: {eq: $reportingLine}}}
                            {endDate: {isNull: true}}
                        ]
                    }
                    sort: {startDate: DESC}
                ) {
                    items {
                        externalId
                        space
                        startDate
                        endDate
                        refReportingLine {
                            externalId
                            space
                            name
                        }
                        refMaterial {
                            externalId
                            space
                            name
                        }
                        refOEEBBCT {
                            externalId
                            space
                            product
                            pITagValue
                            bestBatchCycleTimeMT
                            bestBatchCycleTimeHr
                        }
                        refOEEMDR {
                            externalId
                            space
                            product
                            piTagValue
                        }
                        refOEEMSDP {
                            externalId
                            space
                            productGroup
                            piTagValue
                        }
                    }
                }
            }
        """
