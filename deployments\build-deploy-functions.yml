variables:
  - group: oee-cicd
  - name: skipLintTest
    value: $(SKIP_LINT_TEST)
  - name: logEnvVars
    value: $(LOG_ENV_VARS)
  - name: pythonEnv
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: qa
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: dev
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: prod
    ${{ else }}:
      value: dev
  - name: celaneseProject
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: celanese-stg
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: celanese-dev
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: celanese
    ${{ else }}:
      value: celanese-dev
  - name: authClientId
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(AUTH_CLIENT_ID_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(AUTH_CLIENT_ID_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(AUTH_CLIENT_ID_PROD)
    ${{ else }}:
      value: $(AUTH_CLIENT_ID_DEV)
  - name: authSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(S_AUTH_CLIENT_SECRET_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(S_AUTH_CLIENT_SECRET_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(S_AUTH_CLIENT_SECRET_PROD)
    ${{ else }}:
      value: $(S_AUTH_CLIENT_SECRET_DEV)
  - name: cicdAuthClientId
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(CICD_AUTH_CLIENT_ID_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(CICD_AUTH_CLIENT_ID_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(CICD_AUTH_CLIENT_ID_PROD)
    ${{ else }}:
      value: $(CICD_AUTH_CLIENT_ID_DEV)
  - name: cicdAuthSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(S_CICD_AUTH_CLIENT_SECRET_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(S_CICD_AUTH_CLIENT_SECRET_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(S_CICD_AUTH_CLIENT_SECRET_PROD)
    ${{ else }}:
      value: $(S_CICD_AUTH_CLIENT_SECRET_DEV)
  - name: cronExpression
    ${{ if eq(variables['build.sourcebranchname'], 'qa') }}:
      value: $(CRON_QA)
    ${{ elseif eq(variables['build.sourcebranchname'], 'dev') }}:
      value: $(CRON_DEV)
    ${{ elseif eq(variables['build.sourcebranchname'], 'prod') }}:
      value: $(CRON_PROD)
    ${{ else }}:
      value: $(CRON_DEV)

stages:
  - stage: LintAndTestFunction
    displayName: "Run Lint and Pytest for ${{ variables.celaneseProject }} Project"
    jobs:
      - job: LintAndTest
        condition: ne(variables['SKIP_LINT_TEST'],1)
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: "$(PYTHON_VERSION)"
            displayName: "Use python $(PYTHON_VERSION)"

          - script: |
              cd $(System.DefaultWorkingDirectory)/$(FUNCTION_PATH)
              python -m pip install --upgrade pip
              pip install -r requirements.txt
            displayName: "Install function dependencies"

          - script: |
              cd $(System.DefaultWorkingDirectory)/$(FUNCTION_PATH)
              source ./scripts/lint.sh
            displayName: "Lint $(FUNCTION_NAME)"

          - script: |
              cd $(System.DefaultWorkingDirectory)/$(FUNCTION_PATH)
              pytest
            displayName: "Testing $(FUNCTION_NAME)"

  - stage: PublishCogniteFunction
    dependsOn: LintAndTestFunction
    displayName: "Publish Function to Cognite for ${{ variables.celaneseProject }} Project"
    jobs:
      - job: PublishFunction
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: "3.11.5"
            displayName: "Use python 3.11"

          - script: |
              cd $(System.DefaultWorkingDirectory)/scripts
              python -m pip install --upgrade pip
              pip install -r requirements.txt
            displayName: "Install dependencies"

          - script: |
              cd $(System.DefaultWorkingDirectory)/scripts
              python -m deploy deploy-cognite-function $(FUNCTION_NAME)
            displayName: "Deploy $(FUNCTION_NAME)"
            env:
              PYTHON_ENV: ${{ variables.pythonEnv }}
              AUTH_CICD_CLIENT_ID: ${{ variables.cicdAuthClientId }}
              AUTH_CICD_SECRET: ${{ variables.cicdAuthSecret }}
              AUTH_SECRET: ${{ variables.authSecret }}
              AUTH_CLIENT_ID: ${{ variables.authClientId }}
              CRON_EXPRESSION: ${{ variables.cronExpression }}
              LOG_ENV_VARS: ${{ variables.logEnvVars }}
