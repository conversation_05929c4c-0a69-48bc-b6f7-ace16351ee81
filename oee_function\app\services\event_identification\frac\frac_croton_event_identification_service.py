from typing import Any
import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import (
    RateLossTimeContinuous,
)
from app.utils.event_detection_utils import detect_sustained_transition
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value
from app.utils.constants import Constants as const
import app.utils.event_detection_utils as ed_utils
from app.utils.data_frame import reset_index, set_column_as_index


class FracCrotonEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._total_produced = pd.Series(dtype=float)
        self._not_running_start = pd.Series(dtype=bool)
        self._not_running_end = pd.Series(dtype=bool)
        self._starting_up_start = pd.Series(dtype=bool)
        self._starting_up_end = pd.Series(dtype=bool)
        self._shutting_down_start = pd.Series(dtype=bool)
        self._shutting_down_end = pd.Series(dtype=bool)
        self._running_time_for_rlt = pd.Series(dtype=bool)
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id, msdp=msdp
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            # ************************************************************************************************
            # CODE SUSPENSED:
            # User Story v2 210049: [FRA] [EUS] [CrH] Remove Starting Up Events
            # User Story v2 210050: [FRA] [EUS] [CrH] Remove Shutting Down Logic
            # User Story v2 142830: Stop Events type 4 (Quality) in IPH Croton
            # SUSPENDE EVENTS TYPE 4
            # Business Rules are flagged as false in the Event Hierarchy Configuration
            # ************************************************************************************************
            # "2a": self.identify_events_typeIIa,
            # "2b": self.identify_events_typeIIb,
            # "4a": self.identify_events_typeIVa,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies Not Running events (type Ia).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        set_column_as_index(df=data, col="index")
        event1a_start = self.not_running_start_fn(data)
        event1a_end = self.not_running_end_fn(data)
        data = data.assign(event1a_start=event1a_start, event1a_end=event1a_end)
        reset_index(data)

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies Starting Up events (type IIa).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        event2a_start = self.starting_up_start_fn(data)
        event2a_end = self.starting_up_end_fn(data)
        data = data.assign(event2a_start=event2a_start, event2a_end=event2a_end)
        reset_index(data)
        return data

    def identify_events_typeIIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies Shutting Down events (type IIb).

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        event2b_start = self.shutting_down_start_fn(data)
        event2b_end = self.shutting_down_end_fn(data)
        data = data.assign(event2b_start=event2b_start, event2b_end=event2b_end)
        reset_index(data)
        return data

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies Running Under Scheduled Rate events (type IIIa).

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        set_column_as_index(df=data, col="index")
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_condition_for_rlt_events(data)
        reset_index(data)

        day_data = self._rlt.create_day_data(
            data, "sum", [const.MSDP, const.SCHEDULED_RATE], 0
        )

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]
        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]

        return day_data

    def identify_events_typeIIIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies No Demand events (type IIIb).

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        set_column_as_index(df=data, col="index")
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_condition_for_rlt_events(data)
        reset_index(data)

        day_data = self._rlt.create_day_data(
            data, "sum", [const.MSDP, const.SCHEDULED_RATE], 0
        )

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]
        day_data["total_duration_seconds"] = day_data[const.RLT]
        reset_index(data)

        return day_data

    def identify_events_typeIIIc(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies Running Above MDR events (type IIIc).

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        set_column_as_index(df=data, col="index")
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_condition_for_rlt_events(data)
        reset_index(data)

        day_data = self._rlt.create_day_data(
            data, "sum", [const.MSDP, const.SCHEDULED_RATE], 0
        )

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]
        day_data["total_duration_seconds"] = day_data[const.RLT]
        reset_index(data)

        return day_data

    def identify_events_typeIVa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies events of type IV

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """

        data = self.__create_waste_day_data(data)

        data = data.assign(event4a_end=(data["WasteTotalizer"] > 0))
        data = data.assign(event4a_start=(data["event4a_end"].shift(-1) == True))

        data = data.assign(total_duration_seconds=data["WasteProduced"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )
        data.drop(columns=["WasteMass", "DayWaste"], inplace=True)

        return data

    def __create_waste_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create waste day data to support event 4a

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame

        """
        # create a column with the duration of WasteTotalizer
        data["WasteMass"] = (data["WasteTotalizer"] / 3600) * (data["dt"])
        data.set_index("index", inplace=True)
        # get the duration of WasteTotalizer and sum to the period of one day
        data["DayWaste"] = data["WasteMass"].rolling("24h").sum()

        reset_index(data)

        data["is_midnight"] = (
            (data["index"].dt.hour == 00)
            & (data["index"].dt.minute == 0)
            & (data["index"].dt.second == 0)
            & (data["index"].dt.microsecond == 0)
        )

        if data["is_midnight"].any():
            data = data[data["is_midnight"]]

        data["Year"] = data["index"].dt.year
        data["Month"] = data["index"].dt.month
        data["Day"] = data["index"].dt.day

        filter_msdp = (
            self._msdp["reportingLineExternalId"] == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        data["MSDP"] = data.apply(
            self.get_msdp_value, msdp_data=msdp_data, data_aux="msdp", axis=1
        )
        data["WasteProduced"] = (data["DayWaste"]) / (data["MSDP"] / 24)

        return data

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        return msdp_util_get_msdp_value(row, msdp_data, data_aux)

    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced

        net_production = df["TotalFeed"].diff().fillna(0)
        self._total_produced = net_production

        return net_production

    def not_running_start_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._not_running_start.empty:
            return self._not_running_start

        # Start Trigger:
        # ProductionLineStatus1 < 500
        not_running_start = df["ProductionLineStatus1"] < 500

        self._not_running_start = not_running_start

        return not_running_start

    def not_running_end_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._not_running_end.empty:
            return self._not_running_end

        # End Trigger:
        # ProductionLineStatus1 < 500  to ProductionLineStatus1 > 500 for at least 10 minutes
        not_running_end = detect_sustained_transition(
            df, "ProductionLineStatus1", 500, "10min", "gt"
        )

        self._not_running_end = not_running_end

        return not_running_end

    def starting_up_start_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._starting_up_start.empty:
            return self._starting_up_start

        not_running = self.not_running_start_fn(df)

        # Start trigger:
        # ProductionLineStatus1 goes from less than 500 for at least 10 minutes
        # to greater than 500 for at least 10 minutes
        transition = detect_sustained_transition(
            df, "ProductionLineStatus1", 500, "10min", "gt"
        )

        timestamps_before_transition = df.index[transition.shift(-1).fillna(False)]
        valid_median = pd.Series(False, index=df.index)
        timedelta = pd.Timedelta("10min")

        for timestamp in timestamps_before_transition:
            start_time = timestamp - timedelta
            mask: pd.Series[bool] = (df.index >= start_time) & (df.index < timestamp)

            if not mask.any():
                continue

            if df.loc[mask, "ProductionLineStatus1"].median() < 500:
                valid_median.at[timestamp] = True

        starting_up_start = valid_median.shift(1) & transition & (~not_running)

        self._starting_up_start = starting_up_start

        return starting_up_start

    def starting_up_end_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._starting_up_end.empty:
            return self._starting_up_end

        not_running = self.not_running_start_fn(df)

        # End trigger:
        # ProductionLineStatus2 goes from less than or equal 0 to greater than 0 for at least 2 minutes
        starting_up_end = detect_sustained_transition(
            df, "ProductionLineStatus2", 0, "2min", "gt"
        ) | (not_running)

        self._starting_up_end = starting_up_end

        return starting_up_end

    def shutting_down_start_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._shutting_down_start.empty:
            return self._shutting_down_start

        not_running = self.not_running_start_fn(df)

        # Start trigger:
        # ProductionLineStatus1 goes from greater than 500 to lower than 500 for at least 10 minutes
        shutting_down_start = detect_sustained_transition(
            df, "ProductionLineStatus1", 500, "10min", "lt"
        ) & (~not_running)

        self._shutting_down_start = shutting_down_start

        return shutting_down_start

    def shutting_down_end_fn(self, df: pd.DataFrame) -> "pd.Series[bool]":
        if not self._shutting_down_end.empty:
            return self._shutting_down_end

        not_running = self.not_running_start_fn(df)

        if df.index.name is None:
            df.set_index("index", inplace=True)
        elif df.index.name != "index":
            df.reset_index(inplace=True)
            df.set_index("index", inplace=True)

        df.sort_index()

        duration = "2min"
        timedelta = pd.Timedelta(duration)
        consecutive_zeros_processed = pd.Series(False, index=df.index).sort_index()
        prod_line_two_eq_zero = df["ProductionLineStatus2"] == 0
        consecutive_zeros = (prod_line_two_eq_zero.shift() == True) & (
            prod_line_two_eq_zero == True
        )
        consecutive_zeros.fillna(False, inplace=True)

        if consecutive_zeros.any():
            idxs_to_fix = (
                (consecutive_zeros == False) & (consecutive_zeros.shift(-1) == True)
            ).fillna(False)

            consecutive_zeros.loc[idxs_to_fix] = True

            initial_transition: pd.Series = (
                ((consecutive_zeros.shift() == False) & (consecutive_zeros == True))
                .sort_index()
                .fillna(False)
            )

            final_transition: pd.Series = (
                ((consecutive_zeros.shift() == True) & (consecutive_zeros == False))
                .sort_index()
                .fillna(False)
            )

            if len(initial_transition) > len(final_transition):
                final_transition.at[len(final_transition) - 1] = True

            if len(initial_transition) < len(final_transition):
                initial_transition.at[0] = True

            starts = initial_transition[initial_transition]
            ends = final_transition[final_transition]

            if len(starts) == len(ends):
                for i in range(0, len(starts) - 1, 1):
                    start = starts.index[i]
                    end = ends.index[i] - timedelta

                    if start > end:
                        continue

                    mask = (consecutive_zeros_processed.index >= start) & (
                        consecutive_zeros_processed.index <= end
                    )
                    consecutive_zeros_processed.loc[mask] = True

        non_consecutive_zeros = prod_line_two_eq_zero & (~consecutive_zeros)
        non_consecutive_zeros_processed = pd.Series(False, index=df.index).sort_index()

        for timestamp in df.index[non_consecutive_zeros]:
            end_time = timestamp + timedelta
            mask = (df.index >= timestamp) & (df.index <= end_time)

            if not mask.any():
                continue

            if (df.loc[mask, "ProductionLineStatus2"] == 0).all():
                non_consecutive_zeros_processed.at[timestamp] = True

        # End trigger:
        # ProductionLineStatus2 equals 0 for at least 2 minutes
        shutting_down_end = (
            (consecutive_zeros_processed)
            | (non_consecutive_zeros_processed)
            | (not_running)
        )

        self._shutting_down_end = shutting_down_end

        return shutting_down_end

    def not_running_condition_for_rlt_events(
        self, df: pd.DataFrame
    ) -> "pd.Series[bool]":
        if not self._running_time_for_rlt.empty:
            return self._running_time_for_rlt

        df_work = df.copy()

        df_work["not_running_start"] = self.not_running_start_fn(df_work)
        df_work["not_running_end"] = self.not_running_end_fn(df_work)

        # ***************************CODE SUSPENSED***************************
        # df_work["starting_up_start"] = self.starting_up_start_fn(df_work)
        # df_work["starting_up_end"] = self.starting_up_end_fn(df_work)
        # df_work["shutting_down_start"] = self.shutting_down_start_fn(df_work)
        # df_work["shutting_down_end"] = self.shutting_down_end_fn(df_work)

        df_work = self.__process_event_conditions(df_work)

        running_time_for_rlt = df_work["not_running"]

        # ******************************CODE SUSPENSED******************************
        # running_time_for_rlt = (
        #     df_work["not_running"] | df_work["starting_up"] | df_work["shutting_down"]
        # )

        self._running_time_for_rlt = running_time_for_rlt

        return running_time_for_rlt

    def __process_event_conditions(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Processes event conditions for 'not_running'.

        :param df: DataFrame containing the data
        """
        # Initialize columns
        df["not_running"] = False

        # ******CODE SUSPENSED******
        # df["starting_up"] = False
        # df["shutting_down"] = False

        ed_utils.pair_start_end_indexes(
            df, "not_running_start", "not_running_end", "not_running", True
        )

        # ******************************CODE SUSPENSED******************************
        # ed_utils.pair_start_end_indexes(
        #     df, "starting_up_start", "starting_up_end", "starting_up", True
        # )
        # ed_utils.pair_start_end_indexes(
        #     df, "shutting_down_start", "shutting_down_end", "shutting_down", True
        # )

        return df
