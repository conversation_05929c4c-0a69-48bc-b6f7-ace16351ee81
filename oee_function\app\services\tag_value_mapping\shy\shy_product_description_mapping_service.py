import pandas as pd


class ShyProductDescriptionMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            1: "LKX1365",
            2: "E950ISX",
            3: "J950SX",
            4: "S950SX",
            5: "T950SX",
            6: "ZE7000SX",
            7: "ZE7700SX",
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})
