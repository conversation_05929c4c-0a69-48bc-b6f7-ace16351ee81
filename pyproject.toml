[tool.ruff]
line-length = 79
target-version = "py311"

[tool.ruff.lint]
extend-select = ["I"]

[tool.poetry]
name = "oee"
version = "0.1.0"
description = "project that captures website events and deploys the events to Cognite"
authors = ["jhonat.souza <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "3.11.9"
cognite-sdk = "7.43.3"
pandas = "2.1.3"
loguru = "0.7.2"
ruff = "0.4.4"
pydantic = "2.5.3"
pydantic-settings = "2.2.1"
gql = "3.5.0"
pyarrow = "16.1.0"
openpyxl = "^3.1.4"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
