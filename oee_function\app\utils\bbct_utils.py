import pandas as pd

from app.models.reporting_site_configuration import ReportingSiteConfiguration

def get_bbct_value(
    row: pd.Series, 
    bbct_data: pd.DataFrame, 
    reporting_line_external_id: str,
    reporting_site_configuration: ReportingSiteConfiguration,
) -> float:
    """
    retrieves the BBCT from the data stored in the contract

    :param row: row containing the product ID and start_time
    :type row: pd.Series
    :return: BBCT value
    :rtype: float
    """
    # extract filter parameter
    prod_id = row["ProductDescription"]
    event = row["event_definition"]
    start_time_year = row["start_time"].year
    start_time_month = row["start_time"].month

    # create reference date to find BBCT
    ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

    # if the event does not depend on BBCT values, return 0
    if not reporting_site_configuration.event_dependends_on_bbct(
        reporting_line_external_id,
        event,
    ):
        return 0

    # first filter - filter by year and month of the start date and
    # by the productid
    filter_1 = (
        (bbct_data["productId"].str.lower() == prod_id.lower())
        & (
            bbct_data["reportingLineExternalId"]
            == reporting_line_external_id
        )
        & (bbct_data["year"] == start_time_year)
        & (bbct_data["month"] == start_time_month)
    )
    aux = bbct_data.loc[filter_1, :]

    # if aux is empty, we need to test the second filter
    if aux.shape[0] == 0:
        filter_2 = (bbct_data["productId"].str.lower() == prod_id.lower()) & (
            bbct_data["reportingLineExternalId"]
            == reporting_line_external_id
        )
        aux = bbct_data.loc[filter_2, :]

        # if still the aux is empty, then we don't have matches, return 0
        if aux.shape[0] == 0:
            return 0

        # ensure ordering by the most recent year, considering the event start date
        t = (aux["timestamp"] - ref_date).abs().values
        aux.loc[:, "diff_dates"] = t
        aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

        # fill values to get the most recent date preceding the date of event
        aux.ffill(inplace=True)
        aux["bbct"].fillna(0, inplace=True)

        # ensure ordering by the most recent year, considering the event start date
        aux.sort_values(by=["diff_dates"], inplace=True)

    # extract value of MDR
    return aux["bbct"].head(1).values[0]