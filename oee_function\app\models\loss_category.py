from typing import Any
from .node import Node
from pydantic import Field


class LossCategory(Node):
    name: str = Field(alias="name")
    is_active: bool = Field(alias="isActive")

    @classmethod
    def from_cognite_response(cls, item: dict[str, Any]) -> "LossCategory":
        return cls(
            externalId=item["externalId"],
            space=item["space"],
            name=item["name"],
            isActive=item["isActive"],
        )
