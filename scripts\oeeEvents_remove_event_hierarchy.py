import json
import logging
import os
import sys
from datetime import datetime

import pandas as pd
from cognite.client.data_classes.data_modeling import NodeId
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables

"""
    ATENÇÃO: Este script irá localizar e deletar hierarquias específicas com base nos seguintes filtros:
    - Linhas
    - Events Definitions
    - Event Hierarchies
"""

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from oee_function.app.infra.logger_adapter import get_logger
from oee_function.app.repositories.event_hierarchy_configuration_repository import EventHierarchyConfigurationRepository
from oee_function.app.repositories.view_repository import ViewRepository

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

LINES = ["RLN-CLKVAMVAM"] # External IDs das linhas
EVENT_HIERARCHIES = ["2a","2b"] # Event Hierarchies
EVENT_DEFINITIONS = ["Running Under MDR", "Running Above MDR"]  # Event Definitions

def require_current_day():
    """
    Solicita ao usuário que insira o dia atual para liberar a execução do script.
    """
    today = datetime.now().strftime("%d")
    print("\nAtenção: Este processo é irreversivel para continuar, digite o dia atual (somente número): ")
    
    user_input = input("Dia atual: ").strip()
    
    if user_input != today:
        logger.error("Dia inserido incorreto. Execução abortada.")
        sys.exit(1)  # Encerra o programa com código de erro

    logger.info("Dia confirmado corretamente. Continuando execução.")

def create_cognite_client(variables: EnvVariables):
    """Cria uma instância do cliente Cognite usando as variáveis de ambiente."""
    return CogniteClientFactory.create(variables)

def create_repositories(variables: EnvVariables):
    """Inicializa os repositórios necessários para acessar os dados."""
    cognite_client = create_cognite_client(variables)
    data_model_id = DataModelId(
        variables.cognite.data_model_space,
        variables.cognite.data_model_external_id,
        variables.cognite.data_model_version,
    )
    return {
        "event_hierarchy_config": EventHierarchyConfigurationRepository(
            cognite_client,
            data_model_id,
            view_repository=ViewRepository(cognite_client, data_model_id),
        ),
    }


def build_dynamic_query(lines, event_hierarchies, event_definitions):
    """Constrói a query dinâmica para buscar hierarquias de evento com base nos filtros fornecidos."""
    filters = []
    if lines:
        filters.append(f'{{ reportingLine: {{ externalId: {{in:{json.dumps(lines)}}}}}}}')
    if event_hierarchies:
        filters.append(f'{{ eventHierarchy: {{in:{json.dumps(event_hierarchies)}}}}}')
    if event_definitions:
        filters.append(f'{{ eventDefinition: {{in:{json.dumps(event_definitions)}}}}}')
    
    filters_str = ", ".join(filters)
    query = f"""
        query MyQuery($after: String) {{
            listOEEEventHierarchyConfiguration(
                first: 1000
                filter: {{ and: [{filters_str}]}}
                after: $after
            )
            {{
                items {{
                    externalId
                    space
                    eventHierarchy
                    eventDefinition
                    reportingLine {{
                        externalId
                    }}
                }}
                pageInfo {{
                    endCursor
                    hasNextPage
                }}
            }}
        }}
    """
    return query

def get_filtered_event_hierarchies(variables: EnvVariables) -> pd.DataFrame:
    """Busca hierarquias que atendem aos filtros fornecidos."""
    repositories = create_repositories(variables)
    event_hierarchy_repository = repositories.get("event_hierarchy_config")
    if event_hierarchy_repository is None:
        logger.error("EventHierarchyConfigurationRepository não encontrado.")
        return pd.DataFrame()
    
    # Constrói a query com base nos filtros fornecidos
    query = build_dynamic_query(LINES, EVENT_HIERARCHIES, EVENT_DEFINITIONS)

    # Executa a query e retorna os resultados como DataFrame
    event_hierarchies = event_hierarchy_repository.list_all(query=query, variables={})
    if not event_hierarchies:
        return pd.DataFrame()

    return pd.DataFrame(event_hierarchies)


def delete_event_hierarchies_in_batch(variables: EnvVariables, event_hierarchy_data: pd.DataFrame, batch_size: int = 100) -> None:
    """Exclui hierarquias listadas em event_hierarchy_data em lotes."""
    if event_hierarchy_data.empty:
        logger.info("Nenhuma hierarquia de evento para deletar.")
        return

    repositories = create_repositories(variables)
    event_hierarchy_repository = repositories.get("event_hierarchy_config")

    # Prepara a lista de NodeIds
    nodes_to_delete = [
        NodeId(external_id=row["externalId"], space=row["space"])
        for _, row in event_hierarchy_data.iterrows()
    ]


    try:
        for i in range(0, len(nodes_to_delete), batch_size):
            batch = nodes_to_delete[i:i + batch_size]
            event_hierarchy_repository._cognite_client.data_modeling.instances.delete(nodes=batch)
            logger.info(f"Lote {i//batch_size + 1}: {len(batch)} hierarquias de evento deletadas com sucesso.")
    except Exception as e:
        logger.error(f"Erro ao deletar lote: {e}")

def run():
    """Localiza e exclui hierarquias de evento com base nos filtros definidos."""
    
    require_current_day()
    
    variables = EnvVariables()
    
    # Busca hierarquias com os filtros fornecidos
    logger.info("Buscando hierarquias de evento que atendem aos filtros...")
    event_hierarchy_data = get_filtered_event_hierarchies(variables)
    
    if event_hierarchy_data.empty:
        logger.info("Nenhuma hierarquia de evento encontrada com os filtros fornecidos.")
    else:
        logger.info(f"Encontradas {len(event_hierarchy_data)} hierarquias de evento:")
        
        # Executa a deleção
        delete_event_hierarchies_in_batch(variables, event_hierarchy_data, batch_size=100)
        
        logger.info("Script concluído com sucesso!")

if __name__ == "__main__":
    run()
    
