# Índice / Index

- [Seção em Português](#seção-em-português)
- [English Section](#english-section)

# Seção em Português

## Fórmula Global de Rate Loss Time (RLT)

Este documento descreve o esquema de cálculo de Rate Loss Time aplicado no serviço `rate_loss_time_continuous.py`. Os tópicos abaixo reproduzem as regras com seus códigos de condição (3a0, 3b0, etc.) conforme implementado no algoritmo.

### Linhas que usam a lógica "LIST_SCHR"
- `RLN-BISCHMMO3`
- `RLN-BISCHMMO4`
- `RLN-CLKAANAAN`
- `RLN-CLKAASAAS`
- `RLN-FRAEUS3G8`
- `RLN-FRAVAMACH`
- `RLN-FRAEUSCRH`
- `RLN-NANHACHAC`

### Regras Gerais (sempre aplicadas)
- **3a0**: Se `TOTAL_RUNNING_TIME == 0`, então o RLT da hora é 0.
- **3b0**: Se `Net Production == MSDP`, então RLT da hora é 0.

### Regras para linhas fora da LIST_SCHR 
- **3c0**: Cálculo único de RLT. Fórmula:  
  `RLT = { [MSDP * (Running Time / 24)] - Net Production } / (MSDP / 24)`

### Regras aplicadas apenas para linhas na LIST_SCHR
- **3a**: Quando `MSDP > Scheduled Rate` e ambos superam a produção real.  
  - RLT: `((Scheduled Rate * Running Time / 24) - Net Production) / (MSDP / 24)`  
  - RLT No Demand: `[1 - (Scheduled Rate / MSDP)] * Running Time`  
  - Classificação do evento: *Running Under Scheduled Rate* (RLT) e *No Demand* (RLTND).

- **3d**: Se `MSDP == Scheduled Rate > Net Production`.  
  - RLT: `((Scheduled Rate * Running Time / 24) - Net Production) / (MSDP / 24)`  
  - Evento classificado como *Running Under Scheduled Rate*.

- **3e**: Se `MSDP > Scheduled Rate == Net Production`.  
  - RLT No Demand: `[MSDP * Running Time - Scheduled Rate * Running Time] / (MSDP / 24)`  
  - Evento classificado como *No Demand*.

### Regras aplicadas a todas as linhas (quando colunas necessárias estiverem presentes)
- **3b**: Se `MSDP > Net Production > Scheduled Rate`.  
  - RLT No Demand: `Running Time - (24 * Net Production / MSDP)`  
  - Evento classificado como *No Demand*.

- **3c**: Se `Net Production > MSDP`.  
  - RLT: `((MSDP * Running Time / 24) - Net Production) / (MSDP / 24)`  
  - Evento classificado como *Running Above MDR*.

----

# English Section

## Global Rate Loss Time (RLT) Formula

This section explains how the Rate Loss Time is computed in `rate_loss_time_continuous.py`, mapping each rule to its condition code (3a0, 3b0, etc.) exactly as the algorithm enforces.

### Lines that rely on the "LIST_SCHR"
- `RLN-BISCHMMO3`
- `RLN-BISCHMM04`
- `RLN-CLKAANAN`
- `RLN-CLKAASAAS`
- `RLN-FRAEUS3GB`
- `RLN-FRAWMACH`
- `RLN-FRAEUSCRH`
- `RLN-NANHACHAC`

### Global rules (always enforced)
- **3a0**: If `TOTAL_RUNNING_TIME == 0`, Rate Loss Time for that hour is 0.
- **3b0**: If `Net Production == MSDP`, Rate Loss Time for that hour is 0.

### Rules for lines outside LIST_SCHR (only RLN-BAYVAMVAM, RLN-BISCHMPFM, RLN-NANVAMVAM and lines not using Scheduled Rate)
- **3c0**: Single RLT calculation when production is below MSDP. Formula:  
  `RLT = { [MSDP * (Running Time / 24)] - Net Production } / (MSDP / 24)`

### Rules applied only to LIST_SCHR lines
- **3a**: When `MSDP > Scheduled Rate` and both exceed net production.  
  - RLT: `((Scheduled Rate * Running Time / 24) - Net Production) / (MSDP / 24)`  
  - RLT No Demand: `[1 - (Scheduled Rate / MSDP)] * Running Time`  
  - Event contributes as *Running Under Scheduled Rate* (RLT) and *No Demand* (RLTND).

- **3d**: If `MSDP == Scheduled Rate > Net Production`.  
  - RLT: `((Scheduled Rate * Running Time / 24) - Net Production) / (MSDP / 24)`  
  - Event classified as *Running Under Scheduled Rate*.

- **3e**: If `MSDP > Scheduled Rate == Net Production`.  
  - RLT No Demand: `[MSDP * Running Time - Scheduled Rate * Running Time] / (MSDP / 24)`  
  - Event classified as *No Demand*.

### Rules applied to all lines (when columns are available)
- **3b**: If `MSDP > Net Production > Scheduled Rate`.  
  - RLT No Demand: `Running Time - (24 * Net Production / MSDP)`  
  - Event classified as *No Demand*.

- **3c**: If `Net Production > MSDP`.  
  - RLT: `((MSDP * Running Time / 24) - Net Production) / (MSDP / 24)`  
  - Event classified as *Running Above MDR*.

---

> Observação: Após calcular os valores horários em horas, o serviço converte `RLT` e `RLT_NO_DEMAND` para segundos multiplicando por 3600 antes de gerar os eventos diários.

