from typing import List, Optional, Union

import pandas as pd

from app.utils.product_matching_utils import create_product_match_filter


def get_msdp_value(
    row: pd.Series,
    msdp_data: pd.DataFrame,
    data_aux: Union[str, List[str]],
    product_filter: Optional[pd.Series] = None
) -> Union[float, pd.Series]:
    """
    retrieves the msdp from the data stored in the contract

    :param row: row containing the Year and Month
    :type row: pd.Series
    :param msdp_data: DataFrame containing MSDP data
    :type msdp_data: pd.DataFrame  
    :param data_aux: Column name(s) to retrieve
    :type data_aux: str or list[str]
    :param product_filter: Optional product filter condition
    :type product_filter: Optional[pd.Series]
    :return: MSDP value(s)
    :rtype: float or pd.Series
    """

    msdp_data["scheduledRate"] = msdp_data.apply(
        lambda x: x["scheduledRate"]
        if x["scheduledRate"] > 0
        else x["msdp"],
        axis=1,
    )

    # extract filter parameter
    start_time_year = row["Year"]
    start_time_month = row["Month"]
    # Try to get Day value, default to 1 if not available
    if isinstance(row, dict):
        start_time_day = row.get("Day", 1)
    else:
        if "Day" in row:
            start_time_day = row["Day"]
        else:
            start_time_day = 1

    # create reference date to find MSDP
    ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-{start_time_day}")

    # Base filter - filter by year and month
    filter_1 = (msdp_data["year"] == start_time_year) & (
        msdp_data["month"] == start_time_month
    )
    
    # Add day filter if the column exists in msdp_data
    if "day" in msdp_data.columns:
        filter_1 = filter_1 & (msdp_data["day"] == start_time_day)
    
    # Add product filter if provided
    if product_filter is not None:
        filter_1 = filter_1 & product_filter
        
    aux = msdp_data.loc[filter_1, :]

    # if aux is empty, we need to test the second filter
    if aux.shape[0] == 0:
        aux = msdp_data.copy()
        if product_filter is not None:
            aux = aux[product_filter]

        # if still the aux is empty, then we don't have matches, return 0
        if aux.shape[0] == 0:
            return 0 if isinstance(data_aux, str) else pd.Series([0] * len(data_aux))

        # Only consider MSDP data with effective dates on or before the reference date
        aux = aux[pd.to_datetime(aux["effective_date_datetime"]) <= ref_date]
        
        if aux.shape[0] == 0:
            return 0 if isinstance(data_aux, str) else pd.Series([0] * len(data_aux))

        # Find the closest date that is on or before the reference date
        aux.loc[:, "diff_dates"] = (ref_date - pd.to_datetime(aux["effective_date_datetime"])).dt.total_seconds()
        
        # Sort by closest date before reference date (smallest positive difference)
        aux = aux[aux["diff_dates"] >= 0]  # Keep only dates on or before ref_date
        aux.sort_values(by=["diff_dates"], inplace=True, ascending=True)

        aux.ffill(inplace=True)
        aux[data_aux].fillna(0, inplace=True)

    return aux[data_aux].head(1).values[0]

def get_msdp_product_filter(
    row: pd.Series, product: str, msdp_data: pd.DataFrame, data_aux: str
) -> Union[float, pd.Series]:
    """
    retrieves the msdp from the data stored in the contract with product filtering

    :param row: row containing the Year and Month
    :type row: pd.Series
    :param product: product description
    :type product: pd.Series
    :param msdp_data: DataFrame containing MSDP data
    :type msdp_data: pd.DataFrame  
    :param data_aux: Column name(s) to retrieve
    :type data_aux: str or list[str]
    :return: product filter
    :rtype: pd.Series
    """
    if pd.isna(product):
        return pd.array([0] * len(data_aux))

    combined_mask = create_product_match_filter(msdp_data, product)

    if not combined_mask.any():
        product_filter = msdp_data["piTagValue"] == ""
    else:
        product_filter = combined_mask

    return get_msdp_value(row, msdp_data, data_aux, product_filter)
