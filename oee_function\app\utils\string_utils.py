from typing import Dict, List, Optional, Union

import pandas as pd

class StringUtils:
    @staticmethod
    def generate_final_acronyms(status_list: List[str], min_size: int, max_size: int) -> Dict[str, str]:
        """
        EN: Generates unique acronyms from a list of status strings.
        PT-BR: Gera acrônimos únicos a partir de uma lista de strings de status.
        Args:
            status_list (List[str]): List of status strings to generate acronyms from.
            min_size (int): Minimum size of the acronym.
            max_size (int): Maximum size of the acronym.
        Returns:
            Dict[str, str]: A dictionary mapping each status to its unique acronym.
        Raises:
            ValueError: If min_size is greater than max_size.
        """
        acronyms = {}
        used = set()

        def sanitize(word: str) -> str:
            return ''.join(c for c in word if c.isalnum()).upper()

        for status in status_list:
            words = [sanitize(w) for w in status.split()]
            acronym_parts = [word[0] for word in words]

            # PT-BR: Preenche com letras da última palavra se < min_size letras
            # EN: Fills with letters from the last word if acronym_parts has fewer than min_size letters
            if len(acronym_parts) < min_size and words:
                last_word = words[-1]
                needed = min_size - len(acronym_parts)
                extra_letters = list(last_word[1:])
                acronym_parts.extend(extra_letters[:needed])

            acronym = ''.join(acronym_parts)

            # PT-BR: Limita a min_size letras, exceto se tiver max_size+ palavras (pode ter max_size)
            # EN: Limits to min_size letters, except if there are max_size+ words (can have max_size)
            max_len = max_size if len(words) >= max_size else 3
            acronym = acronym[:max_len]

            candidate = acronym
            i = 1
            while candidate in used:
                suffix = chr(65 + i)
                base = acronym[:2]
                candidate = (base + suffix)[:max_len]
                i += 1

            acronyms[status] = candidate
            used.add(candidate)

        return acronyms
