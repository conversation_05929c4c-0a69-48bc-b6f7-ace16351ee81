import timeit
from datetime import time
from typing import Any

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value

from ...tag_value_mapping.wil.wil_product_mapping_service import (
  WilProductMappingService,
)


class WilContinuousEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._day_data = None
        self._rlt_key = "rlt"
        self._rlt_no_demand_key = "rlt_no_demand"

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        start_key = "event1a_start"
        end_key = "event1a_end"

        data[start_key] = data["ProductionLineStatus"] == 0

        data[end_key] = (data["ProductionLineStatus"] > 0) & (
            data[start_key].shift(1) == True
        )

        data[end_key] = (data[end_key]) & (~(data[end_key].shift(1) == True))
        data[start_key] = (data[start_key]) & (
            ~(data[start_key].shift(1) == True)
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        day_data = self.__create_day_data(data)

        day_data = day_data[day_data[self._rlt_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        day_data = self.__create_day_data(data)

        day_data = day_data[day_data[self._rlt_key] < 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        day_data = self.__create_day_data(data)

        day_data = day_data[day_data[self._rlt_no_demand_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_no_demand_key]

        return day_data

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def __fix_missing_production_status_line(
        self, data: pd.DataFrame
    ) -> pd.DataFrame:
        keys = [
            "ProductionLineStatus",
        ]
        return data.assign(**{key: data[key].fillna(0) for key in keys})

    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        if self._day_data is not None:
            return self._day_data.copy()

        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None
            if first_timestamp.time() == mid_night_time
            else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            (
                (start_cutoff is not None)
                & (data["index"].dt.date > start_cutoff)
            )
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        product_list = list(WilProductMappingService()._mapping_dict.values())

        total_produced_key = "total_produced"

        # reset multiindex, timestamp and product become columns
        data = data.reset_index().rename(columns={"index": "timestamp"})

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )

        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_key = "msdp"
        mssr_key = "mssr"
        scheduled_rate_key = "scheduledRate"

        data[[msdp_key, scheduled_rate_key, mssr_key]] = data.apply(
            lambda row: self.get_msdp_value(
                row, msdp_data, [msdp_key, scheduled_rate_key, mssr_key]
            ),
            axis=1,
            result_type="expand",
        )

        data[total_produced_key] = (data["SiftingRate"] / data["mssr"]) * data[
            "msdp"
        ]

        per_hour_to_per_sec_factor = 1 / 3600
        kg_to_ton_factor = 1 / 1000

        data[total_produced_key] = (
            data[total_produced_key]
            * kg_to_ton_factor
            *  # The above code seems to be a comment in a Python script. It
            # mentions a variable or constant named "per_hour_to_per_sec_factor"
            # but does not provide any further information or context about its
            # purpose or usage. Comments are used in code to provide
            # explanations or notes for developers reading the code, but they
            # are not executed as part of the program.
            per_hour_to_per_sec_factor
            * data["dt"]
        )

        all_below_0_key = "all_below_0"
        # product_not_in_list_key = "product_not_in_list"
        at_least_one_above_0_key = "at_least_one_above_0"

        data[all_below_0_key] = data["ProductionLineStatus"] == 0

        # data[product_not_in_list_key] = ~data["Product"].isin(product_list)

        data[at_least_one_above_0_key] = data["ProductionLineStatus"] > 0

        running_time_key = "running_time"

        data[running_time_key] = 0

        sec_to_hour_factor = 1 / 3600

        data.loc[data[at_least_one_above_0_key], running_time_key] = (
            data.loc[data[at_least_one_above_0_key], "dt"] * sec_to_hour_factor
        )

        data = data.groupby(
            [pd.Grouper(key="timestamp", freq="1h"), "Product"], dropna=False
        ).agg(
            {
                all_below_0_key: "all",
                at_least_one_above_0_key: "all",
                total_produced_key: "sum",
                running_time_key: "sum",
            }
        )
        # # reset multiindex, timestamp and product become columns
        data = data.reset_index().rename(columns={"index": "timestamp"})

        # timestamps can be repeated for multiple products (product tag's value during the hour)
        # this gets unique timestamps based on products with the highest total_produced value for that hour
        data = data.loc[data.groupby("timestamp")["total_produced"].idxmax()]

        data_to_get_products_from = data.copy().drop(
            labels=[
                col
                for col in data.columns
                if col not in ["timestamp", "Product"]
            ],
            axis=1,
        )
        # reset multiindex, timestamp and product become columns
        # data = data.reset_index().rename(columns={"index": "timestamp"})
        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )

        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_key = "msdp"
        scheduled_rate_key = "scheduledRate"

        per_day_to_per_hour_factor = 1 / 24

        data[[msdp_key, scheduled_rate_key]] = (
            data.apply(
                lambda row: self.get_msdp_value(
                    row, msdp_data, [msdp_key, scheduled_rate_key]
                ),
                axis=1,
                result_type="expand",
            )
            * per_day_to_per_hour_factor
        )

        rlt_key = self._rlt_key
        rlt_no_demand_key = self._rlt_no_demand_key

        tolerance = 0.001

        rlt_schema = [
            (  # 3a
                "3a",
                lambda df: (df[all_below_0_key]),
                {rlt_key: lambda _: 0},
            ),
            (
                "3b",
                lambda df: (df[total_produced_key] - df[msdp_key]).abs()
                < tolerance,  # produced = msdp
                {rlt_key: lambda _: 0},
            ),
            (
                "3c1",
                lambda df: (
                    (df[total_produced_key] - df[msdp_key]) > tolerance
                )
                & (
                    df[at_least_one_above_0_key]
                ),  # produced > msdp & at least one above 0 during hour
                {
                    rlt_key: lambda df: (df[msdp_key] - df[total_produced_key])
                    / df[msdp_key]
                },
            ),
            (
                "3c2",
                lambda df: (
                    (df[total_produced_key] - df[msdp_key]) > tolerance
                )
                & (
                    df[at_least_one_above_0_key] == False
                ),  # produced > msdp & not at least one above 300 during hour
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] * df[running_time_key])
                        - df[total_produced_key]
                    )
                    / df[msdp_key]
                },
            ),
            (
                "3d1",
                lambda df: (
                    (df[msdp_key] - df[scheduled_rate_key]) > tolerance
                )
                & (
                    (df[scheduled_rate_key] - df[total_produced_key])
                    > tolerance
                )
                & (
                    df[at_least_one_above_0_key]
                ),  # msdp > scheduled & scheduled > produced & at least one above 0 during hour
                {
                    rlt_key: lambda df: (
                        df[scheduled_rate_key] - df[total_produced_key]
                    )
                    / df[scheduled_rate_key],
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df[scheduled_rate_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "3d2",
                lambda df: (
                    (df[msdp_key] - df[scheduled_rate_key]) > tolerance
                )
                & (
                    (df[scheduled_rate_key] - df[total_produced_key])
                    > tolerance
                )
                & (
                    df[at_least_one_above_0_key] == False
                ),  # msdp > scheduled & scheduled > produced & not at least one above 0 during hour
                {
                    rlt_key: lambda df: (
                        (df[scheduled_rate_key] * df[running_time_key])
                        - df[total_produced_key]
                    )
                    / df[scheduled_rate_key],
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * df[running_time_key])
                        - (df[scheduled_rate_key] * df[running_time_key])
                    )
                    / df[msdp_key],
                },
            ),
            (
                "3e1",
                lambda df: (
                    (df[msdp_key] - df[total_produced_key]) > tolerance
                )
                & (
                    (df[total_produced_key] - df[scheduled_rate_key])
                    > tolerance
                )
                & (
                    df[at_least_one_above_0_key]
                ),  # msdp > produced & produced > scheduled & at least one above 0 during hour
                {
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df[total_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "3e2",
                lambda df: (
                    (df[msdp_key] - df[total_produced_key]) > tolerance
                )
                & (
                    (df[total_produced_key] - df[scheduled_rate_key])
                    > tolerance
                )
                & (
                    df[at_least_one_above_0_key] == False
                ),  # msdp > produced & produced > scheduled & not at least one above 0 during hour
                {
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * df[running_time_key])
                        - df[total_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "3f1",
                lambda df: (
                    (df[msdp_key] - df[scheduled_rate_key]).abs() > tolerance
                )
                & (
                    (df[scheduled_rate_key] - df[total_produced_key])
                    > tolerance
                )
                & (
                    df[at_least_one_above_0_key]
                ),  # msdp = produced & scheduled > produced & at least one above 0 during hour
                {
                    rlt_key: lambda df: (df[msdp_key] - df[total_produced_key])
                    / df[msdp_key],
                },
            ),
            (
                "3f2",
                lambda df: (
                    (df[msdp_key] - df[scheduled_rate_key]).abs() > tolerance
                )
                & (
                    (df[scheduled_rate_key] - df[total_produced_key])
                    > tolerance
                )
                & (
                    df[at_least_one_above_0_key] == False
                ),  # msdp = produced & scheduled > produced & not at least one above 0 during hour
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] * df[running_time_key])
                        - df[total_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
        ]

        data[rlt_key] = pd.Series(dtype="float")
        data[rlt_no_demand_key] = pd.Series(dtype="float")

        condition_key = "rlt_condition"
        data[condition_key] = pd.NA

        for (
            rlt_condition_code,
            get_filter_mask,
            target_col_to_calc_func_map,
        ) in rlt_schema:
            filter_mask = (
                get_filter_mask(data)
                & data[rlt_key].isna()
                & data[rlt_no_demand_key].isna()
            )
            for target_col, calc_func in target_col_to_calc_func_map.items():
                data.loc[filter_mask, target_col] = calc_func(
                    data.loc[filter_mask]
                )
                data.loc[filter_mask, condition_key] = rlt_condition_code

        data[rlt_key] = data[rlt_key].fillna(0)
        data[rlt_no_demand_key] = data[rlt_no_demand_key].fillna(0)

        data = (
            data.groupby(
                pd.Grouper(key="timestamp", freq="D"),
            )
            .agg(
                {
                    msdp_key: "sum",
                    scheduled_rate_key: "sum",
                    total_produced_key: "sum",
                    rlt_key: "sum",
                    rlt_no_demand_key: "sum",
                    running_time_key: "sum",
                },
            )
            .reset_index()
        )

        # no events should be generated if total_produced <= 0
        data = data[data[total_produced_key] > 0]

        data["start_time"] = data["timestamp"]
        data["end_time"] = data["timestamp"] + pd.Timedelta(days=1)

        if data.shape[0] == 0:
            self._day_data = data
            return data.copy()

        # get products reported by tag on start_time
        data = data.merge(
            data_to_get_products_from,
            how="left",
            left_on="start_time",
            right_on="timestamp",
            suffixes=(None, "_to_exclude"),
        )

        data.loc[(~data["Product"].isin(product_list)), "Product"] = pd.NA

        # get msdp and scheduled rate on start_time
        data["Year"] = data["start_time"].dt.year
        data["Month"] = data["start_time"].dt.month
        data["Day"] = data["start_time"].dt.day

        data[
            [
                msdp_key,
                scheduled_rate_key,
            ]
        ] = data.apply(
            lambda row: self.get_msdp_value(
                row, msdp_data, [msdp_key, scheduled_rate_key]
            ),
            axis=1,
            result_type="expand",
        )

        msdp_eq_scheduled_rate_eq_total_produced = (
            ((data[total_produced_key] - data[msdp_key]).abs() < tolerance)
            & (
                (data[total_produced_key] - data[scheduled_rate_key]).abs()
                < tolerance
            )
            & ((data[msdp_key] - data[scheduled_rate_key]).abs() < tolerance)
        )

        # no events should be generated if msdp = scheduled rate = total_produced
        data = data[~msdp_eq_scheduled_rate_eq_total_produced]

        data = data.rename(columns={"timestamp": "index"}).drop(
            columns=["Product"]
        )

        hours_to_seconds_factor = 3600
        data[rlt_key] = data[rlt_key] * hours_to_seconds_factor
        data[rlt_no_demand_key] = (
            data[rlt_no_demand_key] * hours_to_seconds_factor
        )

        self._day_data = data

        return data.copy()

    def get_msdp_value(self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: list[str]) -> float:
        """
        retrieves the msdp from the data stored in the contract
        """
        return get_msdp_value(row, msdp_data, data_aux)
