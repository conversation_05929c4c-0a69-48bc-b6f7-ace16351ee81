from typing import Any, Optional
from datetime import time

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.utils.constants import Constants as const
from app.utils.event_detection_utils import pair_start_end_indexes

class NanHacEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        
        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)
        
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus_1 = Closed & ProductionLineStatus_2 = Closed & TotalFeed < 0
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus_1"] == 0)
                & (data["ProductionLineStatus_2"] == 0)
                & (data["TotalFeed"] < 1)
            )
        )

        # event end - ProductionLineStatus_1 = Open & ProductionLineStatus_2 = Open & TotalFeed > 0
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus_1"] == 1)
                & (data["ProductionLineStatus_2"] == 1)
                & (data["TotalFeed"] > 1)
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        )

        data = data.assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus_1 = Open & ProductionLineStatus_2 = Open & TotalFeed > 0
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus_1"] == 1)
                & (data["ProductionLineStatus_2"] == 1)
                & (data["TotalFeed"] > 1)
            )
        )

        # event end - ProductionLineStatus_3 < 950
        data = data.assign(
            event2a_end=(
                (data["ProductionLineStatus_3"] < 950)
                & (data["ProductionLineStatus_3"].shift(1) >= 950)
            )
        )

        # correct start and end flags
        data = data.assign(
            event2a_start=(
                (data["event2a_start"] == True)
                & (data["event2a_start"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE])

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]

        return day_data
    
    def identify_events_typeIIIb(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE])

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data
    
    def identify_events_typeIIIc(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE])

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data
    
    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced
        
        df["TotalMass"] = (df["TotalFeed"] / 3600) * df["dt"]
        net_production = (df["TotalMass"]/0.53)
        
        self._total_produced = net_production

        return net_production
    
    def not_running_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._not_running.empty:
            return self._not_running
        
        df_work = df.copy()

        df_work["not_running"] = (
            df_work["ProductionLineStatus_1"] == 0
            & (df_work["ProductionLineStatus_2"] == 0)
            & (df_work["TotalFeed"] < 1)
        )
        
        df_work["starting_up_start"] = (
            (df_work["ProductionLineStatus_1"] == 1)
            & (df_work["ProductionLineStatus_2"] == 1)
            & (df_work["TotalFeed"] >= 1)
        )
        
        df_work["starting_up_start"] = (
            (df_work["starting_up_start"] == True)
            & (df_work["starting_up_start"].shift(1) != True)
        )
        
        df_work["starting_up_end"] = (
            (df_work["ProductionLineStatus_3"] < 950)
            & (df_work["ProductionLineStatus_3"].shift(1) >= 950)
        )
        
        # Filter to only include relevant rows for processing
        mask = df_work["not_running"] | df_work["starting_up_start"]
        df_filtered = df_work.loc[mask].copy()
        
        # Ensure starting_up only triggers after not_running
        df_filtered["starting_up_start"] = (
            df_filtered["starting_up_start"] & df_filtered["not_running"].shift(1).fillna(False)
        )
        
        # Update the original dataframe with the corrected starting_up_start values
        df_work.loc[mask, "starting_up_start"] = df_filtered["starting_up_start"]
        
        df_work["starting_up"] = False
        
        pair_start_end_indexes(df_work, "starting_up_start", "starting_up_end", "starting_up", True)

        not_running = (
            df_work["not_running"] | df_work["starting_up"]
        )

        self._not_running = not_running

        return not_running