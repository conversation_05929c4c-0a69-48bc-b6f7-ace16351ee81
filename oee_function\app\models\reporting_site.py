from typing import Any, Optional

from pydantic import Field

from .country import Country
from .node import Node
from .reporting_line import ReportingLine
from .reporting_unit import ReportingUnit
from .time_zone import TimeZone


class ReportingSite(Node):
    name: Optional[str] = Field(None)
    reporting_units: Optional[list[ReportingUnit]] = Field(
        default=None, alias="reportingUnits"
    )
    reporting_lines: Optional[list[ReportingLine]] = Field(
        default=None, alias="reportingLines"
    )

    time_zone: Optional[TimeZone] = Field(default=None, alias="timeZone")
    country: Optional[Country] = Field(default=None, alias="country")
    site_code: Optional[str] = Field(default=None, alias="siteCode")

    @classmethod
    def from_cognite_response(cls, item: dict[str, Any]) -> "ReportingSite":
        return ReportingSite(
            externalId=item["externalId"],
            space=item["space"],
            reportingUnits=[
                ReportingUnit(**entry)
                for entry in item["reportingUnits"]["items"]
                if entry
            ]
            if item["reportingUnits"]
            else [],
            reportingLines=[
                ReportingLine(**entry)
                for entry in item["reportingLines"]["items"]
                if entry
            ]
            if item["reportingLines"]
            else [],
            timeZone=TimeZone(**item["timeZone"])
            if item.get("timeZone")
            else None,
            country=Country(**item["country"])
            if item.get("country")
            else None,
            siteCode=item["siteCode"],
        )
