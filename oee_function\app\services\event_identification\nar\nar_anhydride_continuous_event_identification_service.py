from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value
from app.infra.logger_adapter import get_logger
log = get_logger()


class NarAnhydrideContinuousEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - ProductionLineStatus = 0
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] == 0)
                & (data["ProductionLineStatus"].shift(1) != 0)
            )
        )

        # event end - ProductionLineStatus = 'RUNNING' (1)
        data = data.assign(
            event1a_end=(
                (
                    (data["ProductionLineStatus"] == 1)
                    & (data["ProductionLineStatus"].shift(1) != 1)
                )
            )
        )


        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        if self._msdp is None:
            log.info("Missing MSDP")
            return pd.DataFrame()

        day_data = self.__create_day_data(data)

        # identifying events of 3a (also 3b) type
        day_data = day_data.assign(
            event3a_end=(day_data["total_duration_seconds"] > 0)
        )

        day_data = day_data.assign(
            event3a_start=(day_data["event3a_end"].shift(-1) == True)
        )

        day_data.rename(columns={"start_time": "index"}, inplace=True)

        return day_data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        if self._msdp is None:
            log.info("Missing MSDP")
            return pd.DataFrame()

        day_data = self.__create_day_data(data)

        # identifying events of 3b
        day_data = day_data.assign(
            event3b_end=(day_data["total_duration_seconds"] < 0)
        )

        day_data = day_data.assign(
            event3b_start=(day_data["event3b_end"].shift(-1) == True)
        )

        day_data.rename(columns={"start_time": "index"}, inplace=True)

        return day_data

    def identify_events_typeIIIc(self):
        pass

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data_rlt = (
            data.copy()
            .rename(columns={"index": "timestamp"})
            .assign(
                # fillna(0) for variable with high frequency update
                TotalFeed1=lambda x: x["TotalFeed1"].fillna(0)
            )
            .assign(
                TotalFeed2=lambda x: x["TotalFeed2"]
                .ffill()
                .fillna(0)  # Forward fill for variable with low
                # frequency the update the and fillna(0) by stability
            )
            .assign(
                delta_t=lambda x: x.timestamp.diff(-1)
                .dt.total_seconds()
                .abs()
                .fillna(0)
            )
            .assign(
                net_production=lambda x: (
                    (x["TotalFeed1"] * x["TotalFeed2"]) * x["delta_t"] / 3600
                )  # calculating the pre-aggregation derivative
            )
            .assign(
                # Corrigindo o ProductionLineStatus. Depois, adicionar isso onde for pertinente no código.
                ProductionLineStatus=lambda x: x["ProductionLineStatus"]
                .ffill()
                .fillna(0)
            )
        )

        prod_day_data = (
            data_rlt.copy()  # calculate the value produced at the last interval
        )

        prod_day_data = (
            prod_day_data[["timestamp", "net_production"]]
            .groupby(pd.Grouper(key="timestamp", freq="1h"))
            .agg(
                {
                    "net_production": "sum",
                }
            )
        )
        prod_day_data["net_production"] = (
            prod_day_data["net_production"] / 1000
        )
        prod_day_data.reset_index(inplace=True)
        # find the year that is in accordance with the indexed date
        prod_day_data["Year"] = prod_day_data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        prod_day_data["Month"] = prod_day_data["timestamp"].dt.month
        # low performance
        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )

        msdp_data = self._msdp.loc[filter_msdp, :]

        prod_day_data["msdp"] = prod_day_data.apply(
            self.get_msdp_value, msdp_data=msdp_data, data_aux="msdp", axis=1
        )

        data_with_rlt = self.__implement_RLT(data=prod_day_data)

        start_time = data_with_rlt.iloc[0, :]["start_time"]
        morning = pd.Timestamp(
            start_time.strftime("%Y-%m-%d") + " 12:00:00",
            tz=start_time.tz,
        ) - pd.DateOffset(days=1)
        evening = pd.Timestamp(
            start_time.strftime("%Y-%m-%d") + " 12:00:00", tz=start_time.tz
        )
        if start_time < morning:
            start_range = morning - pd.DateOffset(hours=24)
        elif (start_time >= morning) & (start_time < evening):
            start_range = morning
        else:
            start_range = evening

        # get the nearest shift start concerning the last timestamp of
        # the hourly data_with_rlt
        end_time = data_with_rlt.iloc[-1, :]["end_time"]
        morning = pd.Timestamp(
            start_time.strftime("%Y-%m-%d") + " 12:00:00", tz=end_time.tz
        ) - pd.DateOffset(days=1)
        evening = pd.Timestamp(
            end_time.strftime("%Y-%m-%d") + " 18:00:00", tz=end_time.tz
        )

        if end_time < morning:
            end_range = morning
        elif (end_time >= morning) & (end_time < evening):
            end_range = evening
        else:
            end_range = evening + pd.DateOffset(hours=24)

        # create all working shifts dataframes
        ws_events = pd.DataFrame(
            pd.date_range(
                start=start_range,
                end=end_range - pd.DateOffset(hours=24),
                freq="24H",
            ),
            columns=["start_time"],
        )
        ws_events["end_time"] = pd.date_range(
            start=start_range + pd.DateOffset(hours=24),
            end=end_range,
            freq="24H",
        )

        # aggregate hourly data into working shifts
        ws_events["total_duration_seconds"] = ws_events.apply(
            self.__aggregate_RLT, hourly_data=data_with_rlt, axis=1
        )

        # concatenate events frames and new events
        # data_concat = pd.concat([data, ws_events])

        # sort data
        ws_events.sort_values(by=["end_time"], inplace=True)

        return ws_events

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        return msdp_util_get_msdp_value(row, msdp_data, data_aux)

    def __calculate_RLT_time_duration(self, row: pd.DataFrame) -> float:
        return self.__RLT_formula(row, "1")
    
    

    def __implement_RLT(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        calculates the total duration of the eventIII and eventIV (Rate Loss Time calculated in hours, but converted for seconds)

        :param data: row dataframe with the start and the end times of each events
        :type data: pd.DataFrame
        :return: dataframe containing a column with the total duration of each event
        :rtype: pd.DataFrame
        """

        data["RLT"] = data.apply(self.__calculate_RLT_time_duration, axis=1)
        
        def adjust_end_time(ts):
            try:
                # Adiciona uma hora ao timestamp
                end_time = ts + pd.DateOffset(hours=1)
            except ValueError as e:
                # Lida com horário inexistente devido ao horário de verão
                end_time = ts + pd.DateOffset(hours=1)
                if ts.tzinfo is not None:
                    end_time = end_time.tz_convert(ts.tzinfo)
            return end_time

        hourly_data = (
            data.copy()
            .rename(
                {"timestamp": "start_time", "RLT": "rate_loss_time_h"},
                axis=1,
            )
            .assign(
                end_time=lambda x: x["start_time"].apply(adjust_end_time)
            )
        )

        return hourly_data

    def __RLT_formula(self, row: pd.Series, subtype: str) -> float:
        """
        Helper method to fetch the correct factor, based
        on the calculation method for each event subtype.

        Args:
            row (pd.Series): row from an apply statement.
            factor (str): an identifier for which subtype we're aiming at.

        Returns:
            str: Calculated Hourly Rate Loss Time
        """

        # Selects how to calculate the factor, based upon the event subtype.
        factor = {
            "1": lambda x: (
                (row["msdp"] / 2 - row["net_production"]) / (row["msdp"] / 48)
            ),
        }[subtype](row)

        return factor

    def __aggregate_RLT(
        self, row: pd.DataFrame, hourly_data: pd.DataFrame
    ) -> float:
        """
        aggregates the rate loss time for the entire working shift

        :param row: row with the start and end time of the working shifts
        :type row: pd.DataFrame
        :param hourly_data: data with hourly rate loss time
        :type hourly_data: pd.DataFrame
        :return: aggregation of the rate loss time for each working shift
        :rtype: float
        """

        # get start and end of the shift
        start = row["start_time"]
        end = row["end_time"]

        # filter the hourly data and sum the rate loss time
        rlt = hourly_data.loc[
            (
                (hourly_data["start_time"] >= start)
                & (hourly_data["end_time"] <= end)
            ),
            "rate_loss_time_h",
        ].sum()

        return rlt * 3600
