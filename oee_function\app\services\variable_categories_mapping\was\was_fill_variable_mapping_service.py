class WASFillVariableMappingService:
    
    _map = {
        "RLN-WASFIL2SP": {
            "ExtruderScrewSpeedActual": 35,
            "ExtruderActualLoad": 35,
            "ClutchDrawRatio": 3.7,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.0922,
        }, "RLN-WASFIL3SP": {
            "ExtruderScrewSpeedActual": 40,
            "ExtruderActualLoad": 1,
            "ClutchDrawRatio": 3.7,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.0711,
        }, "RLN-WASFIL4SP": {
            "ExtruderScrewSpeedActual": 40,
            "ExtruderActualLoad": 1,
            "ClutchDrawRatio": 3.7,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.0886,
        }, "RLN-WASFIL5SP": {
            "ExtruderScrewSpeedActual": 35,
            "ExtruderActualLoad": 1,
            "ClutchDrawRatio": 3.65,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.0899,
        }, "RLN-WASFIL6SP": {
            "ExtruderScrewSpeedActual": 35,
            "ExtruderActualLoad": 35,
            "ClutchDrawRatio": 3.7,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.0743,
        }, "RLN-WASFIL7SP": {
            "ExtruderScrewSpeedActual": 35,
            "ExtruderActualLoad": 1,
            "ClutchDrawRatio": 3.7,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.0732,
        }, "RLN-WASFIL8SP": {
            "ExtruderScrewSpeedActual": 100,
            "ExtruderActualLoad": 75,
            "ClutchDrawRatio": 3.7,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.047,
        }, "RLN-WASFIL10SP": {
            "ExtruderScrewSpeedActual": 40,
            "ExtruderActualLoad": 1,
            "ClutchDrawRatio": 2.05,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.1366,
        }, "RLN-WASFIL11SP": {
            "ExtruderScrewSpeedActual": 35,
            "ExtruderActualLoad": 2,
            "ClutchDrawRatio": 2.05,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.1366,
        }, "RLN-WASFIL12SP": {
            "ExtruderScrewSpeedActual": 20,
            "ExtruderActualLoad": 30,
            "ClutchDrawRatio": 3.7,
            "FastRollSpeedActual(fpm)": 0,
            "conversionFactor": 0.055,
        },
    }

    @classmethod
    def get(cls, linha: str, name: str) -> float:
       
        if linha not in cls._map:
            linhas_disponiveis = list(cls.threshold_map.keys())
            raise ValueError(
                f"Line '{linha}' not found. "
                f"Available lines: {linhas_disponiveis}"
            )

        inner = cls._map[linha]

        if name not in inner:
            series_disponiveis = list(inner.keys())
            raise ValueError(
                f"Série '{name}' not found for line '{linha}'. "
                f"Séries available for '{linha}': {series_disponiveis}"
            )

        return inner[name]
