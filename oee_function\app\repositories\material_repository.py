from ast import Dict
from typing import Optional

import pandas as pd
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.data_models import DataModelId

from ..models.reporting_site import ReportingSite
from ..utils.graphql import generate_query, query_all
from ..utils.list import divide_in_chunks

PRODUCT_MATERIAL_DICT = {
    "M_50002030": "VAM",
    "M_50000437": "Ac20",
    "M_50000455": "EtAc",
    "M_50000444": "MO",
    "M_50000483": "MAS",
    "M_50000501": "AA",
    "M_50001481": "Methanol",
    "M_50000884": "3G8",
    "M_50000429": "CrH",
    "M_50000873": "DBM",
    "M_50000882": "Poly O",
    "M_51001869": "BAHIA",
}


class MaterialRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        data_model_id: DataModelId,
    ) -> None:
        self._cognite_client = cognite_client
        self._data_model_id = data_model_id
        self._fixed_product_material_data = None

    def get_oee_material_df(self, event_frame: pd.DataFrame) -> pd.DataFrame:
        list_name = "listMaterial"
        name_to_product_map: Dict[str, dict[str, str]] = {}
        pairs = list(
            set(
                zip(
                    event_frame["Product"],
                    event_frame["ProductDescription"],
                )
            )
        )

        if not self._fixed_product_material_data:
            self._fixed_product_material_data = {}
            filter_ = {"externalId": {"in": list(PRODUCT_MATERIAL_DICT.keys())}}
            materials = self._call_query(list_name, filter_)
            for material in materials:
                material["alias"] = PRODUCT_MATERIAL_DICT[material["externalId"]]
                self._fixed_product_material_data.update(
                    {material["externalId"]: material}
                )
        for pair_chunk in divide_in_chunks(pairs, 1000):
            product_names = [n for n, _ in pair_chunk if pd.notna(n)]
            product_descriptions = [d for _, d in pair_chunk if pd.notna(d)]

            filter_ = {
                "or": [
                    {"name": {"in": product_names}},
                    {"description": {"in": product_descriptions}},
                ]
            }

            query_result = self._call_query(list_name, filter_)

            if len(query_result) > 0:
                for item in query_result:
                    name_to_product_map.update({item["externalId"]: {**item, "alias": item["name"] if item["name"] in product_names else item["description"]}})

        if self._fixed_product_material_data:
            name_to_product_map.update(self._fixed_product_material_data)

        return self._convert_material_dicts_to_df(list(name_to_product_map.values()))

    @staticmethod
    def _convert_material_dicts_to_df(dicts: list[dict]) -> pd.DataFrame:
        if len(dicts) > 0:
            return pd.DataFrame.from_records(dicts)
        return pd.DataFrame(columns=["externalId", "space", "name", "alias", "description"])

    @staticmethod
    def _build_material_query_selection():
        return """
                externalId
                space
                name
                description
            """

    def _call_query(self, list_name: str, filter_):
        return query_all(
            client=self._cognite_client,
            data_model_id=self._data_model_id,
            list_name=list_name,
            query=generate_query(list_name, self._build_material_query_selection()),
            filter=filter_,
        )
