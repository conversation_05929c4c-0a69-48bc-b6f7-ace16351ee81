from typing import Any

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.services.riser_product_event_service import RiserProductEventService
from app.utils.constants import Constants as const
from app.utils.event_detection_utils import detect_sustained_transition

class BisContinuousEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._day_data_riser = None

        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)

        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp,
            multi_product=True
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - ProductionLineStatus < 5 for five minutes
        data = data.assign(
            event1a_start=(
                detect_sustained_transition(
                    df=data,
                    col="ProductionLineStatus",
                    threshold=5,
                    duration="5min",
                    condition="lt"
                )
            )
        )

        # event trigger end - ProductionLineStatus > 5 for five minutes
        data = data.assign(
            event1a_end=(
                detect_sustained_transition(
                    df=data,
                    col="ProductionLineStatus",
                    threshold=5,
                    duration="5min",
                    condition="gt"
                )
            )
        )

        data.reset_index(inplace=True, drop=False)

        return data

    def identify_events_typeIb(self):
        pass
    
    def identify_events_typeIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """
        df_events = self.__create_day_data_riser(data)

        return df_events[df_events["rlt"] > 0].reset_index(drop=True)
    
    def identify_events_typeIIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """
        df_events = self.__create_day_data_riser(data)

        return df_events[df_events["rlt"] < 0].reset_index(drop=True)

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args):
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE], shift_value=1)

        if day_data.empty:
            return day_data


        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]
        return day_data


    def identify_events_typeIIIb(self, data: pd.DataFrame, **args):
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE], shift_value=1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]
        return day_data

    def identify_events_typeIIIc(self, data: pd.DataFrame, **args):
        """
        identifies events of type IIIc

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE], shift_value=1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]
        return day_data

    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced

        MLB_TO_LB_MULTIPLICATION_FACTOR = 1000
        PER_HOUR_TO_PER_SEC_FACTOR = 1 / 3600

        net_production = (
            (
                df["ProductionLineStatus"]
                * PER_HOUR_TO_PER_SEC_FACTOR
                * df["dt"]
            )
            * 0.69
            / 2000
            * MLB_TO_LB_MULTIPLICATION_FACTOR
        )

        self._total_produced = net_production

        return net_production
    
    def __create_day_data_riser(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 2a and 2b

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type II included
        :rtype: pd.DataFrame
        """
        if self._day_data_riser is not None:
            return self._day_data_riser.copy()

        riser_product_names = ["RISER"]
        riser_service = RiserProductEventService(
            msdp=self._msdp,
            reporting_line_external_id=self._reporting_line_external_id,
            net_production_fn=self.calculate_net_production_fn
        )

        data["event2ab_start"] = (
            (data["ProductionLineStatus"] > 5)
            & (data[const.PRODUCT_DESCRIPTION].isin(riser_product_names))
        )

        data["event2ab_start"] = data["event2ab_start"] & (~data["event2ab_start"].shift(1).fillna(False))

        data["event2ab_end"] = (
            (data["ProductionLineStatus"] < 5)
            | ((data[const.PRODUCT_DESCRIPTION].shift(1).isin(riser_product_names))
            & (~data[const.PRODUCT_DESCRIPTION].isin(riser_product_names)))
        )

        data["event2ab_end"] = data["event2ab_end"] & (~data["event2ab_end"].shift(1).fillna(False))

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~self.not_running_fn(data))
        )

        df_events = riser_service.get_rlt_and_msdp_riser_event_blocks(data, riser_product_names)

        if df_events.empty:
            return df_events

        df_events[const.PRODUCT_DESCRIPTION] = "RISER"

        self._day_data_riser = df_events

        return df_events

    def not_running_fn(self, data: pd.DataFrame) -> pd.DataFrame:
        if not self._not_running.empty:
            return self._not_running

        # Modified running_time calculation
        not_running_condition = (data["ProductionLineStatus"] < 5)

        self._not_running = not_running_condition

        return not_running_condition