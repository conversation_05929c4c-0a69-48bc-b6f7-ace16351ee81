from datetime import time
from typing import Any, List, Union

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value
from app.utils.product_matching_utils import create_product_match_filter

from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.utils.uom_conversion import lb_to_mt
from app.utils.constants import Constants as const


class NanGurEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._day_data = None
        
        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp,
            multi_product=True,
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        start_key = "event1a_start"
        end_key = "event1a_end"
        
        data["not_running"] = self._not_running_fn(data)

        data[start_key] = (
            (data["not_running"])
            & (~data["Product"].astype(str).str.startswith("X"))
        )

        data[end_key] = (
            (data["not_running"] == False)
            | (data["Product"].astype(str).str.startswith("X"))
        )

        data[start_key] = (data[start_key]) & (data[start_key].shift(1) != True)
        data[end_key] = (data[end_key]) & (data[end_key].shift(1) != True)

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        data = data.assign(
            event2a_start=(
                (data["TotalFeed"] > 500)
                & (data["Product"].astype(str).str.startswith("X"))
            )
        )

        data = data.assign(
            event2a_end=(
                (
                    (data["TotalFeed"].shift(1) > 500)
                    & (data["TotalFeed"] < 500)
                ) 
                | (~data["Product"].astype(str).str.startswith("X"))
            )
        )

        # fix event trigger start
        data["event2a_start"] = (
            data["event2a_start"] & (data["event2a_start"].shift(1) != True)
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        
        data["not_running"] = self._not_running_fn(data)
        
        data["event2c_start"] = (
            (data["not_running"])
            & (data["Product"].astype(str).str.startswith("X"))
        )

        data["event2c_end"] = (
            (
                (~data["not_running"])
                & (data["event2c_start"].shift(1) == True)
            )
            | (
                (~data["not_running"])
                & (data["not_running"].shift(1) == True)
                & (~data["Product"].astype(str).str.startswith("X"))
            )
            | (
                (~data["Product"].astype(str).str.startswith("X"))
                & (data["Product"] != data["Product"].shift(1))
            )
        )

        data = data.assign(
            event2c_start=(
                (
                    (data["not_running"])
                    & (data["Product"].astype(str).str.startswith("X"))
                )
                & (data["event2c_start"].shift(1) != True)
            )
        )

        data = data[
            (data["event2c_start"] == True) | (data["event2c_end"] == True)
        ]

        data = data.assign(
            event2c_start=(
                (data["event2c_start"] == True)
                & (data["Product"] == data["Product"].shift(-1))
            )
        )
        
        data = data.assign(
            event2c_end=(
                (data["event2c_end"] == True)
                & (data["Product"] == data["Product"].shift(1))
            )
        )
        
        return data

    def identify_events_typeIId(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IId

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        
        data["not_running"] = self._not_running_fn(data)
        
        data["event2d_start"] = (
            (data["not_running"])
            & (data["Product"].astype(str).str.startswith("X"))
        )

        data["event2d_end"] = (
            (
                (~data["not_running"])
                & (data["event2d_start"].shift(1) == True)
            )
            | (
                (~data["not_running"])
                & (data["not_running"].shift(1) == True)
                & (~data["Product"].astype(str).str.startswith("X"))
            )
            | (
                (~data["Product"].astype(str).str.startswith("X"))
                & (data["Product"] != data["Product"].shift(1))
            )
        )

        data = data.assign(
            event2d_start=(
                (
                    (data["not_running"])
                    & (data["Product"].astype(str).str.startswith("X"))
                )
                & (data["event2d_start"].shift(1) != True)
            )
        )

        data = data[
            (data["event2d_start"] == True) | (data["event2d_end"] == True)
        ]

        data = data.assign(
            event2d_start=(
                (data["event2d_start"] == True)
                & (data["Product"] != data["Product"].shift(-1))
                & (data["Product"].shift(-1).notna())
            )
        )
        
        data = data.assign(
            event2d_end=(
                (data["event2d_end"] == True)
                & (data["Product"] != data["Product"].shift(1))
            )
        )
        
        return data

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self._not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE], 0)

        day_data = day_data[(day_data[const.RLT] > 0) & day_data[const.RLT].apply(lambda x: x != float('inf'))]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIIIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self._not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE], 0)

        day_data = day_data[(day_data[const.RLT] < 0) & day_data[const.RLT].apply(lambda x: x != float('-inf'))]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIIIc(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self._not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE], 0)

        day_data = day_data[(day_data[const.RLT_NO_DEMAND] > 0) & day_data[const.RLT_NO_DEMAND].apply(lambda x: x != float('inf'))]

        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]

        return day_data

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def _not_running_fn(self, df: pd.DataFrame) -> pd.Series:
        return (df["ProductionLineStatus_1"] + df["ProductionLineStatus_2"]) <= 500
    
    
    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced
        
        net_production = df["TotalFeed"] * 0.985
        
        per_hour_to_per_sec_factor = 1 / 3600
        kg_to_ton_factor = 1 / 1000

        net_production = net_production * kg_to_ton_factor * per_hour_to_per_sec_factor * df["dt"]
        
        self._total_produced = net_production

        return net_production
