from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as utils_get_msdp_value
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.utils.constants import Constants as const
from app.utils.uom_conversion import lb_to_mt
from app.utils.event_detection_utils import detect_sustained_transition

class BAYVAMEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)
        
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIb,
            "3b": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - ProductionLineStatus < 140
        data = data.assign(event1a_start=(data["ProductionLineStatus"] < 140))

        # event trigger end - ProductionLineStatus > 140
        data = data.assign(event1a_end=(data["ProductionLineStatus"] > 140))

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        ).assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """
        
        data["under_140_five_minutes"] = detect_sustained_transition(df=data, col="ProductionLineStatus", threshold=140, duration="5min", condition="lt")

        data.reset_index(inplace=True, drop=False)

        data["dt"] = data["dt"] / 60
        
        # Counts the duration of the ProductionLineStatus < 140
        data["ProductionLineStatus_duration"] = data.groupby(
            (
                (data["ProductionLineStatus"].shift(1) >= 140)
                & (data["ProductionLineStatus"] < 140)
            ).cumsum()
        )["dt"].cumsum()
        data.loc[data["ProductionLineStatus"] >= 140, "ProductionLineStatus_duration"] = 0

        # event trigger start - ProductionLineStatus goes from < 140 for 5 minutes to >= 140 and ValveOutput <= 0
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus_duration"].shift(1) >= 5)
                & (data["ProductionLineStatus"] >= 140)
                & (data["ValveOutput"] <= 0)
            )
        )
        
        # event trigger end - ProductionlineStatus > 140 and ValveOutput > 0 or ProductionLineStatus goes from >= 140 to < 140 for 5 minutes
        data = data.assign(
            event2a_end=(
                (
                    (data["ProductionLineStatus"] > 140)
                    & (data["ValveOutput"] > 0)
                )
                | (
                    data["under_140_five_minutes"] == True # Not Running starts
                )
            )
        )
        
        data = data.assign(
            event2a_end=(
                (data["event2a_end"] == True)
                & (data["event2a_end"].shift(1) != True)
            )
        )
        
        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        identification_columns = ["NetProduction", "ProductionLineStatus"]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )
        
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)
        
        day_data = self._rlt.create_day_data(data, "first", [const.MSDP, const.SCHEDULED_RATE], shift_value=1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        identification_columns = ["NetProduction", "ProductionLineStatus"]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )

        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)
        
        day_data = self._rlt.create_day_data(data, "first", [const.MSDP, const.SCHEDULED_RATE], shift_value=1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def __remove_unecessary_data(
        self, data: pd.DataFrame, columns_to_keep: list[str]
    ) -> pd.DataFrame:
        remove_columns = [
            col
            for col in data.columns.to_list()
            if col not in columns_to_keep + ["index"]
        ]
        return data.drop(remove_columns, axis=1).dropna(
            subset=columns_to_keep, how="all"
        )

    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced

        net_production = lb_to_mt(df["NetProduction"])

        self._total_produced = net_production

        return net_production

    def not_running_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._not_running.empty:
            return self._not_running
        
        not_running = df["ProductionLineStatus"] < 140
        
        self._not_running = not_running
        
        return not_running