import pandas as pd


class WinCmpVariableCategoriesMappingService:
    def __init__(self) -> None:
        # Define common values as variables
        self._running = "Running"
        self._general_crash = "General Crash"
        self._human_factor = "Human Factor"
        self._not_scheduled_to_run = "Not Scheduled to Run"
        self._planned_maintenance = "Planned Maintenance"
        self._planned_downtime = "Planned Downtime"
        self._transition = "Transition"
        self._manpower = "Manpower"
        self._lack_of_raw_materials = "Lack of Raw Materials"
        self._line_electrical_controls = "Line Electrical and Controls"
        self._tension_bars = "Tension Bars"
        self._creel_rack = "Creel Rack"
        self._feeding_system = "Feeding System"
        self._die = "Die"
        self._extruder = "Extruder"
        self._strand_cooling = "Strand Cooling"
        self._puller = "Puller"
        self._pelletizer = "Pelletizer"
        self._air_convey = "Air Convey"
        self._dust_collection = "Dust Collection"
        self._classifier_screener = "Classifier / Screener"
        self._tape_specific = "Tape Specific"
        self._producing_waste = "Producing Waste"
        self._scheduled_maintenance = "Scheduled Maintenance"
        self._opportunity_maintenance = "Oppurtunity Maintenance"
        self._weather = "Weather"
        self._no_demand = "No Demand"
        self._reactive_downtime = "Reactive Downtime"
        self._product_supply_optimization = "Product & Supply Optimization"
        self._scrap = "Scrap"
        self._availability = "Availability"
        self._loading = "Loading"
        self._quality = "Quality"
        self._fiber_handling_equipment = "Fiber Handling Equipment"
        self._whole_line_pm = "Whole Line PM"
        self._external_raw_material = "External Raw Material"
        self._internal_utilities = "Internal Utilities"
        self._quench_cutter_water_removal = "Quench / Cutter / Water Removal"
        self._raw_material_conveying = "Raw Material Conveying"
        self._finished_product_conveying = "Finished Product Conveying"
        self._tape_handling_equipment = "Tape Handling Equipment"
        self._human_error = "Human Error"
        self._bad_fiber = "Bad Fiber"
        self._control_logic = "Control Logic"
        self._electrical = "Electrical"
        self._daybin_dryer_convey = "Daybin / Dryer Convey"
        self._screener = "Screener"
        self._tape_control = "Tape Controls"
        self._winder = "Winder"
        self._accumulator = "Accumulator"
        
        self._mapping_subcatlevel2_dict = {
            0: "",
            1: "",
            2: self._human_error,
            3: self._die,
            4: "",
            5: self._bad_fiber,
            6: "",
            7: "",
            8: "",
            9: "",
            10: "",
            11: "",
            15: self._control_logic,
            16: self._electrical,
            21: self._tension_bars,
            22: self._tension_bars,
            23: self._creel_rack,
            24: self._creel_rack,
            31: "",
            32: "",
            33: "",
            34: "",
            41: self._die,
            42: self._die,
            43: self._die,
            44: self._extruder,
            45: self._extruder,
            51: self._strand_cooling,
            52: self._strand_cooling,
            53: self._strand_cooling,
            61: self._puller,
            62: self._puller,
            63: self._puller,
            64: self._pelletizer,
            65: self._pelletizer,
            66: self._pelletizer,
            67: self._puller,
            71: "",
            72: self._daybin_dryer_convey,
            73: self._dust_collection,
            74: self._screener,
            81: self._tape_control,
            82: self._winder,
            83: self._winder,
            84: self._accumulator,
            85: self._accumulator,
            91: "",
            92: "",
            93: "",
            94: "",
        }

        self._mapping_subcatlevel1_dict = {
            0: self._running,
            1: "",
            2: self._human_factor,
            3: self._extruder,
            4: self._fiber_handling_equipment,
            5: self._fiber_handling_equipment,
            6: self._no_demand,
            7: self._whole_line_pm,
            8: self._not_scheduled_to_run,
            9: self._transition,
            10: self._manpower,
            11: self._external_raw_material,
            15: self._internal_utilities,
            16: self._internal_utilities,
            21: self._fiber_handling_equipment,
            22: self._fiber_handling_equipment,
            23: self._fiber_handling_equipment,
            24: self._fiber_handling_equipment,
            31: self._feeding_system,
            32: self._feeding_system,
            33: self._feeding_system,
            34: self._feeding_system,
            41: self._extruder,
            42: self._extruder,
            43: self._extruder,
            44: self._extruder,
            45: self._extruder,
            51: self._quench_cutter_water_removal,
            52: self._quench_cutter_water_removal,
            53: self._quench_cutter_water_removal,
            61: self._puller,
            62: self._puller,
            63: self._puller,
            64: self._quench_cutter_water_removal,
            65: self._quench_cutter_water_removal,
            66: self._quench_cutter_water_removal,
            67: self._puller,
            71: self._raw_material_conveying,
            72: self._finished_product_conveying,
            73: self._classifier_screener,
            74: self._classifier_screener,
            81: self._tape_handling_equipment,
            82: self._tape_handling_equipment,
            83: self._tape_handling_equipment,
            84: self._tape_handling_equipment,
            85: self._tape_handling_equipment,
            91: "",
            92: "",
            93: self._opportunity_maintenance,
            94: self._weather,
        }

        self._mapping_eventcode_dict = {
            0: "",  
            1: self._reactive_downtime,
            2: self._reactive_downtime,
            3: self._reactive_downtime,
            4: self._reactive_downtime,
            5: self._reactive_downtime,
            6: self._no_demand,
            7: self._planned_downtime,
            8: self._not_scheduled_to_run,
            9: self._product_supply_optimization,
            10: self._reactive_downtime,
            11: self._product_supply_optimization,
            12: "",  
            15: self._reactive_downtime,
            16: self._reactive_downtime,
            21: self._reactive_downtime,
            22: self._reactive_downtime,
            23: self._reactive_downtime,
            24: self._reactive_downtime,
            31: self._reactive_downtime,
            32: self._reactive_downtime,
            33: self._reactive_downtime,
            34: self._reactive_downtime,
            41: self._reactive_downtime,
            42: self._reactive_downtime,
            43: self._reactive_downtime,
            44: self._reactive_downtime,
            45: self._reactive_downtime,
            51: self._reactive_downtime,
            52: self._reactive_downtime,
            53: self._reactive_downtime,
            61: self._reactive_downtime,
            62: self._reactive_downtime,
            63: self._reactive_downtime,
            64: self._reactive_downtime,
            65: self._reactive_downtime,
            66: self._reactive_downtime,
            67: self._reactive_downtime,
            71: self._reactive_downtime,
            72: self._reactive_downtime,
            73: self._reactive_downtime,
            74: self._reactive_downtime,
            81: self._reactive_downtime,
            82: self._reactive_downtime,
            83: self._reactive_downtime,
            84: self._reactive_downtime,
            85: self._reactive_downtime,
            91: self._reactive_downtime,
            92: self._planned_downtime,
            93: self._opportunity_maintenance,
            94: self._reactive_downtime,
        }

        self._mapping_metriccode_dict = {
            0: "",  
            1: self._availability,
            2: self._availability,
            3: self._availability,
            4: self._availability,
            5: self._availability,
            6: self._loading,
            7: self._availability,
            8: self._loading,   
            9: self._availability,
            10: self._availability,
            11: self._availability,
            12: self._availability,
            15: self._availability,
            16: self._availability,
            21: self._availability,
            22: self._availability,
            23: self._availability,
            24: self._availability,
            31: self._availability,
            32: self._availability,
            33: self._availability,
            34: self._availability,
            41: self._availability,
            42: self._availability,
            43: self._availability,
            44: self._availability,
            45: self._availability,
            51: self._availability,
            52: self._availability,
            53: self._availability,
            61: self._availability,
            62: self._availability,
            63: self._availability,
            64: self._availability,
            65: self._availability,
            66: self._availability,
            67: self._availability,
            71: self._availability,
            72: self._availability,
            73: self._availability,
            74: self._availability,
            81: self._availability,
            82: self._availability,
            83: self._availability,
            84: self._availability,
            85: self._availability,
            91: self._availability,
            92: self._availability,
            93: self._availability,
            94: self._availability,
        }

    def map(self, prod_line_status: int, property: str) -> str:
        property_dict_mapping = {
            "subCategoryLevel2": self._mapping_subcatlevel2_dict,
            "subCategoryLevel1": self._mapping_subcatlevel1_dict,
            "eventCode": self._mapping_eventcode_dict,
            "metricCode": self._mapping_metriccode_dict,
        }
        property_dict = property_dict_mapping.get(property)
        return property_dict.get(prod_line_status)
