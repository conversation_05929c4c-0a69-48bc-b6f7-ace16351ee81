# Geleen Site Event Capture Technical Documentation

## 1. Overview

The Geleen site (STS-GEL) event capture mechanism is part of the OEE (Overall Equipment Effectiveness) application that processes manufacturing data from production lines to automatically detect and categorize operational events. The system analyzes time series data from industrial sensors and control systems to identify various types of production events including downtime, quality issues, and production losses.

### Current Status
**Important Note**: The Geleen site is currently configured for **manual OEE inserts only** and is disabled from automatic event processing. This is controlled by the `IGNORE_SITES` list in the main event processing service.

### Key Characteristics
- **Site Code**: STS-GEL (Geleen)
- **Process Type**: Batch processing
- **Production Lines**: 3 active lines (RLN-GELVAE6K2, RLN-GELVAE6K3, RLN-GELVAE6K4)
- **Timezone**: Europe/Brussels
- **Event Processing**: Currently disabled for automatic processing

## 2. Event Sources

Events for the Geleen site originate from multiple data sources:

### Primary Data Sources
1. **Time Series Data (PI Tags)**
   - Production line status indicators
   - Process control system signals
   - Equipment operational states
   - Production metrics (rework, scrap, waste quantities)

2. **BBCT (Best Batch Cycle Time) Data**
   - Reference cycle times for products
   - Used for duration calculations of type 4 events
   - Contains both BBCT values and bestBatchCycleTimeMT metrics

3. **MSDP (Manufacturing Scheduling Data Platform)**
   - Production scheduling information
   - Product specifications
   - Process parameters

4. **MDR (Manufacturing Data Repository)**
   - Maximum demonstrated rates
   - Product family information
   - Historical performance data

### Data Collection Methods
- **Real-time streaming**: Continuous data ingestion from PI tags
- **Batch retrieval**: Periodic collection of reference data (BBCT, MSDP, MDR)
- **Event-driven updates**: Configuration changes trigger data refresh

## 3. Event Types

The Geleen site implements a standardized OEE event framework with the following event types:

### Type 1 Events - Availability Loss
- **1a (Not Running)**: Equipment downtime events
  - **Trigger**: ProductionLineStatus = " " or "UP_RX_WASH:1"
  - **End Condition**: Status changes from idle/wash state

- **1b, 1c, 1d**: Changeover and setup events (currently not implemented)

### Type 2 Events - Performance Loss  
- **2a, 2b, 2c**: Minor stops and performance issues (currently not implemented)

### Type 3 Events - Quality Loss
- **3b (Production Quality Issues)**: Quality-related production events
  - **Trigger**: ProductionLineStatus = "UP2_RX_LD_VAM:1" or "UP2_RX_LD_WPH:2"
  - **End Condition**: Status changes from "UP2_RX_TO_PRT:1" to another state
  - **Duration Adjustment**: Uses BBCT data for accurate duration calculation

### Type 4 Events - Production Loss
- **4a (Production Rework)**: Rework quantity events
  - **Trigger**: ProductionRework > 0
  - **Duration**: Calculated using BBCT formula: (bbct * quantity) / bbct_mt

- **4b (Production Scrap)**: Scrap quantity events  
  - **Trigger**: ProductionScrap > 0
  - **Duration**: Calculated using BBCT formula: (bbct * quantity) / bbct_mt

- **4c (Production Waste)**: Waste quantity events
  - **Trigger**: ProductionWaste > 0
  - **Duration**: Calculated using BBCT formula: (bbct * quantity) / bbct_mt

## 4. Data Flow

### Complete Event Processing Pipeline

```
1. Data Retrieval
   ├── Time Series Data (PI Tags) → TimeSeriesRepository
   ├── BBCT Data → BbctRepository  
   ├── MSDP Data → MsdpRepository
   └── MDR Data → MdrRepository

2. Event Detection
   ├── GELBatchEventIdentificationService
   ├── Apply event identification rules
   └── Generate event start/end timestamps

3. Event Processing
   ├── Apply business rules
   ├── Calculate event durations
   ├── Apply BBCT adjustments (Type 4 events)
   └── Merge product information

4. Event Validation & Enhancement
   ├── Fix event durations
   ├── Apply categorization
   ├── Generate event IDs
   └── Filter invalid events

5. Data Storage
   ├── Convert to Event objects
   ├── Create Cognite Data Fusion nodes/edges
   └── Store in CDF data model
```

### Event Detection Process
1. **Data Preprocessing**: Clean and validate time series data
2. **Event Identification**: Apply Geleen-specific business rules
3. **Duration Calculation**: Standard time-based or BBCT-based calculations
4. **Product Association**: Link events to production batches and products
5. **Quality Validation**: Filter events < 60 seconds (except RLT events)

## 5. Event Frame Structure

### Core Event Properties
```python
Event {
    external_id: str                    # Unique event identifier
    event_definition: str               # Event type (1a, 3b, 4a, 4b, 4c)
    start_date_time: str               # Event start timestamp
    end_date_time: str                 # Event end timestamp  
    total_duration: float              # Duration in seconds
    status: str                        # Processing status (Unassigned/Assigned)
    ref_reporting_line: Node           # Reference to production line
    ref_site: Node                     # Reference to Geleen site
    ref_oee_product: Node             # Reference to product being produced
    event_id: str                      # Internal event grouping ID
}
```

### Event Detail Properties
```python
EventDetail {
    product: str                       # Product name/code
    batch_id: str                     # Production batch identifier
    process_order: str                # Manufacturing order number
    production_rework: float          # Rework quantity (Type 4a)
    production_scrap: float           # Scrap quantity (Type 4b)  
    production_waste: float           # Waste quantity (Type 4c)
    bbct: float                       # Best batch cycle time value
}
```

### External ID Generation
Format: `OEEEVENT-{line_code}-{event_definition}-{start_time}`
Example: `OEEEVENT-GELVAE6K2-1a-2024-01-15T08:30:00`

## 6. Configuration

### Site-Specific Parameters
- **Reporting Site External ID**: STS-GEL
- **Process Type**: Batch
- **Timezone**: Europe/Brussels  
- **Work Shifts**: Configurable shift patterns
- **Event Processing**: Currently disabled (manual mode)

### Reporting Lines Configuration
```
RLN-GELVAE6K2: Geleen VAE Line 6K2
RLN-GELVAE6K3: Geleen VAE Line 6K3  
RLN-GELVAE6K4: Geleen VAE Line 6K4
```

### Tag Configuration Structure
```python
InputTagConfiguration {
    reporting_line: ReportingLine      # Line reference
    time_series: str                   # PI tag external ID
    alias: str                         # Descriptive name
    event_identification: bool         # Used for event detection
    tag_value_mapping: bool           # Requires value mapping
}
```

### Event Hierarchy Configuration
```python
EventHierarchyConfiguration {
    reporting_line: ReportingLine      # Line reference
    event_hierarchy: str               # Event code (1a, 3b, 4a, etc.)
    event_definition: str              # Human-readable description
    business_rule: bool                # Enable processing
    uses_bbct: bool                   # Use BBCT for duration
    work_shift_rule: bool             # Split events by shifts
}
```

## 7. Integration Points

### Cognite Data Fusion Integration
- **Data Model**: OEESOL v6.1.2
- **Spaces**: 
  - Data: OEE-COR-ALL-DAT
  - References: REF-COR-ALL-DAT
  - Model: INO-COR-ALL-DML

### External System Connections
1. **PI System**: Real-time time series data ingestion
2. **Manufacturing Systems**: MSDP and MDR data sources
3. **Quality Systems**: BBCT reference data
4. **Reporting Systems**: Event data consumption

### API Endpoints
- **GraphQL Queries**: Data retrieval from CDF data model
- **REST APIs**: Time series data access
- **Batch Processing**: Scheduled data synchronization

## 8. Code References

### Primary Service Files
- **`event_frame_service.py`**: Main orchestration service (lines 150, 357-360)
- **`gel_batch_event_identification_service.py`**: Geleen-specific event logic
- **`event_identification_factory.py`**: Service factory (lines 1009-1017)

### Repository Classes
- **`timeseries_repository.py`**: Time series data access
- **`bbct_repository.py`**: Best Batch Cycle Time data
- **`msdp_repository.py`**: Manufacturing scheduling data  
- **`mdr_repository.py`**: Manufacturing data repository
- **`event_frame_repository.py`**: Event storage operations

### Model Classes
- **`event_frame.py`**: Core event data structures
- **`reporting_site_configuration.py`**: Site configuration model
- **`event_hierarchy_configuration.py`**: Event type definitions
- **`input_tag_configuration.py`**: Tag mapping configuration

### Key Methods
- **`transform_time_series_to_event_frames()`**: Main processing pipeline
- **`identify_events_typeXX()`**: Event detection methods
- **`fix_events_type4_duration_based_BBCT()`**: BBCT duration adjustment
- **`create_events()`**: Event persistence to CDF

### Configuration Files
- **`handler.py`**: Function entry point
- **`.env`**: Cognite connection settings
- **`GlobalConfiguration.xlsx`**: Site and line configurations

---

*This documentation reflects the current implementation as of the codebase analysis. The Geleen site requires activation by removing "STS-GEL" from the IGNORE_SITES list to enable automatic event processing.*
