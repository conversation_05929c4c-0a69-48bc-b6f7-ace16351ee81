from enum import Enum

import pandas as pd


class WasEpceStateMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            1: "RUN",
            2: "PRODUCING WASTE",
            3: "NORMAL",
            4: "NOT RUNNING",
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})

class StatesTypes(Enum):
    RUN = "RUN"
    PRODUCING_WASTE = "PRODUCING WASTE"
    NORMAL = "NORMAL"
    NOT_RUNNING = "NOT RUNNING"