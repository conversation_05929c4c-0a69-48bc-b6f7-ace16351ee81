def event_frame_delete_batch_id_product_id(df):
    """
    Processes a dataframe by checking specific conditions and modifying rows without removing others.

    Parameters:
        df (pd.DataFrame): The dataframe to process.

    Returns:
        pd.DataFrame: The dataframe with modifications applied.
    """
    ref_unit_ids = [
        'UNT-ENRVAE', 'UNT-ENRCNV', 'UNT-ENRCNT', 'UNT-PERVAE', 'UNT-PERCNV', 'UNT-NANVAE',
        'UNT-NARFLK', 'UNT-SHYVEC', 'UNT-BCHCNV', 'UNT-SPSVAE', 'UNT-EDAPOL'
    ]
    event_definition = ['Batch Idle', 'Not Running', 'Batch Idle - Known No demand']

    condition = (
        df['refUnitId'].isin(ref_unit_ids) & 
        df['def'].isin(event_definition)
    )
 
    df.loc[condition, ['oeeProductExternalId', 'oeeProductSpace', 'BatchID']] = None

    
    return df