from typing import Any

from pydantic import BaseModel
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables
from cognite.client.data_classes.data_modeling import DataModel, ViewId
import pandas as pd


class OeeInputTagConfiguration(BaseModel):
    reporting_line_ext_id: str
    reporting_line_name: str
    time_series_ext_id: str
    time_series_name: str
    time_series_unit: str | None
    time_series_dataset_id: str | None


class ExportTimeseriesFromTagConfiguration:
    def __init__(self):
        self.__env_variables = EnvVariables()
        self.__cognite_client = CogniteClientFactory.create(self.__env_variables)

    def __get_data_model(self) -> DataModel[ViewId]:
        return self.__cognite_client.data_modeling.data_models.retrieve(
            ids=(
                self.__env_variables.cognite.data_model_space,
                "OEESOL",
                self.__env_variables.cognite.data_model_version,
            )
        ).latest_version()

    def __get_query(self) -> str:
        return """
                query listOEEInputTagConfiguration($after: String) {
                    listOEEInputTagConfiguration (first: 1000, after: $after) {
                        items {
                            reportingLine {
                                externalId
                                name
                            } 
                            timeSeries { 
                                externalId
                                name
                                unit
                                datasetId
                            }
                        }
                        pageInfo{
                            hasNextPage
                            endCursor
                        }
                    } 
                }
                """

    def __get_tags(
        self, data_model: DataModel[ViewId], query: str
    ) -> list[OeeInputTagConfiguration]:
        tags: list[OeeInputTagConfiguration] = []
        has_next_page = True
        after = None

        while has_next_page:
            result = self.__cognite_client.data_modeling.graphql.query(
                data_model.as_id(), query, variables={"after": after}
            )
            items: list[dict[str, Any]] = result["listOEEInputTagConfiguration"][
                "items"
            ]
            page_info = result["listOEEInputTagConfiguration"]["pageInfo"]
            has_next_page = page_info["hasNextPage"]
            after = page_info["endCursor"]

            for item in items:
                reporting_line: dict[str, dict] = item.get("reportingLine", {})
                time_series: dict[str, dict] = item.get("timeSeries", {})
                tags.append(
                    OeeInputTagConfiguration(
                        reporting_line_ext_id=reporting_line.get("externalId", ""),
                        reporting_line_name=reporting_line.get("name", ""),
                        time_series_ext_id=time_series.get("externalId", ""),
                        time_series_name=time_series.get("name", ""),
                        time_series_unit=time_series.get("unit", ""),
                        time_series_dataset_id=time_series.get("datasetId", ""),
                    )
                )

        return tags
    
    def __get_tags_as_data_frame(self, data_model: DataModel[ViewId], query: str) -> pd.DataFrame:
        tags = self.__get_tags(data_model, query)
        return pd.DataFrame([dict(tag) for tag in tags])
    
    def __export_tags_to_xlsx(self, df: pd.DataFrame) -> None:
        file_name = "timeseries_from_tag_configuration.xlsx"
        df.to_excel(file_name, index=False)

    def run(self) -> None:
        data_model = self.__get_data_model()
        query = self.__get_query()
        tags_as_df = self.__get_tags_as_data_frame(data_model, query)
        self.__export_tags_to_xlsx(df=tags_as_df)


if __name__ == "__main__":
    export_timeseries = ExportTimeseriesFromTagConfiguration()
    export_timeseries.run()
