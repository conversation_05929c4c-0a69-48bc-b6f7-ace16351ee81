import logging
from typing import Any, List

import pandas as pd
from cognite.client import Cognite<PERSON>lient
from cognite.client.data_classes.data_modeling import (
  EdgeApply,
  NodeApply,
  NodeOrEdgeData,
)
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from cognite.client.data_classes.data_modeling.ids import ViewId

from ..models.process import OeeProcess
from ..models.reporting_site import ReportingSite
from ..utils.graphql import generate_query, query_all
from .view_repository import ViewRepository


class ProcessRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        view_repository: ViewRepository,
        data_model_id: DataModelId,
    ) -> None:
        self._cognite_client = cognite_client
        self._view_repository = view_repository
        self._data_model_id = data_model_id

    def get_oee_process_df(
        self,
        reporting_site: ReportingSite,
    ) -> pd.DataFrame:
        filter_ = {
            "and": [
                {"name": {"isNull": False}},
                {"OEECategory": {"isNull": False}},
                {"refProcessType": {"externalId": {"isNull": False}}},
                {
                    "refSite": {
                        "externalId": {"eq": reporting_site.external_id}
                    }
                },
            ]
        }

        list_name = "listOEEProcess"

        query_result: list[dict[str, Any]] = query_all(
            client=self._cognite_client,
            data_model_id=self._data_model_id,
            list_name=list_name,
            query=generate_query(
                list_name, self._build_product_query_selection()
            ),
            filter=filter_,
        )

        if len(query_result) > 0:
            return pd.DataFrame.from_records(
                [
                    {
                        "refProcessTypeExternalId": process_type_id,
                        "parentExternalId": (
                            r.pop("refOEEProcess", None) or {}
                        ).get("externalId"),
                        **r,
                    }
                    for r in query_result
                    if (
                        process_type_id := (
                            r.pop("refProcessType", None) or {}
                        ).get("externalId")
                    )
                    is not None
                ]
            ).drop_duplicates(
                subset=["name", "OEECategory", "refProcessTypeExternalId"]
            )

        return pd.DataFrame(
            columns=[
                "externalId",
                "space",
                "name",
                "OEECategory",
                "refProcessTypeExternalId",
                "parentExternalId",
            ]
        )

    def _build_product_query_selection(self):
        return """
            externalId
            space
            name
            OEECategory
            refProcessType {
                externalId
            }
            refOEEProcess {
                externalId
                space
            }
        """
        
    def get_units_oee_process_df(
        self,
        unit_ids: List[str],
    ) -> pd.DataFrame:
        filter_ = {
            "externalId": {
                "in": unit_ids
            }
        }

        list_name = "listOEEReportingUnit"

        query_result: list[dict[str, Any]] = query_all(
            client=self._cognite_client,
            data_model_id=self._data_model_id,
            list_name=list_name,
            query=generate_query(
                list_name, self._build_process_for_units_query_selection()
            ),
            filter=filter_,
        )
        
        result = {}
        
        for unit in query_result:
            unit_id = unit.get("externalId")
            ref_oee_process_items = unit.get("refOEEProcess", [])

            if len(ref_oee_process_items) > 0:
                df = pd.DataFrame.from_records(
                    [
                        {
                            "parentExternalId": (
                                process.pop("refOEEProcess", None) or {}
                            ).get("externalId"),
                            **process,
                        }
                        for process in ref_oee_process_items
                    ]
                ).drop_duplicates(subset=["externalId", "name", "OEECategory"])
                    
                result[unit_id] = df
            else:
                result[unit_id] = pd.DataFrame(
                    columns=[
                        "externalId",
                        "space",
                        "name",
                        "OEECategory",
                        "parentExternalId",
                    ]
                )

        return result

    def _build_process_for_units_query_selection(self):
        return """
            externalId
            refOEEProcess(filter: {isActive: {eq: true}}, first: 1000) {
                items {
                    externalId
                    space
                    name
                    isActive
                    OEECategory
                    refOEEProcess {
                        externalId
                        space
                    }
                }
            }
        """

    def query_all_oee_process_inactive(self) -> List[dict[str, Any]]:
        """
        Busca todos os processos OEE Process Inativos e retorna como uma lista de dicionários.
        """
        all_results = []
        cursor = None

        while True:
            query_result = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                self.build_paginated_query_inactive(),
                {"after": cursor},
            )["listOEEProcess"]

            items = query_result["items"]
            all_results.extend(items)

            page_info = query_result["pageInfo"]
            if not page_info["hasNextPage"]:
                break

            cursor = page_info["endCursor"]

        return all_results
    
    def query_all_oee_process_active(self) -> List[dict[str, Any]]:
        """
        Busca todos os processos OEE Process Ativos e retorna como uma lista de dicionários.
        """
        all_results = []
        cursor = None

        while True:
            query_result = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                self.build_paginated_query_active(),
                {"after": cursor},
            )["listOEEProcess"]

            items = query_result["items"]
            all_results.extend(items)

            page_info = query_result["pageInfo"]
            if not page_info["hasNextPage"]:
                break

            cursor = page_info["endCursor"]

        return all_results
    
    def build_paginated_query_inactive(self) -> str:
        """
        Constrói a query GraphQL para listar processos OEE.
        """
        return """
        query MyQuery($after: String) {
        listOEEProcess(filter: {isActive: {eq: false}}, first: 1000, after: $after) {
            items {
            externalId
            space
            name
            OEECategory
            refUnits {
                    items {
                        externalId
                        space
                        name
                    }
                }
            refOEEProcess {
                externalId
                space
                name
            }
            isActive
            }
            pageInfo {
            endCursor
            hasNextPage
            hasPreviousPage
            startCursor
            }
        }
        }
        """
        
    def build_paginated_query_active(self) -> str:
        """
        Constrói a query GraphQL para listar processos OEE.
        """
        return """
        query MyQuery($after: String) {
        listOEEProcess(filter: {isActive: {eq: true}}, first: 1000, after: $after) {
            items {
            externalId
            space
            name
            OEECategory
            refUnits {
                    items {
                        externalId
                        space
                        name
                    }
                }
            refOEEProcess {
                externalId
                space
                name
            }
            isActive
            }
            pageInfo {
            endCursor
            hasNextPage
            hasPreviousPage
            startCursor
            }
        }
        }
        """
        
    def get_oee_process_inactive_data_as_dataframe(self) -> pd.DataFrame:
        
        """
        Retorna todos os processos OEE como um DataFrame.
        """
        data = self.query_all_oee_process_inactive()

        if not data:
            return pd.DataFrame()  # Retorna um DataFrame vazio se não houver dados

        return pd.DataFrame(data)
    
    def get_oee_process_active_data_as_dataframe(self) -> pd.DataFrame:
        
        """
        Retorna todos os processos OEE como um DataFrame.
        """
        data = self.query_all_oee_process_active()

        if not data:
            return pd.DataFrame()  # Retorna um DataFrame vazio se não houver dados

        return pd.DataFrame(data)
    
    
    def create_oee_processes(self, oee_processes: pd.DataFrame) -> None:
        """
        Cria os processos OEE com base nos dados fornecidos.

        Args:
            oee_processes (pd.DataFrame): DataFrame representando os processos OEE.
        """
        if oee_processes.empty:
            logging.info("Nenhum dado para criar OEE Processes.")
            return

        if not self._view_repository:
            raise ValueError(
                "ViewRepository ausente na instância do ProcessRepository."
            )

        # Obter o ID da view associada aos OEE Processes
        view_id = self._view_repository.get_view_id("OEEProcess")

        # Converter DataFrame em objetos OeeProcess
        oee_process_objects = [
            OeeProcess(
                space="INO-COR-ALL-DML",
                external_id=row["new_externalId"],
                name=row["name"],
                category=row["OEECategory"],
                is_active=True,
                ref_oee_process=row["refOeeProcess"],
            )
            for _, row in oee_processes.iterrows()
        ]

        # Converter objetos em nós
        nodes = [obj.convert_to_cognite_node(view_id=view_id) for obj in oee_process_objects]

        # Paginação de nós para evitar exceder o limite da API
        paginated_nodes = [nodes[i:i + 1000] for i in range(0, len(nodes), 1000)]

        # Criar nós em lotes
        for chunk in paginated_nodes:
            self._cognite_client.data_modeling.instances.apply(nodes=chunk)


    def create_edges_between_processes_and_units(self, oee_processes: pd.DataFrame) -> None:
        """
        Cria edges entre os OeeProcess e as unidades (refUnits) com base nos dados fornecidos.

        Args:
            oee_processes (pd.DataFrame): DataFrame contendo os processos OEE e suas referências a unidades.
        """
        if oee_processes.empty or "refUnits" not in oee_processes.columns:
            logging.info("Nenhum dado disponível para criar edges entre processos e unidades.")
            return

        edges = []
        edge_set = set()  # Para evitar duplicatas

        # Iterar sobre os processos e criar edges para as unidades referenciadas
        for _, process in oee_processes.iterrows():
            ref_units = process.get("refUnits", [])

            if isinstance(ref_units, list):  # Certificar que refUnits é uma lista de itens
                for unit in ref_units:
                    edge_external_id = f"{unit['externalId']}-{process['new_externalId']}"
                    if edge_external_id not in edge_set:
                        edge = EdgeApply(
                            space="INO-COR-ALL-DML",  # Ajuste conforme necessário
                            external_id=edge_external_id,
                            type=("INO-COR-ALL-DML", "OEEReportingUnit.refOEEProcess"),
                            start_node=(unit["space"], unit["externalId"]),
                            end_node=("INO-COR-ALL-DML", process["new_externalId"]),
                        )
                        edges.append(edge)
                        edge_set.add(edge_external_id)

        # Paginação de edges para evitar exceder o limite da API
        paginated_edges = [edges[i:i + 1000] for i in range(0, len(edges), 1000)]

        # Criar edges em lotes
        for chunk in paginated_edges:
            self._cognite_client.data_modeling.instances.apply(edges=chunk)


    def inactivate_oee_processes(self, oee_processes: pd.DataFrame) -> None:
        """
        Inativa os processos OEE fornecidos, atualizando o campo 'isActive' para False.

        Args:
            oee_processes (pd.DataFrame): DataFrame contendo os processos a serem inativados.
            view_id (ViewId): O identificador da view onde os dados estão armazenados.
        """
        
        view_id = self._view_repository.get_view_id("OEEProcess")

        if oee_processes.empty:
            logging.info("Nenhum processo OEE para inativar.")
            return

        nodes = []
        for _, process in oee_processes.iterrows():
            if pd.notnull(process.get("externalId")) and pd.notnull(process.get("space")):
                nodes.append(
                    NodeApply(
                        space=process["space"],
                        external_id=process["externalId"],
                        sources=[
                            NodeOrEdgeData(
                                source=view_id,
                                properties={"isActive": False},  # Atualizando o campo 'isActive' para False
                            )
                        ],
                    )
                )

        # Paginar os nós para evitar exceder os limites da API
        paginated_nodes = [nodes[i:i + 1000] for i in range(0, len(nodes), 1000)]

        for chunk in paginated_nodes:
            self._cognite_client.data_modeling.instances.apply(nodes=chunk)

        logging.info(f"Inativados {len(nodes)} processos OEE.")


