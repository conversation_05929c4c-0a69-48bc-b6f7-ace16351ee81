import pandas as pd

class NanGurProductMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
          "4011": "51032486",
          "4011P": "51032486",
          "4012": "51014587",
          "4012F": "51014587",
          "4012P": "51014585",
          "4016": "51014532",
          "4016P": "51014562",
          "4018": "51004293",
          "4018P": "51009213",
          "4022": "50006595",
          "4022S": "51014458",
          "4022N": "50006595",
          "4032": "50006597",
          "4112F": "51014906",
          "4116": "51014904",
          "4118": "51014905",
          "4120": "50006599",
          "4130": "50006601",
          "4150": "50006603",
          "4150C": "50003014",
          "4152": "51010561",
          "4152H": "51010561",
          "H011": "51033203",
          "H010": "51033076",
          "402M12": "51030596",
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        # Remove single or multiple spaces using regex
        data["ProductDescription"] = data["ProductDescription"].str.replace(r'\s+', '', regex=True).str.upper()
        data["Product"] = data["Product"].str.replace(r'\s+', '', regex=True).str.upper()
        return data.replace({time_series: self._mapping_dict})
