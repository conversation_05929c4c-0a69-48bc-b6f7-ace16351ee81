from typing import Optional


def get_space(reporting_site_code: Optional[str], default: str, is_adm: bool = False):
    space = default
    cor_code = "-COR-"
    if reporting_site_code is not None and cor_code in space:
        site_code = f"-{reporting_site_code}-"
        space = space.replace(cor_code, site_code)
    if not is_adm:
        return space

    app_code = space.split("-")[0]
    if app_code.endswith("ADM"):
        return space

    return space.replace(app_code, app_code + "ADM")
