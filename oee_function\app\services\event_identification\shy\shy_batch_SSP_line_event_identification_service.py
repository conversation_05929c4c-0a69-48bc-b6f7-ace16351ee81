from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.enums.shy.prod_line_status import ProdLineStatusEnumSSP
from app.models.lead_product import LeadProductBbct

class ShyBatchSSPLineEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # event trigger start = "IDLE"  or "WAIT" or "MAINT"
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] == ProdLineStatusEnumSSP.IDLE.value)
                | (
                    data["ProductionLineStatus"]
                    == ProdLineStatusEnumSSP.WAIT.value
                )
            )
        )

        # event trigger end - ProductionLineStatus = "LOAD" or "HEAT" or "COOL" or "PKGING"
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus"] == ProdLineStatusEnumSSP.LOAD.value)
                | (
                    data["ProductionLineStatus"]
                    == ProdLineStatusEnumSSP.HEAT.value
                )
                | (
                    data["ProductionLineStatus"]
                    == ProdLineStatusEnumSSP.COOL.value
                )
                | (
                    data["ProductionLineStatus"]
                    == ProdLineStatusEnumSSP.PKGING.value
                )
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        ).assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )
        
        return data

    def identify_events_typeIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ib

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # event trigger start - ProductionLineStatus == "IDLE"
        data = data.assign(
            event1b_start=(
                data["ProductionLineStatus"] == ProdLineStatusEnumSSP.IDLE.value
            )
        )

        # event trigger end - ProductionLineStatus != "IDLE"
        data = data.assign(
            event1b_end=(
                data["ProductionLineStatus"] != ProdLineStatusEnumSSP.IDLE.value
            )
        )

        # correct start and end flags
        data = data.assign(
            event1b_start=(
                (data["event1b_start"] == True)
                & (data["event1b_start"].shift(1) != True)
            )
        ).assign(
            event1b_end=(
                (data["event1b_end"] == True)
                & (data["event1b_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIIb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type III event
        :rtype: pl.DataFrame
        """
        # event trigger start - ProcessOrder changes
        data = data.assign(
            event3b_start=(
                (data["ProcessOrder"].shift(1) != data["ProcessOrder"])
                & (data["ProcessOrder"].index > 0)
                & (~data["ProcessOrder"].isna())
            )
        )

        # correct start flag
        data = data.assign(
            event3b_start=(
                (data["event3b_start"] == True)
                & (data["event3b_start"].shift(1) != True)
            )
        )

        # event trigger end - ProcessOrder changes
        data = data.assign(
            event3b_end=(
                (data["ProcessOrder"].shift(1) != data["ProcessOrder"])
                & (data["ProcessOrder"].index > 0)
                & (~data["ProcessOrder"].isna())
                & (~data["ProcessOrder"].shift(1).isna())
            )
        )

        # correct end flag
        data = data.assign(
            event3b_end=(
                (data["event3b_end"] == True)
                & (data["event3b_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def get_bbct_value(self, row: pd.Series, bbct_data: pd.DataFrame) -> float:
        """
        retrieves the BBCT from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        # extract filter parameter
        prod_id = row["ProductDescription"]
        event = row["event_definition"]
        start_time_year = row["start_time"].year
        start_time_month = row["start_time"].month

        # create reference date to find BBCT
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # if the event does not depend on BBCT values, return 0
        if not self._reporting_site_configuration.event_dependends_on_bbct(
            self._reporting_line_external_id,
            event,
        ):
            return 0

        # first filter - filter by year and month of the start date and
        # by the productid
        filter_1 = (
            (bbct_data["productId"].str.lower() == prod_id.lower())
            & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            & (bbct_data["year"] == start_time_year)
            & (bbct_data["month"] == start_time_month)
        )
        aux = bbct_data.loc[filter_1, :]

        # if aux is empty, we need to test the second filter
        if aux.shape[0] == 0:
            filter_2 = (
                bbct_data["productId"].str.lower() == prod_id.lower()
            ) & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            aux = bbct_data.loc[filter_2, :]

            # if still the aux is empty, then we don't have matches, return 0
            if aux.shape[0] == 0:
                return 0

            # ensure ordering by the most recent year, considering the event start date
            t = (aux["timestamp"] - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # fill values to get the most recent date preceding the date of event
            aux.fillna(method="ffill", inplace=True)
            aux["bbct"].fillna(0, inplace=True)

            # ensure ordering by the most recent year, considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # extract value of MDR
        return aux["bbct"].head(1).values[0]

    def fix_3b_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame, lead_bbct: LeadProductBbct
    ) -> pd.DataFrame:
        """
        applies the business that demands subtraction of the
        Best Batch Cycle time from every event of type 3b

        :param data: event data
        :type data: pd.DataFrame
        :return: data with the right duration of the events
        :rtype: pd.DataFrame
        """
        pi_tag_value_to_product_description = {
            "VE E950IPP": "E950ISX",
            "VE J950PP": "J950SX",
            "VE S950PP": "S950SX",
            "VE T950PP": "T950SX",
            "ZE 7000PP": "ZE7000SX",
            "ZE 7700PP": "ZE7700SX",
        }
        bbct_data.replace(
            {"productId": pi_tag_value_to_product_description}, inplace=True
        )

        # apply extraction of the BBCT value
        data["bbct"] = data.apply(
            self.get_bbct_value,
            bbct_data=bbct_data,
            axis=1,
        )

        # fix events 3b duration
        data["total_duration_seconds"] -= data["bbct"]

        data = data.query("total_duration_seconds != 0")

        # fill NaN values with 0 when data does not have BBCT
        data["total_duration_seconds"].fillna(0, inplace=True)

        # drop created column
        data.drop(columns=["bbct"], inplace=True)

        return data

