import requests
import pandas as pd

# PUT HERE YOUR COGNITE TOKEN
TOKEN = "YOUR_COGNITE_TOKEN" 

# URL e cabeçalhos da requisição
url = 'https://az-eastus-1.cognitedata.com/api/v1/projects/celanese/transformations/query/run'
headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + TOKEN,
}

# Dados da requisição
data = {
    "limit": 1000,
    "query": """
        SELECT
            event.externalId,
            event.refReportingLine.externalId as reportingLine,
            event.eventDefinition,
            event.status,
            event.startDateTime,
            timestamp_millis(event.`node.createdTime`) as dtCriacao
        FROM
            cdf_data_models('INO-COR-ALL-DML', 'OEESOL', '5_0_1', 'OEEEvent') AS event
        WHERE
            event.startDateTime >= "2025-01-01"
            AND event.status = 'Unassigned'
            AND timestamp_millis(event.`node.createdTime`) < event.startDateTime
    """,
    "convertToString": True
}

# Fazendo a requisição
response = requests.post(url, headers=headers, json=data)

# Verificando se a requisição foi bem-sucedida
if response.status_code == 200:
    # Convertendo a resposta JSON em um DataFrame do pandas
    json_response = response.json()
    results = json_response['results']['items']
    df = pd.DataFrame(results)

    # Salvando o DataFrame em um arquivo Excel
    df.to_excel('resultados.xlsx', index=False)
    print("Arquivo Excel gerado com sucesso!")
else:
    print(f"Erro na requisição: {response.status_code} - {response.text}")