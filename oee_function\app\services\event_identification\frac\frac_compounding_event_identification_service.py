from typing import Any

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.hourly_data_compounding import (
  HourlyDataCompounding,
)


class FraCompoundingFirstEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
    ) -> None:
        super().__init__()
        if mdr is not None:
            self._valid_prod_ids = list(mdr.productId.unique())
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            # "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start
        # ProductionLineStatus = 511E32001.EIN
        # ProductionLineStatus == 'Off'
        # ProductionLineStatus_2 = 511_Running.C
        # ProductionLineStatus_2 == 'Off'
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus_2"].shift(1) == "On") & 
                (data["ProductionLineStatus_2"] == "Off")
            )
        )

        # event trigger end
        # ProductionLineStatus = 511E32001.EIN
        # ProductionLineStatus == 'On'
        # ProductionLineStatus_2 = 511_Running.C
        # ProductionLineStatus_2 == 'On'
        data = data.assign(
            event1a_end=(

                (data["ProductionLineStatus_2"].shift(1) == "Off") & 
                (data["ProductionLineStatus_2"] == "On") 
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

        # Code suspended by client
        # # event trigger start
        # data = data.assign(
        #     event2a_start=(
        #         (data["ProductionLineStatus"] == "On")
        #         & (data["ProductionLineStatus"].shift(1) != "On")
        #         & (data["StartingUp"] == "Off")
        #     )
        # )

        # # event trigger end
       
        # data = data.assign(
        #     event2a_end=(
        #         (data["StartingUp"] == "On")
        #         & (data["StartingUp"].shift(1) != "On")
        #     )
        # )

        # return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self):
        pass

    # Code suspended due to problems in the PI TAGs according to the website.

    # def identify_events_typeIVa(
    #     self, data: pd.DataFrame, **args
    # ) -> pd.DataFrame:
    #     """
    #     identifies the events of type IVa

    #     :param data: time series data with the status of the line
    #     :type data: pd.DataFrame
    #     :return: data with tags which indicate the start and the end time of each type IVa event
    #     :rtype: pd.DataFrame
    #     """

    #     # event trigger start
    #     # 511E32001.EIN = "On" and 511U31200.EIN = "Open"
    #     data = data.assign(
    #         event4a_start=(
    #             ((data["ProductionLineStatus"] == "On")
    #             & (data["ProductValve"] == 1))
    #             & ~((data["ProductionLineStatus"].shift(1) == "On")
    #                 & (data["ProductValve"].shift(1) == 1))
    #         )
    #     )

    #     # event trigger end
    #     # 511E32001.EIN = "Off" or 511U31200.EIN = "Close"
    #     data = data.assign(
    #         event4a_end=(
    #             ((data["ProductionLineStatus"] == "Off")
    #              | (data["ProductValve"] == 0))
    #             & ~((data["ProductionLineStatus"].shift(1) == "Off")
    #                 | (data["ProductValve"].shift(1) == 0))
    #         )
    #     )

    #     return data

    def identify_events_typeIVb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass
