import pandas as pd


class WasMpwCmpProductDescriptionMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            "ZYT70G13HS1L NC010": "ZYT70G13HS1L NC010 (US)",
            "ZYT70G13L NC010": "ZYT70G13L NC010 (US)",
            "ZYT70G30HSLR NC010": "ZYT70G30HSLR NC010 (US)",
            "ZYT70G33HS1L NC010": "ZYT70G33HS1L NC010 (US)",
            "ZYT70G33L NC010": "ZYT70G33L NC010 (US)",
            "ZYT70G35EF NC010": "ZYT70G35EF NC010 (US)",
            "ZYT70G35HSL NC010": "ZYT70G35HSL NC010 (US)",
            "ZYT70G43HSL NC010": "ZYT70G43HSL NC010 (US)",
            "ZYT70G43L NC010": "ZYT70G43L NC010 (US)",
            "ZYT73G15L NC010": "ZYT73G15L NC010 (US)",
            "ZYT73G30HSL NC010": "ZYT73G30HSL NC010 (US)",
            "ZYT73G30L NC010": "ZYT73G30L NC010 (US)",
            "ZYT77G33HS1L NC010": "ZYT77G33HS1L NC010 (US)",
            "ZYT77G33L NC010": "ZYT77G33L NC010 (US)",
            "ZYT80G14A NC010": "ZYT80G14A NC010A (US)",
            "ZYT80G14AHS NC010": "ZYT80G14AHS NC010 (US)",
            "ZYT80G33HS1L NC010": "ZYT80G33HS1L NC010 (US)",
            "ZYT80G33L NC010": "ZYT80G33L NC010 (US)",
            "ZYTFE5322 NC010": "ZYTFE5322 NC010 (US)",
            "ZYTBM70G20HSLX BK537": "ZYTBM70G20HSLX BK537 (US)",
            "RYN550HTE BK503": "RYN550HTE BK503 (US)",
            "ZYTST801A NC010A": "ZYTST801A NC010A (US)",
            "ZYT408HS NC010": "ZYT408HS NC010 (US)",
            "ZYT408L NC010": "ZYT408L NC010 (US)",
            "ZYT7335F NC010": "ZYT7335F NC010 (US)",
            "ZYTMT409AHS NC010": "ZYTMT409AHS NC010 (US)",
            "ZYTST7301 NC010": "ZYTST7301 NC010 (US)",
            "ZYTST801 NC010": "ZYTST801 NC010 (US)",
            "ZYTST801A NC010": "ZYTST801A NC010A (US)",
            "ZYTST801AHS NC010": "ZYTST801AHS NC010 (US)",
            "ZYTST801AW NC010": "ZYTST801AW NC010 (US)",
            "ZYTST801HS NC010": "ZYTST801HS NC010 (US)",
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})
