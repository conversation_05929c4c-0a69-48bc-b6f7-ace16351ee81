from typing import Optional

from cognite.client.data_classes.data_modeling import (
  EdgeApply,
  NodeApply,
  NodeOrEdgeData,
  ViewId,
)
from pydantic import BaseModel, ConfigDict, Field


class Node(BaseModel):
    model_config = ConfigDict(
        extra="ignore",
    )
    external_id: str = Field(alias="externalId")
    space: str = Field(alias="space")

    def _extract_properties_without_identifier(
        self, by_alias: bool, keys_to_exclude: Optional[list[str]] = None, exclude_none: bool = True
    ):
        result = self.model_dump(by_alias=by_alias, exclude_none=exclude_none)

        final_keys_to_exclude = (
            {"externalId", "space"} if by_alias else {"external_id", "space"}
        )
        if keys_to_exclude:
            final_keys_to_exclude.update(keys_to_exclude)

        return {
            k: v for k, v in result.items() if k not in final_keys_to_exclude
        }

    def convert_to_cognite_node(
        self,
        view_id: ViewId,
        exclude_none: bool = True,
        by_alias: bool = True,
        keys_to_exclude: Optional[list[str]] = None,
    ) -> NodeApply:
        return NodeApply(
            self.space,
            self.external_id,
            sources=[
                NodeOrEdgeData(
                    source=view_id,
                    properties=self._extract_properties_without_identifier(
                        by_alias=by_alias,
                        keys_to_exclude=keys_to_exclude,
                        exclude_none=exclude_none
                    ),
                )
            ],
        )

    def convert_to_cognite_edge(
        self, view_id: ViewId, property_name: str, end_node: "Node"
    ) -> EdgeApply:
        type_external_id = f"{view_id.external_id}.{property_name}"
        edge_external_id = (
            f"{type_external_id}-{self.external_id}-{end_node.external_id}"
        )

        return EdgeApply(
            self.space,
            edge_external_id,
            type=(view_id.space, type_external_id),
            start_node=(self.space, self.external_id),
            end_node=(end_node.space, end_node.external_id),
        )

    def convert_to_node_reference(self) -> "Node":
        return Node(externalId=self.external_id, space=self.space)
