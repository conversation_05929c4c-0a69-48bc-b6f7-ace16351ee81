import pandas as pd


class ObhProductMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            1: "GHR 8020",
            2: "GHR 8010",
            3: "GUR 1020",
            4: "GUR 1050",
            5: "GUR 2005",
            6: "GUR 2022",
            7: "GUR 2022-Y",
            8: "GUR 2022-5",
            9: "GUR 4012",
            10: "GUR 4020",
            11: "GUR 4022",
            12: "GUR 4022-6",
            13: "GUR 4030",
            14: "GUR 4032",
            15: "GUR 4050",
            16: "GUR 4050-3",
            17: "GUR 4022-5",
            19: "GUR 4070",
            23: "SL180",
            25: "GUR 4013",
            31: "GUR 4020-3",
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})
