import numpy as np
import pandas as pd
from datetime import time
from typing import Optional

from app.utils.constants import Constants as const
from app.utils.msdp_utils import get_msdp_product_filter, get_msdp_value as msdp_util_get_msdp_value
from app.utils.product_matching_utils import create_product_match_filter

class RateLossTimeContinuous:
    def __init__(self, msdp: pd.DataFrame, reporting_line_external_id: str, multi_product: bool = False):
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._line_uses_scheduled_rate = (
            self._reporting_line_external_id
            in const.LINES_USE_SCHEDULED_RATE_FOR_RLT
        )
        self._multi_product = multi_product
        
        self._day_data = None

    def create_day_data(
            self, 
            data: pd.DataFrame, 
            agg: str, 
            msdp_columns: list[str], 
            shift_value: Optional[int] = 0
        ) -> pd.DataFrame:

        if self._day_data is not None:
            return self._day_data.copy()
        
        data = self._apply_cutoffs(data)
        
        if data.empty:
            return data
        
        not_running_condition = data[const.RUNNING_TIME]
        data['condition_running_time'] = not_running_condition
        
        data["duration_in_seconds_without_conditions"] = (
            data["index"].diff().dt.total_seconds().fillna(0)
        )

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0).shift(-1)) * (~not_running_condition)
        )
        
        if self._multi_product:
            data = data.groupby(
                pd.Grouper(key="index", freq="1h"), dropna=False
            ).agg(
                {
                    const.TOTAL_PRODUCED: agg,
                    "duration_in_seconds": "sum",
                    const.PRODUCT_DESCRIPTION: lambda x: x.mode().iloc[0] if not x.mode().empty else np.nan,
                }
            )

            data = data.reset_index().rename(columns={"index": "timestamp"})

            data = data.loc[data.groupby("timestamp")[const.TOTAL_PRODUCED].idxmax()]
            
            data[const.RUNNING_TIME] = (
                data.groupby(pd.Grouper(key="timestamp", freq="1h"))["duration_in_seconds"].transform("sum") / 3600
            )
        else:
            data = (
                data.groupby(pd.Grouper(key="index", freq="D")).agg({
                    const.TOTAL_PRODUCED: agg,
                    "duration_in_seconds": "sum"
                })
            )

            data[const.TOTAL_PRODUCED] = data[const.TOTAL_PRODUCED].shift(shift_value)
            
            data = data.reset_index().rename(columns={"index": "timestamp"})
            
            data[const.RUNNING_TIME] = (
                data.groupby(pd.Grouper(key="timestamp", freq="D"))["duration_in_seconds"].transform("sum") / 3600
            )

        data.drop(columns=["duration_in_seconds"], inplace=True)
        
        data = self._add_msdp_values(data, msdp_columns)
        
        data = self._calculate_rlt_values(data)
        
        data = self._apply_filters(data, msdp_columns)
        
        data["start_time"] = data["timestamp"]
        data["end_time"] = data["timestamp"] + pd.Timedelta(days=1)
        
        hours_to_seconds_factor = 3600
        data[const.RLT] = data[const.RLT] * hours_to_seconds_factor
        data[const.RLT_NO_DEMAND] = (
            data[const.RLT_NO_DEMAND] * hours_to_seconds_factor
        )
        
        self._day_data = data
        
        return data

    
    def _apply_cutoffs(self, data: pd.DataFrame) -> pd.DataFrame:
        """Apply start and end cutoffs to remove partial days"""
        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        cutoff_time = time(0, 0, 0, 0)
        
        start_cutoff = (
            None if first_timestamp.time() == cutoff_time
            else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()
        
        return data[
            ((start_cutoff is not None) & (data["index"].dt.date > start_cutoff))
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]
    
    def _add_msdp_values(self, data: pd.DataFrame, msdp_columns: list[str]) -> pd.DataFrame:
        """Add MSDP and scheduled rate values"""
        data["Year"] = data["timestamp"].dt.year
        data["Month"] = data["timestamp"].dt.month
        data["Day"] = data["timestamp"].dt.day
        
        filter_msdp = (
            self._msdp["reportingLineExternalId"] == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        data[msdp_columns] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=msdp_columns,
            axis=1,
            result_type="expand",
        )
        
        return data
    
    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        if self._multi_product and self._reporting_line_external_id not in ["RLN-BISGURGUR"]:
            return get_msdp_product_filter(row, row[const.PRODUCT_DESCRIPTION], msdp_data, data_aux)

        return msdp_util_get_msdp_value(row, msdp_data, data_aux)

    def _build_rlt_schema(self) -> list[tuple[str, callable, dict[str, callable]]]:
        return [
            (
                "3a0",  # zero rlt when running time is zero
                lambda df: (
                    df[const.RUNNING_TIME] == 0
                ),
                {const.RLT: lambda df: pd.Series(0, index=df.index, dtype="float")},  # Return Series
            ),
            (
                "3b0",  # zero rlt when total produced equals msdp
                lambda df: (
                    (const.MSDP in df.columns) and
                    (df[const.MSDP].notna())
                    & (
                        (df[const.TOTAL_PRODUCED] - (df[const.MSDP] * df[const.RUNNING_TIME] / 24)).abs()
                        < const.TOLERANCE
                    )
                ),
                {const.RLT: lambda df: pd.Series(0, index=df.index, dtype="float")},  # Return Series
            ),
            (
                "3c0",  # single RLT calculation for specific lines
                lambda df: ((not self._line_uses_scheduled_rate)),
                {
                    const.RLT: lambda df: (
                        (
                            (df[const.MSDP] * df[const.RUNNING_TIME] / 24) - df[const.TOTAL_PRODUCED]
                        )
                        / (df[const.MSDP] / 24)
                    ) if (const.MSDP in df.columns) else pd.Series(0, index=df.index, dtype="float")
                },
            ),
            (
                "3a",
                lambda df: (
                    (const.MSDP in df.columns) and (const.SCHEDULED_RATE in df.columns) and
                    (df[const.MSDP].notna())
                    & (df[const.SCHEDULED_RATE].notna())
                    & (
                        (df[const.MSDP] - df[const.SCHEDULED_RATE]) 
                        > const.TOLERANCE
                    )
                    & (
                        ((df[const.SCHEDULED_RATE] * df[const.RUNNING_TIME] / 24) - df[const.TOTAL_PRODUCED])
                        > const.TOLERANCE
                    )
                ),
                {
                    const.RLT: lambda df: (
                        ((df[const.SCHEDULED_RATE] * df[const.RUNNING_TIME] / 24) - df[const.TOTAL_PRODUCED])
                        / (df[const.MSDP] / 24)
                    ) if (const.MSDP in df.columns and const.SCHEDULED_RATE in df.columns) else pd.Series(0, index=df.index, dtype="float"),
                    # {[MSDP * (Running Time / 24)] - [Scheduled Rate * (Running Time / 24)]} / {MSDP / 24}
                    # It can be written as: [1 - (Scheduled Rate / MSPD)] * Running Time
                    const.RLT_NO_DEMAND: lambda df: (
                        ((1 - (df[const.SCHEDULED_RATE] / df[const.MSDP])) * (df[const.RUNNING_TIME]))
                    ) if (const.MSDP in df.columns and const.SCHEDULED_RATE in df.columns) else pd.Series(0, index=df.index, dtype="float"),
                },
            ),
            (
                "3b",
                lambda df: (
                    (const.MSDP in df.columns) and (const.SCHEDULED_RATE in df.columns) and
                    (df[const.MSDP].notna())
                    & (df[const.SCHEDULED_RATE].notna())
                    & (
                        ((df[const.MSDP] * df[const.RUNNING_TIME] / 24) - df[const.TOTAL_PRODUCED])
                        > const.TOLERANCE
                    )
                    & (
                        (df[const.TOTAL_PRODUCED] - (df[const.SCHEDULED_RATE] * df[const.RUNNING_TIME] / 24))
                        > const.TOLERANCE
                    )
                ),
                {
                    # {[MSDP * (Running Time / 24)] - Net Production} / {MSDP / 24}
                    # It can be written as: Running Time - (24 * Net Production / MSDP)
                    const.RLT_NO_DEMAND: lambda df: (
                        ((df[const.RUNNING_TIME]) - (24 * df[const.TOTAL_PRODUCED] / df[const.MSDP]))
                    ) if (const.MSDP in df.columns) else pd.Series(0, index=df.index, dtype="float"),
                },
            ),
            (
                "3c",
                lambda df: (
                    (const.MSDP in df.columns) and
                    (df[const.MSDP].notna())
                    & (
                        (
                            (df[const.TOTAL_PRODUCED] - (df[const.MSDP] * df[const.RUNNING_TIME] / 24))
                            > const.TOLERANCE
                        )
                    )
                ),
                {
                    const.RLT: lambda df: (
                        (
                            (df[const.MSDP] * df[const.RUNNING_TIME] / 24) - df[const.TOTAL_PRODUCED]
                        )
                        / (df[const.MSDP] / 24)
                    ) if (const.MSDP in df.columns) else pd.Series(0, index=df.index, dtype="float")
                },
            ),
            (
                "3d",
                lambda df: (
                    (const.MSDP in df.columns)
                    & (const.SCHEDULED_RATE in df.columns)
                    & (df[const.MSDP].notna())
                    & (df[const.SCHEDULED_RATE].notna())
                    & (
                        (df[const.MSDP] - df[const.SCHEDULED_RATE]).abs()
                        < const.TOLERANCE
                    )
                    & (
                        ((df[const.SCHEDULED_RATE] * df[const.RUNNING_TIME] / 24) - df[const.TOTAL_PRODUCED])
                        > const.TOLERANCE
                    )
                ),
                {
                    const.RLT: lambda df: (
                        (
                            (df[const.SCHEDULED_RATE] * df[const.RUNNING_TIME] / 24)
                            - df[const.TOTAL_PRODUCED]
                        )
                        / (df[const.MSDP] / 24)
                    ) if (const.MSDP in df.columns and const.SCHEDULED_RATE in df.columns) else pd.Series(0, index=df.index, dtype="float")
                },
            ),
            (
                "3e",
                lambda df: (
                    (const.MSDP in df.columns)
                    & (const.SCHEDULED_RATE in df.columns)
                    & (df[const.MSDP].notna())
                    & (df[const.SCHEDULED_RATE].notna())
                    & (
                        (df[const.MSDP] - df[const.SCHEDULED_RATE])
                        > const.TOLERANCE
                    )
                    & (
                        ((df[const.SCHEDULED_RATE] * df[const.RUNNING_TIME] / 24) - df[const.TOTAL_PRODUCED]).abs()
                        < const.TOLERANCE
                    )
                ),
                {
                    # {[MSDP * (Running Time / 24)] - [Scheduled Rate * (Running Time / 24)]} / {MSDP / 24}
                    # It can be written as: [1 - (Scheduled Rate / MSPD)] * Running Time
                    const.RLT_NO_DEMAND: lambda df: (
                        ((1 - (df[const.SCHEDULED_RATE] / df[const.MSDP])) * (df[const.RUNNING_TIME]))
                    ) if (const.MSDP in df.columns and const.SCHEDULED_RATE in df.columns) else pd.Series(0, index=df.index, dtype="float")
                },
            ),
        ]
    
    def _calculate_rlt_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate RLT values using service-specific schema"""

        data = data.assign(
            **{
                const.RLT: pd.NA,
                const.RLT_NO_DEMAND: pd.NA,
                "rlt_condition": pd.NA,
            }
        )

        for rlt_condition_code, get_filter_mask, target_map in self._build_rlt_schema():
            mask = (
                get_filter_mask(data)
                & data[const.RLT].isna()
                & data[const.RLT_NO_DEMAND].isna()
            )
            if not mask.any():
                continue

            data.loc[mask, list(target_map.keys())] = pd.concat(
                {col: func(data.loc[mask]) for col, func in target_map.items()},
                axis=1,
            )
            data.loc[mask, "rlt_condition"] = rlt_condition_code

        return data.fillna({const.RLT: 0, const.RLT_NO_DEMAND: 0})
    
    def _apply_filters(self, data: pd.DataFrame, msdp_columns: list[str]) -> pd.DataFrame:
        data = data[data[const.MSDP] > 0.1]

        data = data[
            (data[const.TOTAL_PRODUCED] > 0.1)
            | ((data[const.TOTAL_PRODUCED] <= 0.1) & (data[const.RUNNING_TIME] > 0.25))
        ]
        
        # no events should be generated if running_time <= 15 minutes
        data = data[data[const.RUNNING_TIME] > 0.25]

        if data.shape[0] == 0:
            self._day_data = data
            return data.copy()
        
        if self._multi_product:
            data = self._apply_multi_product_filter(data, msdp_columns)
        
        if const.SCHEDULED_RATE in data.columns:
            msdp_eq_scheduled_rate_eq_total_produced = (
                ((data[const.TOTAL_PRODUCED] - (data[const.MSDP] * data[const.RUNNING_TIME] / 24)).abs() < const.TOLERANCE)
                & (
                    (data[const.TOTAL_PRODUCED] - (data[const.SCHEDULED_RATE] * data[const.RUNNING_TIME] / 24)).abs()
                    < const.TOLERANCE
                )
                & ((data[const.MSDP] - data[const.SCHEDULED_RATE]).abs() < const.TOLERANCE)
            )

            # no events should be generated if msdp = scheduled rate = total_produced
            data = data[~msdp_eq_scheduled_rate_eq_total_produced]

        data = data.dropna()
        
        return data
    
    def _apply_multi_product_filter(self, data: pd.DataFrame, msdp_columns: list[str]) -> pd.DataFrame:
        data["timestamp"] = data["timestamp"].dt.floor("D")

        agg_conditions = {
            const.PRODUCT_DESCRIPTION: lambda x: x.mode().iloc[0] if not x.mode().empty else np.nan,
            const.TOTAL_PRODUCED: "sum",
            const.RLT: "sum",
            const.RLT_NO_DEMAND: "sum",
            const.RUNNING_TIME: "sum",
        }

        condition = data[const.RLT_NO_DEMAND] > 0
        data_no_demand = data[condition].groupby("timestamp").agg(agg_conditions).reset_index()
        data = data[data[const.RLT] != 0].groupby("timestamp").agg(agg_conditions).reset_index()

        data_no_demand[const.RLT] = 0
        data[const.RLT_NO_DEMAND] = 0

        data = pd.concat([data, data_no_demand], ignore_index=True)

        # no events should be generated if total_produced <= 0
        data = data[data[const.TOTAL_PRODUCED] > 0]
        data["start_time"] = data["timestamp"]
        data["end_time"] = data["timestamp"] + pd.Timedelta(days=1)

        if data.shape[0] == 0:
            self._day_data = data
            return data.copy()
        
        data = self._add_msdp_values(data, msdp_columns)

        return data
