from typing import Any, Optional

import pandas as pd
from app.enums.inactive_value_enum import InactiveValueEnum
from app.enums.nar.prod_line_status import ProdLineStatusEnum
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.models.lead_product import LeadProductBbct
from app.utils.bbct_utils import get_bbct_value

class NarBatchEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "1e": self.identify_events_typeIe,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """

        # Event Start - Production Line Status Tag == IDLE (0)
        data = data.assign(
            event1a_start=(
                data["ProductionLineStatus"] == ProdLineStatusEnum.IDLE.value
            )
        )

        # Event End - Production Line Status Tag != IDLE (0)
        data = data.assign(
            event1a_end=(data["ProductionLineStatus"] != ProdLineStatusEnum.IDLE.value)
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
            & (data["event1a_start"].shift(1) != True)
            )
        ).assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ib

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """

        # Event Start - Production Line Status Tag == FAIL (25)
        data = data.assign(
            event1b_start=(
                data["ProductionLineStatus"] == ProdLineStatusEnum.FAIL.value
            )
        )

        # Event Start - Production Line Status Tag != FAIL (25)
        data = data.assign(
            event1b_end=(
                data["ProductionLineStatus"] != ProdLineStatusEnum.FAIL.value
            )
        )

                # correct start and end flags
        data = data.assign(
            event1b_start=(
                (data["event1b_start"] == True)
                & (data["event1b_start"].shift(1) != True)
            )
        ).assign(
            event1b_end=(
                (data["event1b_end"] == True)
                & (data["event1b_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ic

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """

        # Event Start - Production Line Status Tag == DOWN (2)
        data = data.assign(
            event1c_start=(
                data["ProductionLineStatus"] == ProdLineStatusEnum.DOWN.value
            )
        )

        # Event Start - Production Line Status Tag != DOWN (2)
        data = data.assign(
            event1c_end=(
                data["ProductionLineStatus"] != ProdLineStatusEnum.DOWN.value
            )
        )

        # correct start and end flags
        data = data.assign(
            event1c_start=(
                (data["event1c_start"] == True)
                & (data["event1c_start"].shift(1) != True)
            )
        ).assign(
            event1c_end=(
                (data["event1c_end"] == True)
                & (data["event1c_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeId(self):
        pass

    def identify_events_typeIe(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ic

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """

        # Event Start - Production Line Status Tag == START (1) or EMPTY (3)
        data = data.assign(
            event1e_start=(data["ProductionLineStatus"] == ProdLineStatusEnum.START.value) |
            (data["ProductionLineStatus"] == ProdLineStatusEnum.EMPTY.value)
        )

        # Event Start - Production Line Status Tag == PRECHILL1 (4)
        data = data.assign(
            event1e_end=(
                data["ProductionLineStatus"] == ProdLineStatusEnum.PRECHILL1.value
            )
        )

        # correct start and end flags
        data = data.assign(
            event1e_start=(
                (data["event1e_start"] == True)
                & (data["event1e_start"].shift(1) != True)
            )
        ).assign(
            event1e_end=(
                (data["event1e_end"] == True)
                & (data["event1e_end"].shift(1) != True)
            )
        )

        data = data[
            data["event1e_start"] 
            | data["event1e_end"] 
            | (data["ProductionLineStatus"] == ProdLineStatusEnum.FAIL.value)
        ]

        data["event1e_start"] = (
            data["event1e_start"] & (data["ProductionLineStatus"].shift(-1) != ProdLineStatusEnum.FAIL.value)
        )

        return data

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIIb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """

        # Event Start - Production Line Status Tag == START (1)
        data = data.assign(
            event3b_start=(
                (data["ProductionLineStatus"] == ProdLineStatusEnum.PRECHILL1.value)
            )
        )

        # Event End - Production Line Status Tag changes from "DISCHARGE" to "START" or "IDLE" or  " " 
        data = data.assign(
            event3b_end=(
                (data["ProductionLineStatus"] == ProdLineStatusEnum.START.value) |
                (data["ProductionLineStatus"] == ProdLineStatusEnum.IDLE.value) |
                (data["ProductionLineStatus"] == ProdLineStatusEnum.DISCHARGE.value)
            )
        )

        data = data[
            data["event3b_start"] 
            | data["event3b_end"] 
            | (data["ProductionLineStatus"] == ProdLineStatusEnum.FAIL.value)
        ]

        # correct start and end flags
        data = data.assign(
            event3b_start=(
                (data["event3b_start"] == True)
                & (data["event3b_start"].shift(1) != True)
            )
        ).assign(
            event3b_end=(
                (data["event3b_end"] == True)
                & (data["event3b_end"].shift(1) != True)
            )
        )

        data = data[
            data["event3b_start"] 
            | data["event3b_end"] 
            | (
                (data["ProductionLineStatus"] == ProdLineStatusEnum.FAIL.value) 
                & (data["ProductionLineStatus"].shift(1) != ProdLineStatusEnum.FAIL.value)
            )
        ]

        data["event3b_start"] = (
            data["event3b_start"] & (data["ProductionLineStatus"].shift(-1) != ProdLineStatusEnum.FAIL.value)
        )
        if not data.empty and data["event3b_start"].iloc[-1]:
            data = data.iloc[:-1]

        return data

    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IVa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """

        data = data.assign(
            event4a_start = (
                data["ProductionLineStatus"] == ProdLineStatusEnum.PRECHILL1.value
            )
        )

        data = data.assign(
            event4a_end = (
                data["ProductionLineStatus"] == ProdLineStatusEnum.FAIL.value
            )
        )

        data = data[
            (data["event4a_start"] == True) |
            (data["ProductionLineStatus"] == ProdLineStatusEnum.DISCHARGE.value) |
            (data["event4a_end"] == True)
        ]

        # correct start and end flags
        data = data.assign(
            event4a_start=(
                (data["event4a_start"] == True)
                & (data["event4a_start"].shift(1) != True)
            )
        ).assign(
            event4a_end=(
                (data["event4a_end"] == True)
                & (data["event4a_end"].shift(1) != True)
            )
        )

        data = data[
            (data["event4a_start"] == True) |
            (data["ProductionLineStatus"] == ProdLineStatusEnum.DISCHARGE.value) |
            (data["event4a_end"] == True)
        ]

        data["event4a_start"] = data["event4a_start"] & (data["ProductionLineStatus"].shift(-1) != ProdLineStatusEnum.DISCHARGE.value)
        data = data[(data["event4a_start"] == True) | (data["event4a_end"] == True)]
        if not data.empty and data["event4a_start"].iloc[-1]:
            data = data.iloc[:-1]

        return data
    
    def identify_events_typeIVb(self):
        pass

    def fix_3b_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame, lead_bbct: LeadProductBbct
    ) -> pd.DataFrame:
        """
        applies the business that demands subtraction of the
        Best Batch Cycle time from every event of type 3b

        :param data: event data
        :type data: pd.DataFrame
        :return: data with the right duration of the events
        :rtype: pd.DataFrame
        """
        # apply extraction of the BBCT value
        data["bbct"] = data.apply(
            get_bbct_value,
            bbct_data=bbct_data,
            reporting_line_external_id=self._reporting_line_external_id,
            reporting_site_configuration=self._reporting_site_configuration,
            axis=1,
        )

        # fix events 3b duration
        data.loc[data["event_definition"] == "3b", "total_duration_seconds"] -= data["bbct"]
        data.loc[(data["event_definition"] == "3b") & (data["bbct"] == 0), "total_duration_seconds"] = 0  # Explicitly set to 0

        data = data.query("total_duration_seconds != 0")

        # fill NaN values with 0 when data does not have BBCT
        data["total_duration_seconds"].fillna(0, inplace=True)

        # drop created column
        data.drop(columns=["bbct"], inplace=True)

        return data