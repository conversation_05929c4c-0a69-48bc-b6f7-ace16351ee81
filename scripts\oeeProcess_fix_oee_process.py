from datetime import datetime
import time
import logging
import os
import sys
import numpy as np
import pandas as pd
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables

"""
    ATENÇÃO: Este script deverá corrigir todas as Categorias OEE Process que estão inativas.
"""

# Definindo o caminho do script para encontrar os pacotes corretamente
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from oee_function.app.infra.logger_adapter import get_logger
from oee_function.app.repositories.process_repository import ProcessRepository
from oee_function.app.repositories.view_repository import ViewRepository

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def require_current_day():
    """
    Solicita ao usuário que insira o dia atual para liberar a execução do script.
    """
    today = datetime.now().strftime("%d")
    print("\nAtenção: Este processo é irreversivel para continuar, digite o dia atual (somente número): ")
    
    user_input = input("Dia atual: ").strip()
    
    if user_input != today:
        logger.error("Dia inserido incorreto. Execução abortada.")
        sys.exit(1)  # Encerra o programa com código de erro

    logger.info("Dia confirmado corretamente. Continuando execução.")

def create_cognite_client(variables: EnvVariables):
    """Cria uma instância do cliente Cognite usando as variáveis de ambiente."""
    return CogniteClientFactory.create(variables)

def create_repositories(variables: EnvVariables):
    """Inicializa os repositórios necessários para acessar os dados."""
    cognite_client = create_cognite_client(variables)
    data_model_id = DataModelId(
        variables.cognite.data_model_space,
        variables.cognite.data_model_external_id,
        variables.cognite.data_model_version,
    )
    return {
        "process": ProcessRepository(
            cognite_client,
            ViewRepository(cognite_client, data_model_id),
            data_model_id,
        ),
    }

def get_all_oee_process_inactive_data(variables: EnvVariables) -> pd.DataFrame:
    """Executa a query para buscar todos os processos OEE Inativos e retorna como DataFrame."""
    repositories = create_repositories(variables)
    process_repository = repositories.get("process")
    if process_repository is None:
        logger.error("ProcessRepository não encontrado.")
        return pd.DataFrame()

    data = process_repository.get_oee_process_inactive_data_as_dataframe()
    return data


def get_all_oee_process_active_data(variables: EnvVariables) -> pd.DataFrame:
    """Executa a query para buscar todos os processos OEE Ativos e retorna como DataFrame."""
    repositories = create_repositories(variables)
    process_repository = repositories.get("process")
    if process_repository is None:
        logger.error("ProcessRepository não encontrado.")
        return pd.DataFrame()

    data = process_repository.get_oee_process_active_data_as_dataframe()
    return data

def add_metric_code_column(data: pd.DataFrame) -> pd.DataFrame:
    """
    Adiciona uma nova coluna 'Metric Code' ao DataFrame. 
    O valor é copiado da coluna 'name' apenas para linhas onde 'OEECategory' é 'Metric Code'.
    """
    if "Metric Code" not in data.columns:
        data["Metric Code"] = np.where(data["OEECategory"] == "Metric Code", data["name"], None)
    return data

def add_event_code_with_metric_code(data: pd.DataFrame) -> pd.DataFrame:
    """
    Adiciona as colunas 'Event Code' e 'Metric Code' ao DataFrame.
    - 'Event Code': preenchido com o valor da coluna 'name' para linhas onde 'OEECategory' é 'Event Code'.
    - 'Metric Code': para linhas onde 'OEECategory' é 'Event Code', busca o valor do 'Metric Code' correspondente
      ao 'refOEEProcess.externalId' na coluna 'externalId'.
    """
    # Adicionar a coluna 'Event Code'
    if "Event Code" not in data.columns:
        data["Event Code"] = np.where(data["OEECategory"] == "Event Code", data["name"], None)
    
    # Adicionar ou atualizar a coluna 'Metric Code' para 'Event Code'
    if "Metric Code" not in data.columns:
        data["Metric Code"] = None

    for index, row in data.iterrows():
        if row["OEECategory"] == "Event Code":
            # Obter o refOEEProcess, ignorando linhas onde está ausente ou inválido
            ref_oee_process = row.get("refOEEProcess")
            
            if ref_oee_process and isinstance(ref_oee_process, dict):
                ref_external_id = ref_oee_process.get("externalId")
                
                if ref_external_id:
                    # Localizar a linha correspondente no DataFrame
                    metric_row = data[data["externalId"] == ref_external_id]
                    
                    if not metric_row.empty:
                        # Preencher o Metric Code
                        data.at[index, "Metric Code"] = metric_row.iloc[0]["name"]

    return data

def add_subcat_level1_with_event_and_metric_code(data: pd.DataFrame) -> pd.DataFrame:
    """
    Adiciona as colunas 'Subcat Level 1', 'Metric Code' e 'Event Code' ao DataFrame para linhas com 'OEECategory' igual a 'Subcat Level 1'.
    - 'Subcat Level 1': preenchido com o valor de 'name'.
    - 'Metric Code': obtido a partir do 'Metric Code' do 'Event Code' referenciado por 'refOEEProcess'.
    - 'Event Code': obtido a partir do 'name' do 'Event Code' referenciado por 'refOEEProcess'.
    """
    # Adicionar ou atualizar as colunas necessárias
    if "Subcat Level 1" not in data.columns:
        data["Subcat Level 1"] = None
    if "Metric Code" not in data.columns:
        data["Metric Code"] = None
    if "Event Code" not in data.columns:
        data["Event Code"] = None

    # Iterar pelas linhas do DataFrame
    for index, row in data.iterrows():
        if row["OEECategory"] == "Subcat Level 1":
            # Preencher a coluna 'Subcat Level 1' com o valor de 'name'
            data.at[index, "Subcat Level 1"] = row["name"]

            # Obter o refOEEProcess, ignorando linhas onde está ausente ou inválido
            ref_oee_process = row.get("refOEEProcess")
            
            if ref_oee_process and isinstance(ref_oee_process, dict):
                ref_external_id = ref_oee_process.get("externalId")
                
                if ref_external_id:
                    # Localizar a linha correspondente no DataFrame
                    event_row = data[(data["externalId"] == ref_external_id) & (data["OEECategory"] == "Event Code")]
                    
                    if not event_row.empty:
                        # Preencher o Metric Code e o Event Code
                        data.at[index, "Metric Code"] = event_row.iloc[0]["Metric Code"]
                        data.at[index, "Event Code"] = event_row.iloc[0]["name"]

    return data

def add_subcat_level2_with_event_and_metric_code(data: pd.DataFrame) -> pd.DataFrame:
    """
    Adiciona as colunas 'Subcat Level 2', 'Metric Code' e 'Event Code' ao DataFrame para linhas com 'OEECategory' igual a 'Subcat Level 2'.
    - 'Subcat Level 2': preenchido com o valor de 'name'.
    - 'Metric Code': herdado do 'Metric Code' do 'Subcat Level 1' referenciado por 'refOEEProcess'.
    - 'Event Code': herdado do 'Event Code' do 'Subcat Level 1' referenciado por 'refOEEProcess'.
    """
    # Adicionar ou atualizar as colunas necessárias
    if "Subcat Level 2" not in data.columns:
        data["Subcat Level 2"] = None
    if "Metric Code" not in data.columns:
        data["Metric Code"] = None
    if "Event Code" not in data.columns:
        data["Event Code"] = None

    # Iterar pelas linhas do DataFrame
    for index, row in data.iterrows():
        if row["OEECategory"] == "Subcat Level 2":
            # Preencher a coluna 'Subcat Level 2' com o valor de 'name'
            data.at[index, "Subcat Level 2"] = row["name"]

            # Obter o refOEEProcess, ignorando linhas onde está ausente ou inválido
            ref_oee_process = row.get("refOEEProcess")
            
            if ref_oee_process and isinstance(ref_oee_process, dict):
                ref_external_id = ref_oee_process.get("externalId")
                
                if ref_external_id:
                    # Localizar a linha correspondente no DataFrame
                    subcat1_row = data[(data["externalId"] == ref_external_id) & (data["OEECategory"] == "Subcat Level 1")]
                    
                    if not subcat1_row.empty:
                        # Preencher o Metric Code e o Event Code
                        data.at[index, "Metric Code"] = subcat1_row.iloc[0]["Metric Code"]
                        data.at[index, "Event Code"] = subcat1_row.iloc[0]["Event Code"]
                        data.at[index, "Subcat Level 1"] = subcat1_row.iloc[0]["Subcat Level 1"]


    return data

def add_subcat_level3_with_event_and_metric_code(data: pd.DataFrame) -> pd.DataFrame:
    """
    Adiciona as colunas 'Subcat Level 3', 'Metric Code', 'Event Code', e os níveis anteriores ao DataFrame.
    - 'Subcat Level 3': preenchido com o valor de 'name'.
    - 'Metric Code': herdado do 'Metric Code' do 'Subcat Level 2' referenciado por 'refOEEProcess'.
    - 'Event Code': herdado do 'Event Code' do 'Subcat Level 2' referenciado por 'refOEEProcess'.
    - 'Subcat Level 1' e 'Subcat Level 2': herdados do 'Subcat Level 2'.
    """
    if "Subcat Level 3" not in data.columns:
        data["Subcat Level 3"] = None
    if "Subcat Level 2" not in data.columns:
        data["Subcat Level 2"] = None
    if "Subcat Level 1" not in data.columns:
        data["Subcat Level 1"] = None
    if "Metric Code" not in data.columns:
        data["Metric Code"] = None
    if "Event Code" not in data.columns:
        data["Event Code"] = None

    for index, row in data.iterrows():
        if row["OEECategory"] == "Subcat Level 3":
            data.at[index, "Subcat Level 3"] = row["name"]

            ref_oee_process = row.get("refOEEProcess")
            if ref_oee_process and isinstance(ref_oee_process, dict):
                ref_external_id = ref_oee_process.get("externalId")
                if ref_external_id:
                    subcat2_row = data[
                        (data["externalId"] == ref_external_id) & (data["OEECategory"] == "Subcat Level 2")
                    ]
                    if not subcat2_row.empty:
                        data.at[index, "Metric Code"] = subcat2_row.iloc[0]["Metric Code"]
                        data.at[index, "Event Code"] = subcat2_row.iloc[0]["Event Code"]
                        data.at[index, "Subcat Level 1"] = subcat2_row.iloc[0]["Subcat Level 1"]
                        data.at[index, "Subcat Level 2"] = subcat2_row.iloc[0]["Subcat Level 2"]

    return data

def add_subcat_level4_with_event_and_metric_code(data: pd.DataFrame) -> pd.DataFrame:
    """
    Adiciona as colunas 'Subcat Level 4', 'Metric Code', 'Event Code', e os níveis anteriores ao DataFrame.
    - 'Subcat Level 4': preenchido com o valor de 'name'.
    - 'Metric Code': herdado do 'Metric Code' do 'Subcat Level 3' referenciado por 'refOEEProcess'.
    - 'Event Code': herdado do 'Event Code' do 'Subcat Level 3' referenciado por 'refOEEProcess'.
    - 'Subcat Level 1', 'Subcat Level 2' e 'Subcat Level 3': herdados do 'Subcat Level 3'.
    """
    if "Subcat Level 4" not in data.columns:
        data["Subcat Level 4"] = None
    if "Subcat Level 3" not in data.columns:
        data["Subcat Level 3"] = None
    if "Subcat Level 2" not in data.columns:
        data["Subcat Level 2"] = None
    if "Subcat Level 1" not in data.columns:
        data["Subcat Level 1"] = None
    if "Metric Code" not in data.columns:
        data["Metric Code"] = None
    if "Event Code" not in data.columns:
        data["Event Code"] = None

    for index, row in data.iterrows():
        if row["OEECategory"] == "Subcat Level 4":
            data.at[index, "Subcat Level 4"] = row["name"]

            ref_oee_process = row.get("refOEEProcess")
            if ref_oee_process and isinstance(ref_oee_process, dict):
                ref_external_id = ref_oee_process.get("externalId")
                if ref_external_id:
                    subcat3_row = data[
                        (data["externalId"] == ref_external_id) & (data["OEECategory"] == "Subcat Level 3")
                    ]
                    if not subcat3_row.empty:
                        data.at[index, "Metric Code"] = subcat3_row.iloc[0]["Metric Code"]
                        data.at[index, "Event Code"] = subcat3_row.iloc[0]["Event Code"]
                        data.at[index, "Subcat Level 1"] = subcat3_row.iloc[0]["Subcat Level 1"]
                        data.at[index, "Subcat Level 2"] = subcat3_row.iloc[0]["Subcat Level 2"]
                        data.at[index, "Subcat Level 3"] = subcat3_row.iloc[0]["Subcat Level 3"]

    return data

def add_hierarchy_column(data: pd.DataFrame) -> pd.DataFrame:
    """
    Adiciona a coluna 'hierarchy' ao DataFrame, concatenando as colunas
    'Metric Code', 'Event Code', 'Subcat Level 1', 'Subcat Level 2', 'Subcat Level 3', 'Subcat Level 4'.
    - Formato: "metric_code > event_code > subcat level 1 > subcat level 2 > subcat level 3 > subcat level 4".
    - Valores nulos são ignorados na hierarquia.
    """
    # Certificar-se de que a coluna exista
    if "hierarchy" not in data.columns:
        data["hierarchy"] = data.apply(
            lambda row: ">".join(
                str(row[col]) for col in [
                    "Metric Code", "Event Code", "Subcat Level 1", "Subcat Level 2", "Subcat Level 3", "Subcat Level 4"
                ] if pd.notnull(row[col])
            ),
            axis=1
        )
    return data

def group_hierarchy_and_summarize_units(data: pd.DataFrame) -> pd.DataFrame:
    """
    Agrupa o DataFrame com base na coluna 'hierarchy' e sumariza as unidades ('refUnits').
    - As unidades de cada grupo são combinadas em uma única lista.
    - Outras colunas (se necessário) são mantidas intactas para as hierarquias agrupadas.
    """
    # Garantir que 'hierarchy' e 'refUnits' existam no DataFrame
    if "hierarchy" not in data.columns or "refUnits" not in data.columns:
        raise ValueError("As colunas 'hierarchy' e 'refUnits' são necessárias para o agrupamento.")

    # Definir a função de sumarização para as unidades
    def summarize_units(units):
        combined_units = set()
        for unit_list in units.dropna():
            for unit in unit_list.get("items", []):
                combined_units.add((unit["externalId"], unit["space"], unit["name"]))
        # Retornar como uma lista de dicionários
        return [{"externalId": u[0], "space": u[1], "name": u[2]} for u in combined_units]

    # Agrupar por 'hierarchy' e sumarizar 'refUnits'
    grouped_data = data.groupby("hierarchy").agg({
        "refUnits": summarize_units,
        # Para outras colunas, escolha a primeira ocorrência (ou ajuste conforme necessário)
        "new_externalId": "first",
        "name": "first",
        "OEECategory": "first",
        "Metric Code": "first",
        "Event Code": "first",
        "Subcat Level 1": "first",
        "Subcat Level 2": "first",
        "Subcat Level 3": "first",
        "Subcat Level 4": "first"
    }).reset_index()

    return grouped_data

def add_external_id_column(data: pd.DataFrame) -> pd.DataFrame:
    """
    Adiciona a coluna 'externalId' ao DataFrame, baseada na coluna 'hierarchy'.
    - Adiciona o sufixo 'OEPR'.
    - Remove os espaços da hierarquia.
    - Substitui o caracter '>' por '-'.
    """
    if "hierarchy" not in data.columns:
        raise ValueError("A coluna 'hierarchy' é necessária para criar 'externalId'.")

    # Criar a coluna 'externalId'
    data["new_externalId"] = data["hierarchy"].apply(
        lambda x: f"OEPR-{x.replace(' ', '').replace('>', '-')}" if pd.notnull(x) else None
    )

    return data

def add_ref_oee_process_column(data: pd.DataFrame) -> pd.DataFrame:
    """
    Adiciona a coluna 'refOeeProcess' ao DataFrame com o valor de 'new_externalId' do elemento pai,
    considerando toda a hierarquia e a categoria do elemento pai.
    """
    # Certificar-se de que as colunas necessárias existam
    if "hierarchy" not in data.columns or "new_externalId" not in data.columns or "OEECategory" not in data.columns:
        raise ValueError("As colunas 'hierarchy', 'new_externalId' e 'OEECategory' são necessárias para criar 'refOeeProcess'.")

    # Criar ou inicializar a coluna 'refOeeProcess'
    data["refOeeProcess"] = None

    for index, row in data.iterrows():
        # Obter a hierarquia atual
        current_hierarchy = row["hierarchy"]
        category = row["OEECategory"]

        # Determinar a hierarquia do pai e a categoria esperada do pai
        if category == "Subcat Level 4":
            parent_category = "Subcat Level 3"
        elif category == "Subcat Level 3":
            parent_category = "Subcat Level 2"
        elif category == "Subcat Level 2":
            parent_category = "Subcat Level 1"
        elif category == "Subcat Level 1":
            parent_category = "Event Code"
        elif category == "Event Code":
            parent_category = "Metric Code"
        else:
            parent_category = None

        if parent_category:
            # Remover o último nível da hierarquia para encontrar o pai
            parent_hierarchy = ">".join(current_hierarchy.split(">")[:-1])
            parent_row = data[
                (data["hierarchy"] == parent_hierarchy) & (data["OEECategory"] == parent_category)
            ]
            if not parent_row.empty:
                # Preencher 'refOeeProcess' com o 'new_externalId' do pai
                data.at[index, "refOeeProcess"] = parent_row.iloc[0]["new_externalId"]

    return data


def run():
    """
    Script para coletar todos os processos OEE e exibir os dados.
    """
    
    require_current_day()
    
    variables = EnvVariables()    
    log = get_logger()
    
    log.info("Inicializando Fix de OEE Process...")

    # Pega todas as categorias inativas
    log.info("Capturando todos OEEProcess Inativos...")
    oee_process_data = get_all_oee_process_inactive_data(variables)

    # Aplica os valores de metric codes, event codes, subcat level 1, level 2 e level 3
    log.info("Tratando Metric Codes...")
    oee_process_data = add_metric_code_column(oee_process_data)
    
    log.info("Tratando Event Codes...")
    oee_process_data = add_event_code_with_metric_code(oee_process_data)
    
    log.info("Tratando Subcat Level 1...")
    oee_process_data = add_subcat_level1_with_event_and_metric_code(oee_process_data)
    
    log.info("Tratando Subcat Level 2...")
    oee_process_data = add_subcat_level2_with_event_and_metric_code(oee_process_data)
    
    log.info("Tratando Subcat Level 3...")
    oee_process_data = add_subcat_level3_with_event_and_metric_code(oee_process_data)
    
    log.info("Tratando Subcat Level 4...")
    oee_process_data = add_subcat_level4_with_event_and_metric_code(oee_process_data)
    
    # Criar a coluna 'hierarchy'
    log.info("Criando coluna de Hierarquia para analise...")
    oee_process_data = add_hierarchy_column(oee_process_data)

    # apply new external ID(s)
    log.info("Gerando novos external IDs...")
    oee_process_data = add_external_id_column(oee_process_data)

    # Agrupar e sumarizar unidades
    log.info("Agrupando e sumarizando hierarquias iguais")
    oee_process_data = group_hierarchy_and_summarize_units(oee_process_data)

    # Criar a coluna 'refOeeProcess'
    log.info("Criando vinculo entre processos (Categorias pais e categoria filhos)...")
    oee_process_data = add_ref_oee_process_column(oee_process_data)
    
    # Busca os OEE Process Ativos para inativa-los'
    log.info("Capturando todos OEEProcess Ativos...")
    oee_process_active_data = get_all_oee_process_active_data(variables)
    
    if not oee_process_active_data.empty:
        log.info("Inativando OEEProcess Ativos...")
        repositories = create_repositories(variables)
        process_repository = repositories.get("process")
        
        process_repository.inactivate_oee_processes(oee_process_active_data)
        log.info("OEEProcess Ativos inativados com sucesso.")

    
    if oee_process_data.empty:
        logger.info("Nenhum dado encontrado para os parâmetros fornecidos.")
    else:
        logger.info("Dados de processos OEE montados...")
        
        # Quality Data
        logger.info("Tratando dados, removendo dados invalidos")
        oee_process_data = oee_process_data.dropna(subset=["Metric Code"])
        
        output_path = os.path.join(SCRIPT_DIR, "oee_process_data.xlsx")
        oee_process_data.to_excel(output_path, index=False)
        logger.info(f"Dados exportados para {output_path}")

        # Criar repositórios
        repositories = create_repositories(variables)
        process_repository = repositories.get("process")
        
        # Verificar duplicados na coluna 'new_externalId'
        duplicated_external_ids = oee_process_data[oee_process_data["new_externalId"].duplicated(keep=False)]

        if not duplicated_external_ids.empty:
            logger.warning("Foram encontrados externalIds duplicados. O processamento será interrompido.")
            logger.info("ExternalIds duplicados encontrados:")
            logger.info(duplicated_external_ids[["new_externalId", "hierarchy"]])
            # Opcional: Levantar uma exceção para parar a execução
            oee_process_data = oee_process_data.drop_duplicates(subset=["new_externalId"], keep="first")

        duplicated_external_ids = oee_process_data[oee_process_data["new_externalId"].duplicated(keep=False)]

        if not duplicated_external_ids.empty:
            logger.warning("Ainda existem externalIds duplicados após a tentativa de remoção.")
        else:
            logger.info("Duplicados removidos com sucesso.")
        
        
        # Salvar os processos OEE
        logger.info("Criando categorias...")
        process_repository.create_oee_processes(oee_process_data)
        
        time.sleep(10)

        # Criar os edges entre processos e unidades
        logger.info("Vinculando as unidades com as categorias")
        process_repository.create_edges_between_processes_and_units(oee_process_data)

        
        # GERAR UM EXCEL COMO QUE DEVERA SER SALVO
        
        logger.info("Script concluído com sucesso. Todas as categorias OEE foram processadas e vinculadas.")


if __name__ == "__main__":
    run()
