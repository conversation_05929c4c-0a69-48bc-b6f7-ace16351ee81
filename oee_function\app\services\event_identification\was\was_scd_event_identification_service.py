import pandas as pd
from typing import Any, Optional

from app.models.lead_product import LeadProduct
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.was.washington_works_event_identification_service import (
    WashingtonWorksEventIdentificationService,
)
from app.utils.event_detection_utils import keep_only_first_occurrence, pair_start_end_indexes

class WASSCDSettings:
    """
    Settings for WASSCD event identification.
    """
    def __init__(self, reporting_line_external_id: str, mdr: pd.DataFrame):
        self.reporting_line_external_id = reporting_line_external_id
        self._mdr = mdr
        self._threshold = 1
    
    def get_not_running_conditions(self, data: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when system is in not running state.
        """
        return (
            (
                self.get_transition_conditions(data)
            ) | (
                (data["ExtMtrRunning"] == 0)
                & (data["ManualDivert"] == 0)
            ) | (
                (data["CampaignStatus"] == 0)
                & (data["ManualDivert"] == 0)
            ) | (
                (data["ExtruderFlowMeter"] < self._threshold)
                & (data["ManualDivert"] == 0)
            )
        )
        
    def get_transition_conditions(self, data: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when system is in transition state.
        """
        
        products_pre_defined_list = self._mdr["piTagValue"].tolist()
        
        return (
            (data["CampaignStatus"] == 99)
            & (data["ManualDivert"] == 0)
            & (data["ProductDescription"].isin(products_pre_defined_list))
        )
        
    def get_manual_divert_idle_condition(self, data: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when system is in manual divert idle state.
        """
        return (
            (data["ExtMtrRunning"] == 0)
            & (data["ManualDivert"] == 1)
        )
        
    def get_producing_waste_condition(self, data: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when system is in producing waste state.
        """
        return (
            (data["CampaignStatus"] == 1)
            & (data["ExtMtrRunning"] != 0)
            & (data["ExtruderFlowMeter"] >= 1)
            & (data["ProductDescription"].isin(self._mdr["piTagValue"]))
        )
    
    def not_running(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        identifies the not running events

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each not running event
        :rtype: pd.DataFrame
        """
        data = data.assign(
            event1a_start=(
                self.get_not_running_conditions(data)
                & (data["ProductDescription"].isin(self._mdr["piTagValue"]))
            ),
            event1a_end=(
                ~self.get_not_running_conditions(data)
                | (~data["ProductDescription"].isin(self._mdr["piTagValue"]))
            )
        )

        # fix start and end
        data["event1a_start"] = keep_only_first_occurrence(data, "event1a_start")
        data["event1a_end"] = keep_only_first_occurrence(data, "event1a_end")
        
        data["not_running"] = False
        pair_start_end_indexes(data, "event1a_start", "event1a_end", "not_running", True)

        return data

    def manual_divert(self, data: pd.DataFrame) -> pd.DataFrame:
        data = data.assign(
            event2a_start=(
                self.get_manual_divert_idle_condition(data)
                & (data["ProductDescription"].isin(self._mdr["piTagValue"]))
            ),
            event2a_end=(
                ~self.get_manual_divert_idle_condition(data)
                | (~data["ProductDescription"].isin(self._mdr["piTagValue"]))
            )
        )
        
        # fix start and end
        data["event2a_start"] = keep_only_first_occurrence(data, "event2a_start")
        data["event2a_end"] = keep_only_first_occurrence(data, "event2a_end")
        
        data["manual_divert_idle"] = False
        pair_start_end_indexes(data, "event2a_start", "event2a_end", "manual_divert_idle", True)

        return data
    
    def not_running_start_fn(self, df: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when not running event starts.

        Start Trigger for Event Type 1:
        {(CampaignStatus = 99 AND MANUAL_DIVERT = 0) OR
         (Ext_Mtr_Running = 0 AND MANUAL_DIVERT = 0) OR
         (CampaignStatus = 0 AND MANUAL_DIVERT = 0) OR
         (Extruder Flow Meter < threshold AND MANUAL_DIVERT = 0)}
        AND Product Description Tag reports a Product code that is ON a predefined list

        :param df: time series data with the status of the line
        :type df: pd.DataFrame
        :return: boolean series indicating start of not running events
        :rtype: pd.Series
        """
        not_running_conditions = self.get_not_running_conditions(df)
        return (
            not_running_conditions
            & (df["ProductDescription"].isin(self._mdr["piTagValue"]))
        )

    def not_running_end_fn(self, df: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when not running event ends.

        End Trigger for Event Type 1:
        {if any signal of the activating pair changes, i.e.,
         (CampaignStatus != 99 OR/AND MANUAL_DIVERT != 0) AND
         (Ext_Mtr_Running != 0 OR/AND MANUAL_DIVERT != 0) AND
         (CampaignStatus != 0 OR/AND MANUAL_DIVERT != 0) AND
         (Extruder Flow Meter >= threshold AND/OR MANUAL_DIVERT != 0)}
        or Product Description Tag reports a Product code that is NOT on a predefined list

        :param df: time series data with the status of the line
        :type df: pd.DataFrame
        :return: boolean series indicating end of not running events
        :rtype: pd.Series
        """
        not_running_conditions = self.get_not_running_conditions(df)
        not_running_conditions_changed = ~not_running_conditions
        product_not_in_list = ~df["ProductDescription"].isin(self._mdr["piTagValue"])

        return (
            not_running_conditions_changed
            | product_not_in_list
        )

    def not_running_during_product_trial_start_fn(self, df: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when not running during product trial event starts.

        This covers events 2e and 2f where the system is not running (or in manual divert idle)
        AND the product is NOT in the predefined list.

        :param df: time series data with the status of the line
        :type df: pd.DataFrame
        :return: boolean series indicating start of not running during product trial events
        :rtype: pd.Series
        """
        return (
            (
                self.get_not_running_conditions(df)
                | self.get_manual_divert_idle_condition(df)
            ) & (
                (df["ProductDescription"].notna())
                & (~df["ProductDescription"].isin(self._mdr["piTagValue"]))
            )
        )

    def not_running_during_product_trial_end_fn(self, df: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when not running during product trial event ends.

        :param df: time series data with the status of the line
        :type df: pd.DataFrame
        :return: boolean series indicating end of not running during product trial events
        :rtype: pd.Series
        """
        return (
            ~(
                self.get_not_running_conditions(df)
                | self.get_manual_divert_idle_condition(df)
            )
            | (df["ProductDescription"].isin(self._mdr["piTagValue"]))
        )

    def producing_waste_start_fn(self, df: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when producing waste event starts (Event 4a).

        :param df: time series data with the status of the line
        :type df: pd.DataFrame
        :return: boolean series indicating start of producing waste events
        :rtype: pd.Series
        """
        return (
            (self.get_producing_waste_condition(df))
            & (df["ManualDivert"] == 0)
        )

    def producing_waste_end_fn(self, df: pd.DataFrame) -> pd.Series:
        """
        Returns boolean series indicating when producing waste event ends (Event 4a).

        :param df: time series data with the status of the line
        :type df: pd.DataFrame
        :return: boolean series indicating end of producing waste events
        :rtype: pd.Series
        """
        return (
            (~self.get_producing_waste_condition(df))
            | (df["ManualDivert"] != 0)
        )

    def prepare_hourly_data(
        self, data: pd.DataFrame
    ) -> pd.DataFrame:
        data = self.not_running(data)
        data = self.manual_divert(data)

        data["isRunning"] = (~data["not_running"]) & (~data["manual_divert_idle"]) & (data["ProductDescription"].isin(self._mdr["piTagValue"]))

        return data

class WASSCDEventIdentificationService(WashingtonWorksEventIdentificationService):
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
        product_transition: Optional[dict[str, dict[str, dict[str, int]]]],
        lead_products: Optional[list[LeadProduct]] = None,

    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._product_transition = product_transition

        self._mdr = mdr
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._threshold = 1

        self._was_scd_settings = WASSCDSettings(
            reporting_line_external_id=reporting_line_external_id,
            mdr=mdr,
        )
        self._products_pre_defined_set: set[str] = self.extract_products_list_from_mdr(
            mdr
        )

        # Get lead product for this reporting line (needed for event 4c calculations)
        self._lead_product = next(
            (
                x
                for x in (lead_products or [])
                if x.reporting_line.external_id == reporting_line_external_id
            ),
            None,
        )

        # Cache variable for not running with transitions
        self._not_running_with_transitions = pd.DataFrame()

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1c": self.identify_events_typeIc,
            "2a": self.identify_events_typeIIa,
            "2c": self.identify_events_typeIIc,
            "2e": self.identify_events_typeIIe,
            "2f": self.identify_events_typeIIf,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
            "4c": self.identify_events_typeIVc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        processed_data = self.process_not_running_with_transitions(data)

        if processed_data.empty:
            return data

        if "event1c_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1c_start"], inplace=True)

        if "event1c_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1c_end"], inplace=True)

        return processed_data
    
    def identify_events_typeIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ic
        
        For SCD, event 1c is only created if the not running was activated by
        transition condition (CampaignStatus = 99 AND MANUAL_DIVERT = 0).
        
        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        
        processed_data = self.process_not_running_with_transitions(data)

        if processed_data.empty:
            return data

        if "event1a_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1a_start"], inplace=True)

        if "event1a_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1a_end"], inplace=True)

        if "event1c_start" in processed_data.columns.to_list():
            processed_data = processed_data.assign(
                event1c_start=(
                    processed_data["event1c_start"]
                    & (processed_data["CampaignStatus"] == 99)
                    & (processed_data["ManualDivert"] == 0)
                )
            )

            if "event1c_end" in processed_data.columns.to_list():
                valid_starts = processed_data[processed_data["event1c_start"] == True]["index"]

                for idx in processed_data[processed_data["event1c_end"] == True].index:
                    event1c_end_time = processed_data.loc[idx, "index"]
                    has_valid_start_before = (valid_starts < event1c_end_time).any()
                    if not has_valid_start_before:
                        processed_data.loc[idx, "event1c_end"] = False

        return processed_data

    def process_not_running_with_transitions(self, df: pd.DataFrame) -> pd.DataFrame:
        if not self._not_running_with_transitions.empty:
            return self._not_running_with_transitions.copy()

        self._not_running_with_transitions: pd.DataFrame = (
            self.identify_not_running_with_transitions(
                self._reporting_line_external_id,
                df,
                self._was_scd_settings.not_running_start_fn,
                self._was_scd_settings.not_running_end_fn,
                self._products_pre_defined_set,
                self._product_transition,
                48,
            )
        )

        return self._not_running_with_transitions.copy()
    
    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        
        data = self._was_scd_settings.manual_divert(data)
        
        return data
    
    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        data = data.assign(
            event2c_start=(
                ~self._was_scd_settings.get_not_running_conditions(data)
                & ~self._was_scd_settings.get_manual_divert_idle_condition(data)
                & (data["ProductDescription"].notna())
                & (~data["ProductDescription"].isin(self._mdr["piTagValue"]))
            )
        )

        data = data.assign(
            event2c_end=(
                self._was_scd_settings.get_not_running_conditions(data)
                | self._was_scd_settings.get_manual_divert_idle_condition(data)
                | (data["ProductDescription"].isin(self._mdr["piTagValue"]))
            )
        )
        
        data["event2c_start"] = keep_only_first_occurrence(data, "event2c_start")
        data["event2c_end"] = keep_only_first_occurrence(data, "event2c_end")
        
        return data
    
    def identify_events_typeIIe(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        data = data.assign(
            event2e_start=(
                (
                    self._was_scd_settings.get_not_running_conditions(data)
                    | self._was_scd_settings.get_manual_divert_idle_condition(data)
                ) & (
                    (data["ProductDescription"].notna())
                    & (~data["ProductDescription"].isin(self._mdr["piTagValue"]))
                )
            )
        )

        data = data.assign(
            event2e_end=(
                ~(
                    self._was_scd_settings.get_not_running_conditions(data)
                    | self._was_scd_settings.get_manual_divert_idle_condition(data)
                )
                | (data["ProductDescription"].isin(self._mdr["piTagValue"]))
            )
        )
        
        data["event2e_start"] = keep_only_first_occurrence(data, "event2e_start")
        data["event2e_end"] = keep_only_first_occurrence(data, "event2e_end")
        
        data = data[data["event2e_start"] | data["event2e_end"]]

        data = data.assign(
            event2e_start=(
                (data["event2e_start"])
                & (data["ProductDescription"] == data["ProductDescription"].shift(-1))
            )
        )
        
        data = data.assign(
            event2e_end=(
                (data["event2e_end"])
                & (data["ProductDescription"] == data["ProductDescription"].shift(1))
            )
        )
        
        return data
    
    def identify_events_typeIIf(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        data = data.assign(
            event2f_start=(
                (
                    self._was_scd_settings.get_not_running_conditions(data)
                    | self._was_scd_settings.get_manual_divert_idle_condition(data)
                ) & (
                    (data["ProductDescription"].notna())
                    & (~data["ProductDescription"].isin(self._mdr["piTagValue"]))
                )
            )
        )

        data = data.assign(
            event2f_end=(
                ~(
                    self._was_scd_settings.get_not_running_conditions(data)
                    | self._was_scd_settings.get_manual_divert_idle_condition(data)
                )
                | (data["ProductDescription"].isin(self._mdr["piTagValue"]))
            )
        )
        
        data["event2f_start"] = keep_only_first_occurrence(data, "event2f_start")
        data["event2f_end"] = keep_only_first_occurrence(data, "event2f_end")
        
        data = data[data["event2f_start"] | data["event2f_end"]]

        data = data.assign(
            event2f_start=(
                (data["event2f_start"])
                & (data["ProductDescription"].shift(-1).notna())
                & (data["ProductDescription"] != data["ProductDescription"].shift(-1))
            )
        )
        
        data = data.assign(
            event2f_end=(
                (data["event2f_end"])
                & (data["ProductDescription"] != data["ProductDescription"].shift(1))
            )
        )
        
        return data
    
    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        data = data.assign(
            event4a_start=(
                (self._was_scd_settings.get_producing_waste_condition(data))
                & (data["ManualDivert"] == 0)
            ),
            event4a_end=(
                (~self._was_scd_settings.get_producing_waste_condition(data))
                | (data["ManualDivert"] != 0)
            )
        )
        
        data["event4a_start"] = keep_only_first_occurrence(data, "event4a_start")
        data["event4a_end"] = keep_only_first_occurrence(data, "event4a_end")
        
        return data
    
    def identify_events_typeIVb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        data = data.assign(
            event4b_start=(
                (self._was_scd_settings.get_producing_waste_condition(data))
                & (data["ManualDivert"] == 1)
            ),
            event4b_end=(
                (~self._was_scd_settings.get_producing_waste_condition(data))
                | (data["ManualDivert"] != 1)
            )
        )
        
        data["event4b_start"] = keep_only_first_occurrence(data, "event4b_start")
        data["event4b_end"] = keep_only_first_occurrence(data, "event4b_end")

        return data

    def identify_events_typeIVc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        Identify Producing Waste by Quality Control events (type IVc).

        This method calculates the scrap product quantity based on DivertTotalizer values
        across shifts, following the same logic as MPW's event 4b but using only DivertTotalizer.

        The calculation excludes periods when the following events are active:
        - Event 1a/1c: Not Running
        - Event 2a: Manual Divert IDLE
        - Event 2e/2f: Not Running During Product Trial
        - Event 4a: Producing Waste

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of type IVc events.
        :rtype: pd.DataFrame
        """
        data_copy = data.copy()

        producing_waste_tags = ["DivertTotalizer"]

        freq = "12h"

        offset = pd.Timedelta(hours=6, minutes=30)

        triggers = {
            "NotRunning": {
                "start": self._was_scd_settings.not_running_start_fn,
                "end": self._was_scd_settings.not_running_end_fn,
            },
            "ManualDivert": {
                "start": lambda df: (df["ExtMtrRunning"] == 0) & (df["ManualDivert"] == 1),
                "end": lambda df: (df["ExtMtrRunning"] != 0) | (df["ManualDivert"] != 1),
            },
            "NotRunningDuringProductTrial": {
                "start": self._was_scd_settings.not_running_during_product_trial_start_fn,
                "end": self._was_scd_settings.not_running_during_product_trial_end_fn,
            },
            "ProducingWaste": {
                "start": self._was_scd_settings.producing_waste_start_fn,
                "end": self._was_scd_settings.producing_waste_end_fn,
            },
        }

        return self.process_producing_waste_by_quality_control(
            data_copy,
            producing_waste_tags,
            self._mdr,
            self._lead_product,
            freq,
            offset,
            triggers,
            "4c"
        )
