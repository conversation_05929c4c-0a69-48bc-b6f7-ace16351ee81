import re
from typing import Optional

import numpy as np
import pandas as pd

from app.utils.rlt_utils import RLT_DEFINITIONS

class BaseService:
    @staticmethod
    def fix_null_values(
        data: pd.DataFrame,
        subset: list[str],
        reporting_line: str,
        dropna: bool = True,
    ) -> pd.DataFrame:
        """
        fixes the null values of time series, assuming
        the last known status holds. Drops the remaining NaNs

        :param data: dataframe with timeseries values containing nulls / NaNs
        :type data: pd.DataFrame
        :param dropna: option to drop the remaining NaNs/nulls,
          defaults to True
        :type dropna: bool, optional
        :return: clean dataframe
        :rtype: pd.DataFrame
        """

        # treat NaNs assuming the last values hold
        dtypes = data.dtypes

        # data.ffill(inplace=True)

        # for column in data.columns:
        #     data[column] = data[column].astype(dtypes[column])

        # # TODO: Put the subset into the configuration
        # if dropna and subset:
        #     valid_columns = [col for col in subset if col in data.columns]
        #     if valid_columns:
        #         return data.dropna(subset=valid_columns)
        #     else:
        #         return data
        # return data
        
        
        data = data.copy()

        # Handle all columns except "NetProduction"
        columns_except_net = [col for col in data.columns if col != "NetProduction" and col != "TotalFeed"]
        data[columns_except_net] = data[columns_except_net].ffill()

        daily_net_production = [
            "RLN-BAYVAMVAM",
            "RLN-BISCHMMO3",
            "RLN-BISCHMMO4",
            "RLN-BISCHMPFM",
            "RLN-CLKAANAAN",
            "RLN-CLKAASAAS",
            "RLN-CLKMS1MS1",
            "RLN-CLKVAMVAM",
            "RLN-NANANHANH"
        ]

        # Custom fill for "NetProduction"
        if (reporting_line not in daily_net_production and ("NetProduction" in data.columns or "TotalFeed" in data.columns)):
            col_name = "NetProduction" if "NetProduction" in data.columns else "TotalFeed"
            net_col = data[col_name].copy()
            
            days_with_net_data = net_col.resample('D').apply(lambda x: x.count())
            valid_days = days_with_net_data[days_with_net_data > 0].index
            
            # if day n has values, forward fill within that day, if day n+1 also has values, continue forward fill until day n+1 first value. repeat
            for day in valid_days:
                fill_start = day
                next_day = day + pd.Timedelta(days=1)
                fill_end = next_day - pd.Timedelta(seconds=1) if next_day not in valid_days else next_day + pd.Timedelta(days=1) - pd.Timedelta(seconds=1)

                mask = (data.index >= fill_start) & (data.index <= fill_end)
                net_col.loc[mask] = net_col.loc[mask].ffill()

            data[col_name] = net_col

        # Restaura os tipos
        for column in data.columns:
            data[column] = data[column].astype(dtypes[column])

        if dropna and subset:
            valid_columns = [col for col in subset if col in data.columns]
            if valid_columns:
                return data.dropna(subset=valid_columns)
        return data

    def create_event_dataframe(
        self, data: pd.DataFrame, event_definition: str, pc_type: str, event_description: Optional[str],
    ) -> pd.DataFrame:
        """
        creates a dataframe containing the start and the end
        of each event frame

        :param data: dataframe containing the PI timeseries
        :type data: pd.DataFrame
        :param event_definition: name of the event definition
        :type data: str
        :return: dataframe containing the identified events
        :rtype: pd.DataFrame
        """

        # extract the starts of events
        events_data = data.loc[
            data[f"event{event_definition}_start"],
            [f"event{event_definition}_start"],
        ]

        # extract a list of the ends of the events
        end_events = data.loc[
            data[f"event{event_definition}_end"],
            f"event{event_definition}_end",
        ].index

        # find the closest timestamp to each start timestamp
        events_data[f"event{event_definition}_end"] = events_data.apply(
            self.find_closest_end_timestamp,
            end_timestamps_list=end_events,
            axis=1,
        )

        # Continuous Lines
        if not events_data.empty and pc_type == "Continuous":
            last_index = events_data.index[-1]
            
            if pd.isna(events_data.at[last_index, f"event{event_definition}_end"]):
                start_time = last_index
                if not isinstance(start_time, pd.Timestamp):
                    start_time = pd.to_datetime(start_time, errors="coerce")

                if pd.notna(start_time):
                    if event_description.upper() in [r.upper() for r in RLT_DEFINITIONS]:
                        events_data.at[last_index, f"event{event_definition}_end"] = start_time + pd.Timedelta(days=1)
                else:
                    print(f"Error: Not possible to convert index to datetime {last_index}: {start_time}")

        
        # correct first column contents
        events_data[f"event{event_definition}_start"] = events_data.index
        events_data.reset_index(drop=True, inplace=True)

        # drop duplicates to eliminate events start inside another event
        events_data.drop_duplicates(
            subset=f"event{event_definition}_end", keep="first", inplace=True
        )

        # merge create columns of event definition
        events_data["event_definition"] = event_definition
        
        if data.index.name is not None:
            data = data.reset_index()
            
        columns_merge = ["NetProduction", "MSDP", "ScheduledRate", "running_time", "rlt"]

        for col in columns_merge:
            if col in data.columns:
                data = data.sort_values("index")
                events_data = events_data.sort_values(f"event{event_definition}_start")

                events_data = pd.merge_asof(
                    events_data,
                    data[["index", col]], 
                    left_on=f"event{event_definition}_start", 
                    right_on="index",
                    direction="backward"
                )

                events_data.drop(columns=["index"], inplace=True)
        

        if (events_data.index is None or not events_data.index.empty) and event_definition.startswith(("1", "2")):
            # Fix End Time - Opened Events
            # For all events where event{event_definition}_end is NaT, we will set now as the end time and mark them as opened
            is_open = events_data[f"event{event_definition}_end"].isna().fillna(False)
            startTimezone = events_data[f"event{event_definition}_start"].dt.tz

            
            # Set the current timestamp for opened events
            events_data.loc[is_open, f"event{event_definition}_end"] = pd.Timestamp.now(tz=startTimezone)
            events_data.loc[is_open, 'isOpened'] = True
            events_data['isOpened'] = events_data['isOpened'].fillna(False)
        else:
            events_data['isOpened'] = False

        return events_data

    @staticmethod
    def find_closest_end_timestamp(
        row: pd.DataFrame, end_timestamps_list: list
    ) -> pd.DatetimeIndex:
        """
        returns the closest timestamp from a reference, ensuring it is always greater the reference

        :param row: row with the reference timestamp
        :type row: pd.DataFrame
        :param end_timestamps_list: list of the timestamps to be searched
        :type end_timestamps_list: list
        :return: closest timestamp
        :rtype: pd.DatetimeIndex
        """
        # filter the list to get only valid timestamps
        valid_end_timestamps = end_timestamps_list[
            end_timestamps_list > row.name
        ]
        # check for the closest timestamp
        if not valid_end_timestamps.empty:
            return min(valid_end_timestamps)

        return pd.NaT

    def encode_strings(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        encodes strings as integers to save memory

        :param data: dataframe to be encoded
        :type data: pd.DataFrame
        :return: transformed dataframe
        :rtype: pd.DataFrame
        """
        self.string_map = {}
        for col in data.columns:
            if str(data[col].dtype) == "object":
                # create string maps
                self.string_map[col] = {
                    i: val for i, val in enumerate(data[col].dropna().unique())
                }

                # create inverse string map
                inverse_map = {
                    val: k for k, val in self.string_map[col].items()
                }

                # do the mapping in the dataset
                data[col] = data[col].map(inverse_map)

        return data

    @staticmethod
    def validate_string_pattern(string: str, pattern: str) -> bool:
        """
        validates if a string fits the desired pattern

        :param string: string to be validated
        :type string: str
        :param pattern: desired pattern
        :type pattern: str
        :return: flag that indicates validation
        :rtype: bool
        """
        if re.match(pattern=pattern, string=string):
            return True

        return False

    def create_list_valid_batch_ids(
        self, pattern: Optional[str], data: pd.DataFrame
    ) -> None:
        """
        creates a list containing valid terms of BatchIds

        :param max_no_letters: maximum number of letters for the batchID, defaults to 2
        :type max_no_letters: int, optional
        """
        if not pattern:
            self.valid_batch_ids = None
            return

        self.valid_batch_ids = data["BatchID"][
            data["BatchID"].apply(
                lambda s: self.validate_string_pattern(s, pattern)
            )
        ]

    def get_key_based_on_values(self, col_name: list[str], value: str) -> int:
        """
        retrieves the numeric key based on the name of column encoded
        and the text value desired

        :param col_name: name of the column encoded
        :type col_name: list[str]
        :param value: desired value of the text
        :type value: str
        :return: numeric code
        :rtype: int
        """
        # get the string map for the column
        if len(col_name) == 1:
            col_name = col_name[0]

        map_ = self.string_map.get(col_name)

        # inverse keys and values
        map_ = {v: k for k, v in map_.items()}

        # recover numeric code
        return map_.get(value)

    # @staticmethod
    # def calculate_time_duration(
    #     data: pd.DataFrame, start_col: str, end_col: str
    # ) -> pd.DataFrame:
    #     """
    #     calculates the total duration of the events

    #     :param data: dataframe with the start  and the end times of each events
    #     :type data: pd.DataFrame
    #     :return: dataframe containing a column with the total duration of each event
    #     :rtype: pd.DataFrame
    #     """

    #     # calculate time duration
    #     data["total_duration_seconds"] = (
    #         data[end_col] - data[start_col]
    #     ).dt.total_seconds()

    #     return data
    
    @staticmethod
    def calculate_time_duration(
        data: pd.DataFrame, start_col: str, end_col: str
    ) -> pd.DataFrame:
        """
        Calculates the total duration of the events.

        :param data: DataFrame with the start and the end times of each event.
        :type data: pd.DataFrame
        :param start_col: Name of the column with the start times.
        :type start_col: str
        :param end_col: Name of the column with the end times.
        :type end_col: str
        :return: DataFrame containing a column with the total duration of each event in seconds.
        :rtype: pd.DataFrame
        """
        # Ensure the start and end columns are datetime
        data[start_col] = pd.to_datetime(data[start_col], errors="coerce")
        data[end_col] = pd.to_datetime(data[end_col], errors="coerce")

        # Check for invalid values
        if data[start_col].isna().any() or data[end_col].isna().any():
            invalid_rows = data[data[start_col].isna() | data[end_col].isna()]
            print(f"Warning: Found invalid datetime values in the following rows:\n{invalid_rows}")

        # Drop rows with invalid datetime values
        data = data.dropna(subset=[start_col, end_col])

        if data.empty:
            return data

        # Calculate time duration in seconds
        data["total_duration_seconds"] = (
            data[end_col] - data[start_col]
        ).dt.total_seconds()

        return data


    @staticmethod
    def round_to_nearest_period(
        data: pd.DataFrame, freq: str = "T"
    ) -> pd.DataFrame:
        """
        rounds the timeseries timestamps to the nearest period

        :param data: dataframe to be transformed
        :type data: pd.DataFrame
        :param freq: frequency to round the data, defaults to 'T'
        :type freq: str, optional
        :return: transformed dataframe
        :rtype: pd.DataFrame
        """

        # check if the index is datetime
        if not isinstance(data.index, pd.DatetimeIndex):
            data.index = pd.DatetimeIndex(data.index)

        # round to nearest index
        data.index = data.index.round(freq)

        # check for duplicated indexes
        if data.index.duplicated().any:
            data = data[~data.index.duplicated(keep="first")]

        return data

    @staticmethod
    def fix_time_range(data: pd.DataFrame, freq: str = "T") -> pd.DataFrame:
        """
        fixes the time series range to a selected frequency

        :param data: data to be corrected
        :type data: pd.DataFrame
        :param freq: frequency, defaults to 'T'
        :type freq: str, optional
        :return: reindexed dataframe
        :rtype: pd.DataFrame
        """

        # check if the index is datetime
        if not isinstance(data.index, pd.DatetimeIndex):
            data.index = pd.DatetimeIndex(data.index)

        # extract start and end times
        start_, end_ = data.index[0], data.index[-1]

        # create new time range
        new_range = pd.date_range(start=start_, end=end_, freq=freq)

        # reindex data
        return data.reindex(new_range)

    @staticmethod
    def apply_timeseries_interpolation(data: pd.DataFrame) -> pd.DataFrame:
        """
        applies timeseries linear interpolation to fill NaNs

        :param data: data with NaNa and a timestamp index
        :type data: pd.DataFrame
        :return: interpolated dataframe
        :rtype: pd.DataFrame
        """
        return data.interpolate(method="time")

    @staticmethod
    def check_if_null_column(data: pd.DataFrame) -> tuple:
        """
        checks if there's any column fully nulled

        :param data: dataframe to be analyzed
        :type data: pd.DataFrame
        :return: flag with if any colum is completely null and the name of the column
        :rtype: tuple
        """

        for col in data:
            if data[col].isna().sum() == data.shape[0]:
                return True, col

        return False, None
