import streamlit as st
import pandas as pd
from cognite.client import Cognite<PERSON><PERSON>
from cognite.client.data_classes.filters import Prefix
from cognite.client.data_classes.functions import Function<PERSON>all, FunctionList

from datetime import datetime, timedelta
from statistics import mean


st.title("OEE Event Frame Execution (Last 100 executions)")
client = CogniteClient()


def calculate_metrics(executions: list[timedelta]) -> tuple[str, str, str]:
    if not executions:
        return ("-", "-", "-")
    max_value = max(executions).total_seconds()
    min_value = min(executions).total_seconds()
    mean_value = mean([i.total_seconds() for i in executions])

    return (max_value, min_value, mean_value)


@st.cache_data
def get_result():
    INSTANCE_PREFIX = "OEERSC-STS-"
    FUNCTION_PREFIX = "OEE-EF-"

    instances = client.data_modeling.instances.list(
        filter=Prefix(["node", "externalId"], INSTANCE_PREFIX), limit=None
    ).as_ids()

    sites = list(
        {instance.external_id.replace(INSTANCE_PREFIX, "") for instance in instances}
    )

    function_external_ids = [FUNCTION_PREFIX + site for site in sites]

    functions_list = client.functions.retrieve_multiple(
        external_ids=function_external_ids
    )

    result = []
    assert isinstance(functions_list, FunctionList)
    for function_entry in functions_list:
        function_calls = function_entry.list_calls(limit=100)

        total_executions: list[timedelta] = []
        total_success_execution: list[timedelta] = []
        total_failure_execution: list[timedelta] = []

        for entry in function_calls.data:
            assert isinstance(entry, FunctionCall)

            if not entry.end_time:
                continue
            execution_time = datetime.fromtimestamp(
                entry.end_time / 1000
            ) - datetime.fromtimestamp(entry.start_time / 1000)
            total_executions.append(execution_time)

            if entry.status == "Failed":
                total_failure_execution.append(execution_time)
            else:
                total_success_execution.append(execution_time)

        total_execution = len(function_calls.data)
        number_of_failures = len(total_failure_execution)
        number_of_success = len(total_success_execution)
        (max_total_executions, min_total_executions, mean_total_executions) = (
            calculate_metrics(total_executions)
        )
        (
            max_total_success_execution,
            min_total_success_execution,
            mean_total_success_execution,
        ) = calculate_metrics(total_success_execution)
        (
            max_total_failure_execution,
            min_total_failure_execution,
            mean_total_failure_execution,
        ) = calculate_metrics(total_failure_execution)
        result.append(
            {
                "site": function_entry.external_id.replace(FUNCTION_PREFIX, ""),
                "total_execution": total_execution,
                "number_of_failures": number_of_failures,
                "number_of_success": number_of_success,
                "max_total_executions_sec": max_total_executions,
                "min_total_executions_sec": min_total_executions,
                "mean_total_executions_sec": mean_total_executions,
                "max_total_success_execution_sec": max_total_success_execution,
                "min_total_success_execution_sec": min_total_success_execution,
                "mean_total_success_execution_sec": mean_total_success_execution,
                "max_total_failure_execution_sec": max_total_failure_execution,
                "min_total_failure_execution_sec": min_total_failure_execution,
                "mean_total_failure_execution_sec": mean_total_failure_execution,
            }
        )

    df = pd.DataFrame(result)
    return df


st.write(get_result())
