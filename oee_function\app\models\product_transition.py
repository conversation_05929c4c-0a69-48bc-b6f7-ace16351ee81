from typing import Any
from pydantic import Field
from .node import Node
from .reporting_line import ReportingLine
from .material import Material


class ProductTransition(Node):
    ref_reporting_line: ReportingLine = Field(alias="refReportingLine")
    from_product: Material = Field(alias="fromProduct")
    to_product: Material = Field(alias="toProduct")
    duration: int

    @classmethod
    def from_cognite_response(cls, item: dict[str, Any]) -> "ProductTransition":
        return ProductTransition(
            externalId=item["externalId"],
            space=item["space"],
            refReportingLine=ReportingLine(**item["refReportingLine"]),
            fromProduct=Material(**item["fromProduct"]),
            toProduct=Material(**item["toProduct"]),
            duration=item["duration"],
        )
