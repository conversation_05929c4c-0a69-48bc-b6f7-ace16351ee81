from typing import Any

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.utils.constants import Constants as const

class FracACHEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        
        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)
        
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp
        )

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus < 2
        data = data.assign(
            event1a_start=(
                self.not_running_fn(data)
            )
        )

        # event end - ProductionLineStatus > 2
        data = data.assign(
            event1a_end=(
                self.not_running_fn(data) == False
                & (self.not_running_fn(data).shift(1) == True)
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE])

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]

        return day_data
    
    def identify_events_typeIIIb(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE])

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data
    
    def identify_events_typeIIIc(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "sum", [const.MSDP, const.SCHEDULED_RATE])

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data

    def identify_events_typeIVa(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIVb(self):
        pass
    
    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced

        def calculate_diff(column):

            prev_value = df[column].shift(1)
            diff = df[column] - prev_value
            return diff.where(diff > 0, 0).shift(-1)
        
        net_production = (
            calculate_diff("ProductionLineStatus2").fillna(0)
            - calculate_diff("ProductionLineStatus3").fillna(0)
            - calculate_diff("ProductionLineStatus4").fillna(0)
            - calculate_diff("ProductionLineStatus5").fillna(0) * 0.0002592
        )

        net_production = net_production.where(net_production <= 1000, 0)
        
        self._total_produced = net_production

        return net_production

    def not_running_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._not_running.empty:
            return self._not_running
        
        not_running = df["ProductionLineStatus"] < 2
        
        self._not_running = not_running
        
        return not_running