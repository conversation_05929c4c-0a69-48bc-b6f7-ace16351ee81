from typing import Any, Optional

import numpy as np
import pandas as pd
from app.models.lead_product import LeadProduct
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.was.washington_works_event_identification_service import (
    WashingtonWorksEventIdentificationService,
)
from app.services.tag_value_mapping.was.was_epce_state_mapping_service import (
    StatesTypes,
)
from app.utils.event_detection_utils import detect_not_runnings_type_two
from app.utils.uom_conversion import lbs_to_kg


class WASEPCEEventIdentificationService(WashingtonWorksEventIdentificationService):
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: Optional[pd.DataFrame],
        product_transition: Optional[dict[str, dict[str, dict[str, int]]]],
        lead_products: Optional[list[LeadProduct]]
    ) -> None:
        self._available_products = mdr["piTagValue"].to_list() if mdr is not None else []
        self._reporting_site_configuration = reporting_site_configuration
        self._reporting_line_external_id = reporting_line_external_id
        self._not_running_with_transitions = pd.DataFrame()
        self._product_transition = product_transition
        self._mdr = mdr
        self._lead_product = next(
            (
                x
                for x in (lead_products or [])
                if x.reporting_line.external_id == reporting_line_external_id
            ),
            None,
        )
        
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1c": self.identify_events_typeIc,
            "2a": self.identify_events_typeIIa,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }
    
    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods
        
    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        processed_data = self.__process_not_running_with_transitions(data)
       
        if processed_data.empty:
            return data
        
        if "event1c_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1c_start"], inplace=True)

        if "event1c_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1c_end"], inplace=True)
        return processed_data

    def identify_events_typeIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        processed_data = self.__process_not_running_with_transitions(data)
        
        if processed_data.empty:
            return data
        
        if "event1a_start" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1a_start"], inplace=True)
        
        if "event1a_end" in processed_data.columns.to_list():
            processed_data.drop(columns=["event1a_end"], inplace=True)
            
        return processed_data
    
    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        # Start Trigger:
        # State = 4 AND
        # Product Description Tag reports a Product code that is NOT on a predefined list
        event2a_start = self.__product_trial_start_fn(data)
        consecutive_start = event2a_start.shift(1) & event2a_start
        event2a_start.loc[consecutive_start] = False

        # End Trigger:
        # State != 4 OR
        # Product Description Tag reports a Product code that is on a predefined list
        event2a_end = self.__product_trial_end_fn(data)
        consecutive_end = event2a_end.shift(1) & event2a_end
        event2a_end.loc[consecutive_end] = False

        data = data.assign(event2a_start=event2a_start, event2a_end=event2a_end)

        return data
    
    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        data = detect_not_runnings_type_two(
            data,
            self.__get_type_two_trigger,
            equal_product=True,
            start_key="event2c_start",
            end_key="event2c_end",
        )

        return data
    
    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        data = detect_not_runnings_type_two(
            data,
            self.__get_type_two_trigger,
            equal_product=False,
            start_key="event2d_start",
            end_key="event2d_end",
        )

        return data
    
    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        # start and end conditions											
        cond_start = self.__producing_waste_start_fn(data)
        cond_end = self.__producing_waste_end_fn(data)

        data = data.assign(
            event4a_start=cond_start,
            event4a_end=cond_end,
        )
        
        # correct start and end flags
        data = data.assign(
            event4a_start=(
                data["event4a_start"] & (data["event4a_start"].shift(1) != True)
            ),
            event4a_end=(
                data["event4a_end"] & (data["event4a_end"].shift(1) != True)
            ),
        )
        
        return data
    
    def identify_events_typeIVb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        offset = pd.Timedelta(hours=5, minutes=30)
        producing_waste_tags = ["PlopTotalizer"]
        data_copy = data.copy()
        freq = "12h"
        
        triggers = {
            "NotRunning": {
                "start": self.__not_running_start_fn,
                "end": self.__not_running_end_fn,
            },
            "ProductTrial": {
                "start": self.__product_trial_start_fn,
                "end": self.__product_trial_end_fn,
            },
            "NotRunningDuringProductTrial": {
                "start": self.__not_running_during_product_trial_start_fn,
                "end": self.__not_running_during_product_trial_end_fn,
            },
            "ProducingWaste": {
                "start": self.__producing_waste_start_fn,
                "end": self.__producing_waste_end_fn,
            },
        }
        
        return self.process_producing_waste_by_quality_control(
            data_copy,
            producing_waste_tags,
            self._mdr,
            self._lead_product,
            freq,
            offset,
            triggers,
            "4b"
        )
    
    def __process_not_running_with_transitions(self, df: pd.DataFrame) -> pd.DataFrame:
        if not self._not_running_with_transitions.empty:
            return self._not_running_with_transitions.copy()

        self._not_running_with_transitions: pd.DataFrame = (
            self.identify_not_running_with_transitions(
                self._reporting_line_external_id,
                df,
                self.__not_running_start_fn,
                self.__not_running_end_fn,
                self._available_products,
                self._product_transition,
                48,
            )
        )

        return self._not_running_with_transitions.copy()
    
    # Events condtions type 1a and 1c
    def __not_running_start_fn(self, df: pd.DataFrame) -> pd.Series:
        return (
            (df["State"] == StatesTypes.NOT_RUNNING.value)
            & (df["ProductDescription"].isin(self._available_products))
        )
    
    def __not_running_end_fn(self, df: pd.DataFrame) -> pd.Series:
        return (
            (df["State"] != StatesTypes.NOT_RUNNING.value)
            | (~df["ProductDescription"].isin(self._available_products))
        )
    
    # Events condtions type 2a
    def __product_trial_start_fn(self, data: pd.DataFrame) -> pd.Series:
        return (data["State"] != StatesTypes.NOT_RUNNING.value) & (
            ~data["ProductDescription"].isin(self._available_products)
        )

    def __product_trial_end_fn(self, data: pd.DataFrame) -> pd.Series:
        return (data["State"] == StatesTypes.NOT_RUNNING.value) | (
            data["ProductDescription"].isin(self._available_products)
        )
    
    # Events condtions type 2c and 2d
    def __not_running_during_product_trial_start_fn(self, data: pd.DataFrame) -> pd.Series:
        return (data["State"] == StatesTypes.NOT_RUNNING.value) & (
            ~data["ProductDescription"].isin(self._available_products))
        
    def __not_running_during_product_trial_end_fn(self, data: pd.DataFrame) -> pd.Series:
        return (data["State"] != StatesTypes.NOT_RUNNING.value) | (
            data["ProductDescription"].isin(self._available_products))
    
    # Events condtions type 4a
    def __producing_waste_start_fn(self, data: pd.DataFrame) -> pd.Series:
        return ((data["State"] == StatesTypes.PRODUCING_WASTE.value)
            & (data["ProductDescription"].isin(self._available_products)))
    
    def __producing_waste_end_fn(self, data: pd.DataFrame) -> pd.Series:
        return ((data["State"] != StatesTypes.PRODUCING_WASTE.value)
            | (~data["ProductDescription"].isin(self._available_products)))

    def __get_type_two_trigger(self, data: pd.DataFrame, start_key: str, end_key: str) -> pd.DataFrame:
        # event trigger start
        data[start_key] = self.__not_running_during_product_trial_start_fn(data)

        # event trigger end
        data[end_key] = self.__not_running_during_product_trial_end_fn(data)

        return data
    

class WasEpceSettings:
    def __init__(
        self,
        reporting_line_external_id: str,
        mdr: Optional[pd.DataFrame]
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._mdr = mdr
        
    
    def prepare_hourly_data(self, data: pd.DataFrame) -> pd.DataFrame:
        products = self._mdr["piTagValue"].to_list() if self._mdr is not None else []

        data["isRunning"] = (
            (data["State"] != StatesTypes.NOT_RUNNING.value)
            & (data["ProductDescription"].isin(products))
        )

        return data
    
    def prepare_total_feed(self, data: pd.DataFrame) -> pd.DataFrame:
        data["TotalFeed"] = data["TotalFeed"].where((data["isRunning"]), 0)
        
        return data