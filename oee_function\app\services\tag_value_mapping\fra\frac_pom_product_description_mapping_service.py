import pandas as pd


class FracPomProductDescriptionMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            "01507": "POM PULVER 01507 COMPACTED",
            "02556": "POM PULVER 02556 COMPACTED",
            "09034": "POM PULVER 09034 COMPACTED",
            "09034-OH": "POM PULVER 09034-OH COMPACTED",
            "13014": "POM PULVER 13014 COMPACTED",
            "13034": "POM PULVER 13034 COMPACTED",
            "27045": "POM PULVER 27045 COMPACTED",
            "52034": "POM PULVER 52034 COMPACTED",
            "99000": "POM PULVER TER 99000 COMPACTED",
            "18999": "Riser product (during product change)",
            "18999-K": "Riser product (during product change)",
            "27045-K": "Wrong entry ( please ignore)",
            "05999": "Riser product (during product change)",
            "0": "Wrong entry ( please ignore)"
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})