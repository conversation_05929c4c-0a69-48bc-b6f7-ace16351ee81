from typing import Optional

from app.enums.entity_code import EntityCode
from app.repositories.bbct_repository import BbctRepository
from app.repositories.event_frame_repository import EventFrameRepository
from app.repositories.hourly_data_repository import HourlyDataRepository
from app.repositories.lead_product_repository import LeadProductRepository
from app.repositories.loss_category_repository import LossCategoryRepository
from app.repositories.mdr_repository import MdrRepository
from app.repositories.msdp_repository import MsdpRepository
from app.repositories.process_repository import ProcessRepository
from app.repositories.product_repository import ProductRepository
from app.repositories.material_repository import MaterialRepository
from app.repositories.product_transition_repository import ProductTransitionRepository
from app.repositories.reporting_site_repository import ReportingSiteRepository
from app.repositories.timeseries_repository import TimeSeriesRepository
from app.repositories.view_repository import ViewRepository
from app.services.event_frame_service import EventFrameService
from app.utils.id_generation import IdGenerator

from .cognite_client_factory import CogniteClientFactory
from .env_variables import EnvVariables


def create_repositories(variables: Optional[EnvVariables] = None):
    if not variables:
        variables = EnvVariables()
    data_model_id = variables.cognite.get_data_model_id()

    cognite_client = CogniteClientFactory.create(variables)

    view_repository = ViewRepository(cognite_client, data_model_id)
    rsc_repository = ReportingSiteRepository(
        cognite_client,
        view_repository,
        data_model_id,
    )
    ts_repository = TimeSeriesRepository(cognite_client)
    bbct_repository = BbctRepository(cognite_client, data_model_id)
    msdp_repository = MsdpRepository(cognite_client, data_model_id)
    mdr_repository = MdrRepository(cognite_client, data_model_id)

    event_frame_repository = EventFrameRepository(cognite_client, view_repository)

    product_repository = ProductRepository(
        cognite_client=cognite_client,
        view_repository=view_repository,
        data_model_id=data_model_id,
        default_instances_space=variables.cognite.default_data_model_instances_space,
    )

    material_repository = MaterialRepository(
        cognite_client=cognite_client, data_model_id=data_model_id
    )

    process_repository = ProcessRepository(
        cognite_client=cognite_client,
        view_repository=view_repository,
        data_model_id=data_model_id,
    )

    hourly_data_repository = HourlyDataRepository(cognite_client, view_repository)

    loss_category_repository = LossCategoryRepository(
        cognite_client, view_repository, data_model_id
    )
    lead_product_repository = LeadProductRepository(cognite_client, data_model_id)

    product_transition_repository = ProductTransitionRepository(
        cognite_client, data_model_id
    )

    return (
        view_repository,
        rsc_repository,
        ts_repository,
        bbct_repository,
        event_frame_repository,
        hourly_data_repository,
        msdp_repository,
        mdr_repository,
        product_repository,
        material_repository,
        process_repository,
        loss_category_repository,
        lead_product_repository,
        product_transition_repository,
    )


def create_event_frame_service(variables: Optional[EnvVariables] = None):
    if not variables:
        variables = EnvVariables()

    id_generator = IdGenerator[EntityCode]()

    (
        _,
        rsc_repository,
        ts_repository,
        bbct_repository,
        event_frame_repository,
        hourly_data_repository,
        msdp_repository,
        mdr_repository,
        product_repository,
        material_repository,
        process_repository,
        loss_category_repository,
        lead_product_repository,
        product_transition_repository,
    ) = create_repositories(variables)

    return EventFrameService(
        rsc_repository,
        ts_repository,
        bbct_repository,
        event_frame_repository,
        hourly_data_repository,
        variables.cognite.default_data_model_instances_space,
        variables.cognite.asset_hierarchy_instances_space,
        msdp_repository,
        mdr_repository,
        lead_product_repository,
        product_repository,
        material_repository,
        process_repository,
        id_generator,
        loss_category_repository,
        product_transition_repository,
    )
