import math
from datetime import datetime, timedelta
from typing import Any, Optional

import pandas as pd
import pytz
from cognite.client.data_classes.data_modeling import (
    EdgeApply,
    NodeApply,
    ViewId,
)
from pydantic import Field

from ..enums.entity_code import EntityCode
from ..utils.string_utils import StringUtils
from .event_hierarchy_configuration import EventHierarchyConfiguration
from .node import Node


class EventDetail(Node):
    ref_metric_code: Optional[Node] = Field(
        default=None, alias="refMetricCode"
    )
    ref_event_code: Optional[Node] = Field(default=None, alias="refEventCode")
    ref_subcategory_level_1: Optional[Node] = Field(
        default=None, alias="refSubCategoryL1"
    )
    ref_subcategory_levle_2: Optional[Node] = Field(
        default=None, alias="refSubCategoryL2"
    )
    assigned_hours: Optional[float] = Field(
        default=None, alias="assignedHours"
    )
    assigned_lost_production_mt: Optional[float] = Field(
        default=None, alias="assignedLostProductionMT"
    )
    ref_loss_category: Optional[Node] = Field(
        default=None, alias="refLossCategory"
    )


class Event(Node):
    event_definition: str = Field(alias="eventDefinition")
    ref_site: Node = Field(alias="refSite")
    ref_unit: Node = Field(default=None, alias="refUnit")
    ref_reporting_line: Optional[Node] = Field(
        default=None, alias="refReportingLine"
    )
    ref_oee_product: Optional[Node] = Field(
        default=None, alias="refOEEProduct"
    )
    ref_material: Optional[Node] = Field(default=None, alias="refMaterial")
    start_date_time: str = Field(alias="startDateTime")
    end_date_time: str = Field(alias="endDateTime")
    total_duration: float = Field(alias="totalDuration")
    total_unassigned_hours: float = Field(alias="totalUnAssignedHours")
    status: Optional[str] = Field(alias="status")
    ref_process_type: Optional[Node] = Field(None, alias="refProcessType")
    line_type: Optional[str] = Field(None, alias="lineType")
    event_id: str = Field(alias="eventId")
    process_order: Optional[str] = Field(None, alias="processOrder")
    batch: Optional[str] = Field(None, alias="batch")
    is_open: Optional[bool] = Field(False, alias="isOpen")
    net_production: Optional[float] = Field(None, alias="netProduction")
    msdp: Optional[float] = Field(None, alias="msdp")
    scheduled_rate: Optional[float] = Field(None, alias="scheduleRate")
    running_time: Optional[float] = Field(None, alias="totalRunningTime")
    lost_production_mt: Optional[float] = Field(None, alias="lostProductionMT")
    net_production_lbs: Optional[float] = Field(None, alias="netProductionLbs")
    ref_oee_event_detail: list[EventDetail] = Field(
        default_factory=list, alias="refOEEEventDetail", exclude=True
    )
    ref_bbct: Optional[Node] = Field(alias="refOEEBBCT", default=None)
    mdr: Optional[float] = Field(None, alias="mdr")
    event_key: Optional[str] = Field(default=None, alias="eventKey")
    is_transition: Optional[bool] = Field(default=None, alias="isTransition")
    from_product: Optional[Node] = Field(default=None, alias="fromProduct")
    to_product: Optional[Node] = Field(default=None, alias="toProduct")
    extra_duration: Optional[float] = Field(
        default=None, alias="extraDuration"
    )
    expected_duration: Optional[float] = Field(
        default=None, alias="expectedDuration"
    )

    @staticmethod
    def generate_external_id_prefix(
        reporting_line_external_id: str, event_definition: str
    ) -> str:
        line_code = str(reporting_line_external_id.replace("RLN-", ""))
        return (
            f"{EntityCode.OEE_EVENT.value}-{line_code}-{event_definition}"
        ).replace(" ", "")

    @staticmethod
    def generate_external_id(
        reporting_line_external_id: str,
        event_definition: str,
        start_time: datetime,
    ) -> str:
        external_id_prefix = Event.generate_external_id_prefix(
            reporting_line_external_id, event_definition
        )

        start_time = start_time.replace(microsecond=0)

        external_id = (
            f"{external_id_prefix}-{str(start_time).replace(' ', '')}"
        )

        return external_id

    @staticmethod
    def _generate_event_key(
        reporting_line_name: str,
        event_definition: str,
        start_time: datetime,
    ) -> str:
        event_definition_acronyms = StringUtils.generate_final_acronyms(
            [event_definition],
            min_size=3,
            max_size=5,
        )

        return (
            f"{reporting_line_name}-"
            f"{event_definition_acronyms[event_definition]}-"
            f"{int(start_time.timestamp())}"
        ).replace(" ", "")

    @classmethod
    def from_event_row(
        cls,
        row: dict[str, Any],
        space: str,
        asset_hierarchy_space: str,
    ) -> "Event":
        external_id = (
            Event.generate_external_id(
                row["refReportingLineId"], row["def"], row["start_time"]
            )
            if row.get("externalId") is None
            else row["externalId"]
        )

        batch_id = (
            (
                None
                if isinstance(row["BatchID"], float)
                and math.isnan(row["BatchID"])
                else row["BatchID"]
            )
            if row.get("BatchID")
            else None
        )

        process_order = (
            (
                None
                if isinstance(row["ProcessOrder"], float)
                and math.isnan(row["ProcessOrder"])
                else row["ProcessOrder"]
            )
            if row.get("ProcessOrder")
            else None
        )

        is_assigned = Event.is_auto_assigned(row)

        event_key = Event._generate_event_key(
            row["refReportingLineName"],
            row["def"],
            row["start_time"],
        )

        is_transition = row["IsTransition"]
        from_product = (
            {
                "externalId": row["FromProductExtId"],
                "space": row["FromProductSpace"],
            }
            if row["FromProductExtId"] and row["FromProductSpace"]
            else None
        )
        to_product = (
            {
                "externalId": row["ToProductExtId"],
                "space": row["ToProductSpace"],
            }
            if row["ToProductExtId"] and row["ToProductSpace"]
            else None
        )
        extra_duration = row["ExtraDuration"]
        expected_duration = row["ExpectedDuration"]

        return cls(
            externalId=external_id,
            space=space,
            eventDefinition=row["def"],
            refSite={
                "externalId": row["refSiteId"],
                "space": asset_hierarchy_space,
            },
            refUnit=(
                {
                    "externalId": row["refUnitId"],
                    "space": asset_hierarchy_space,
                }
                if row["refUnitId"] is not None
                else None
            ),
            refReportingLine=(
                {
                    "externalId": row["refReportingLineId"],
                    "space": asset_hierarchy_space,
                }
                if row["refReportingLineId"] is not None
                else None
            ),
            refOEEProduct=(
                {
                    "externalId": row["oeeProductExternalId"],
                    "space": row["oeeProductSpace"],
                }
                if row.get("oeeProductExternalId") is not None
                and row.get("oeeProductSpace") is not None
                else None
            ),
            refMaterial=(
                {
                    "externalId": row["oeeMaterialExternalId"],
                    "space": row["oeeMaterialSpace"],
                }
                if row.get("oeeMaterialExternalId") is not None
                and row.get("oeeMaterialSpace") is not None
                else None
            ),
            startDateTime=pd.to_datetime(row["start_time"])
            .replace(microsecond=0)
            .isoformat(),
            endDateTime=pd.to_datetime(row["end_time"])
            .replace(microsecond=0)
            .isoformat(),
            status="Assigned" if is_assigned else "Unassigned",
            totalDuration=row["total_duration_seconds"],
            totalUnAssignedHours=(
                0 if is_assigned else row["total_duration_seconds"]
            ),
            refProcessType=(
                {
                    "externalId": row["refProcessTypeId"],
                    "space": asset_hierarchy_space,
                }
                if row["refProcessTypeId"] is not None
                else None
            ),
            lineType=row["lineType"],
            eventId=str(row["EventID"]),
            processOrder=str(process_order)
            if process_order is not None
            else None,
            batch=str(batch_id) if batch_id is not None else None,
            isOpen=row["isOpened"] if row.get("isOpened") else False,
            netProduction=row["NetProduction"]
            if row.get("NetProduction")
            else None,
            msdp=row["MSDP"] if row.get("MSDP") else None,
            scheduleRate=row["ScheduledRate"]
            if row.get("ScheduledRate")
            else None,
            totalRunningTime=row["running_time"]
            if row.get("running_time")
            else None,
            netProductionLbs=(
                row["net_production_lbs"]
                if row.get("net_production_lbs")
                else None
            ),
            lostProductionMT=(
                row["lostProductionMT"]
                if row.get("lostProductionMT")
                else None
            ),
            mdr=row["MDR"] if row.get("MDR") else None,
            refOEEBBCT=(
                {
                    "externalId": row["refOEEBBCT"]["externalId"],
                    "space": row["refOEEBBCT"]["space"],
                }
                if row["refOEEBBCT"] is not None
                else None
            ),
            eventKey=event_key,
            isTransition=is_transition,
            fromProduct=from_product,
            toProduct=to_product,
            extraDuration=extra_duration,
            expectedDuration=expected_duration,
            refOEEEventDetail=[
                {
                    "externalId": external_id.replace(
                        EntityCode.OEE_EVENT.value,
                        EntityCode.OEE_EVENT_DETAIL.value,
                        1,
                    ),
                    "space": space,
                    "refMetricCode": (
                        {
                            "externalId": row["externalId_mt"],
                            "space": row["space_mt"],
                        }
                        if row["externalId_mt"] is not None
                        and row["space_mt"] is not None
                        else None
                    ),
                    "refEventCode": (
                        {
                            "externalId": row["externalId_ev"],
                            "space": row["space_ev"],
                        }
                        if row["externalId_ev"] is not None
                        and row["space_ev"] is not None
                        else None
                    ),
                    "refSubCategoryL1": (
                        {
                            "externalId": row["externalId_sc1"],
                            "space": row["space_sc1"],
                        }
                        if row["externalId_sc1"] is not None
                        and row["space_sc1"] is not None
                        else None
                    ),
                    "refSubCategoryL2": (
                        {
                            "externalId": row["externalId_sc2"],
                            "space": row["space_sc2"],
                        }
                        if row["externalId_sc2"] is not None
                        and row["space_sc2"] is not None
                        else None
                    ),
                    "assignedHours": (
                        0 if not is_assigned else row["total_duration_seconds"]
                    ),
                    "assignedLostProductionMT": (
                        row["lostProductionMT"]
                        if is_assigned and row.get("lostProductionMT")
                        else 0
                    ),
                    "refLossCategory": (
                        {
                            "externalId": row["loss_category_ext_id"],
                            "space": row["loss_category_space"],
                        }
                        if row["loss_category_ext_id"] is not None
                        and row["loss_category_space"] is not None
                        else None
                    ),
                }
            ],
        )

    def convert_to_nodes_and_edges(
        self, event_view: ViewId, event_detail_view: ViewId
    ) -> tuple[list[NodeApply], list[EdgeApply]]:
        edges: list[EdgeApply] = []
        nodes: list[NodeApply] = []

        nodes.append(
            self.convert_to_cognite_node(
                event_view,
            )
        )
        for event_detail in self.ref_oee_event_detail:
            edges.append(
                self.convert_to_cognite_edge(
                    event_view, "refOEEEventDetail", event_detail
                )
            )

            nodes.append(
                event_detail.convert_to_cognite_node(event_detail_view)
            )

        return (nodes, edges)

    @staticmethod
    def is_auto_assigned(row: dict[str, Any]) -> bool:
        """
        Determines if an event should be auto-assigned.
        """

        if row.get("RLT_SHORT_DURATION", False):
            return True

        return (
            row["externalId_mt"] is not None
            and row["externalId_ev"] is not None
            and (
                row["name_ev"]
                in ["Minor Stops", "Not Scheduled to Run", "No Demand"]
                or (
                    row["name_sc2"] is not None
                    and row["name_sc2"] in ["Normal System Variation"]
                )
                or (
                    row["name_sc1"] is not None
                    and row["name_sc1"] in ["Product Swap"]
                )
            )
        )

    @staticmethod
    def to_dict(event: "Event") -> dict[str, Any]:
        return {
            "eventDefinition": event.event_definition,
            "refSite": event.ref_site.external_id,
            "refUnit": event.ref_unit.external_id,
            "refReportingLine": (
                event.ref_reporting_line.external_id
                if event.ref_reporting_line
                else ""
            ),
            "refOEEProduct": (
                event.ref_oee_product.external_id
                if event.ref_oee_product
                else ""
            ),
            "refMaterial": event.ref_material.external_id
            if event.ref_material
            else "",
            "startDateTime": event.start_date_time,
            "endDateTime": event.end_date_time,
            "totalDuration": event.total_duration,
            "totalUnAssignedHours": event.total_unassigned_hours,
            "status": event.status,
            "refProcessType": (
                event.ref_process_type.external_id
                if event.ref_process_type
                else ""
            ),
            "lineType": event.line_type,
            "eventId": event.event_id,
            "processOrder": event.process_order,
            "batch": event.batch,
            "isOpen": event.is_open,
            "netProduction": event.net_production,
            "msdp": event.msdp,
            "scheduleRate": event.scheduled_rate,
            "totalRunningTime": event.running_time,
            "lostProductionMT": event.lost_production_mt,
            "netProductionLbs": event.net_production_lbs,
            "refOEEEventDetail": [
                e.external_id for e in event.ref_oee_event_detail
            ],
            "refOEEBBCT": event.ref_bbct.external_id if event.ref_bbct else "",
            "mdr": event.mdr,
            "eventKey": event.event_key,
        }

    @staticmethod
    def to_list_of_dict(events: list["Event"]) -> list[dict[str, Any]]:
        return [Event.to_dict(event) for event in events]

    @staticmethod
    def to_dataframe(events: list["Event"]) -> pd.DataFrame:
        list_of_dict = Event.to_list_of_dict(events)
        return pd.DataFrame(list_of_dict)


class EventFrameLastExecutionCollection:
    def __init__(
        self,
        search_keys: Optional[dict[str, EventHierarchyConfiguration]] = None,
    ) -> None:
        self._search_keys = search_keys or {}
        self._keys_by_reporting_line: dict[str, dict[str, datetime]] = {}

    def add_entries(self, entries: dict[str, datetime]) -> None:
        for external_id, timestamp in entries.items():
            event = self._search_keys.get(external_id)
            if not event or not event.event_definition:
                continue

            reporting_line = event.reporting_line.external_id
            entry = self._keys_by_reporting_line.get(reporting_line, {})
            entry[event.event_definition] = timestamp
            self._keys_by_reporting_line[reporting_line] = entry

    def get_min_execution(self):
        value: Optional[datetime] = None
        for reporting_line in self._keys_by_reporting_line.keys():
            min_value = self.get_min_execution_by_reporting_line(
                reporting_line
            )
            if not min_value:
                continue

            if not value or value.timestamp() > min_value.timestamp():
                value = min_value
        return value

    def get_min_execution_by_reporting_line(
        self,
        reporting_line_external_id: str,
        event_definition: Optional[str] = None,
    ) -> Optional[datetime]:
        values = self._keys_by_reporting_line.get(reporting_line_external_id)

        if not values:
            return None

        min_value = min(values.values())

        if min_value.tzinfo is None:
            min_value_timezone = pytz.utc
        else:
            min_value_timezone = min_value.tzinfo

        forty_five_days_ago = datetime.now(min_value_timezone) - timedelta(
            days=45
        )
        return forty_five_days_ago
