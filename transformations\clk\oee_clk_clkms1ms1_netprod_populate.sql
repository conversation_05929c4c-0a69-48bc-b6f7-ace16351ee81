WITH
  sites AS (
    SELECT
      SiteID as id,
      SiteName as site
    FROM
      YAS.`YAS-COR-tblCfgSite`
  ),
  areas AS (
    SELECT
      AreaID AS id,
      AreaName AS area
    FROM
      YAS.`YAS-COR-tblCfgArea`
  ),
  units AS (
    SELECT
      u.UnitID AS id,
      u.UnitName AS unit,
      a.area AS area,
      a.id AS area_id
    FROM
      YAS.`YAS-COR-tblCfgUnit` u
      LEFT JOIN areas a ON u.AreaID = a.id
  ),
  products AS (
    SELECT
      ID AS id,
      ProductName AS product
    FROM
      YAS.`YAS-COR-tblCfgProduct`
  ),
  dor_cfg AS (
    SELECT
      d.OrderOfHeader AS id,
      d.Header AS header,
      s.site AS site,
      u.unit AS unit,
      u.area AS area,
      p.product AS product
    FROM
      YAS.`YAS-COR-tblCfgDOR` d
      LEFT JOIN sites s ON d.tblCfgSite_SiteID = s.id
      LEFT JOIN units u ON d.RestrictTo_tblCfgArea_AreaID = u.area_id
      LEFT JOIN products p ON d.tblCfgProduct_ID = p.id
  ),
  dor AS (
    SELECT
      *
    FROM
      YAS.`YAS-COR-tblDOR`
    WHERE
      is_new("YAS_YAS-COR-tblDOR", lastUpdatedTime)
  ),
  dor_full AS (
    SELECT
      cast(d.YasDate AS timestamp) AS timestamp,
      d.RoundNetValue AS value,
      c.id AS order_of_header,
      c.header AS header,
      c.site AS site,
      c.unit AS unit,
      c.area AS area,
      c.product AS product
    FROM
      dor d
      LEFT JOIN dor_cfg c ON d.tblCfgDOR_OrderOfHeader = c.id
  ),
  final_table AS (
    SELECT
      "CL-MS1:NetProduction" AS externalId,
      timestamp,
      max(value) AS value
    FROM
      dor_full
    WHERE
      site = "Clear Lake"
      AND area = "Fairway"
      AND unit = "MeOH"
      AND product = "MeOH"
      AND header = "MeOH Net Production (lbs)"
      AND isnotnull(timestamp)
      AND isnotnull(value)
    GROUP BY
      timestamp
  )
SELECT
  *
FROM
  final_table