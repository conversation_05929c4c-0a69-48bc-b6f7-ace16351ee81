import logging
import os
import time
from typing import Callable, List

from cognite.client import Cognite<PERSON>lient
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.cognite_utils import zip_and_upload_folder
from core.env_variables import EnvVariables
from core.function_configuration import FunctionConfiguration, ScheduleParameters
from core.settings import load_variables
from pydantic import TypeAdapter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

current_path = os.path.dirname(__file__)

logger.info("Loading environment variables")
env_variables = EnvVariables()

logger.info("Creating Cognite Client")
client: CogniteClient = CogniteClientFactory.create(env_variables=env_variables)

from cognite.client.data_classes.data_modeling.query import (
  NodeResultSetExpression,
  Query,
  Select,
  SourceSelector,
)
from cognite.client.data_classes.filters import And, Exists, HasData


# Manual OEE
# Comment site if you want to include it in the deployment
IGNORE_SITES = [
    "STS-CAN", # Cangrejera
    "STS-BCH", # Boucherville
    "STS-EVA", # Evansville
    "STS-FLO", # Florence
    "STS-NPT", # Newport
    "STS-OBH", # Oberhausen
    "STS-PEN", # Pensacola
    "STS-PER", # Perstorp
    "STS-SHY", # Shelby
    "STS-SPS", # Singapore
    "STS-SHE", # Songjang
    "STS-UTZ", # Utzenfeld
    "STS-WIL", # Wilmington
    "STS-WIN", # Winona
    # "STS-GEL", # Geleen
]

CELANESE_IT_SITES = [
    "STS-GEL", # Geleen
]

def status_check(function):
    logger.info("Verifying function deployment")
    start_time = time.time()
    # Repeat until status is ready
    while function.status != "Ready":
        function.update()
        time_elapsed = int(time.time() - start_time)

        print(function.status + f". Waiting for {time_elapsed} seconds", end="\r")

        if function.status == "Failed":
            print("Failed to deploy function")
            raise Exception("Failed to deploy function")
        time.sleep(5)
    else:
        print(f"Function is successfully deployed. Wait time: {time_elapsed} seconds.")
        logger.info(
            f"Function is successfully deployed. Wait time: {time_elapsed} seconds."
        )


def _build_reporting_line_query(cursor=None):
    """Constrói a query GraphQL para buscar listOEEInputTagConfiguration com paginação."""
    after_cursor = f', after: "{cursor}"' if cursor else ""

    return f"""
    {{
        listOEEInputTagConfiguration(first: 1000 {after_cursor}) {{
            items {{
                externalId
                reportingLine {{
                    externalId
                    isActive
                    isDeleted
                    reportingSites {{
                        items {{
                            externalId
                            isActive
                            isDeleted
                        }}
                    }}
                }}
            }}
            pageInfo {{
                hasNextPage
                endCursor
            }}
        }}
    }}
    """
    
def fetch_reporting_lines():
    data_model_id = DataModelId(
        env_variables.cognite.data_model_space,
        env_variables.cognite.data_model_external_id,
        env_variables.cognite.data_model_version,
    )

    reporting_lines = {}
    cursor = None

    while True:
        query_result = client.data_modeling.graphql.query(
            data_model_id,
            _build_reporting_line_query(cursor)
        )

        data = query_result.get("listOEEInputTagConfiguration", {})
        items = data.get("items", [])
        page_info = data.get("pageInfo", {})

        for item in items:
            reporting_line = item.get("reportingLine", {})
            
            if not reporting_line:
                continue
            
            reporting_line_id = reporting_line.pop("externalId", None)

            reporting_sites = reporting_line.get("reportingSites", {}).get("items", [])
            reporting_site = reporting_sites[0] if reporting_sites else None 

            if reporting_line_id in reporting_lines:
                continue

            reporting_lines[reporting_line_id] = {
                "externalId": reporting_line_id,
                "isActive": reporting_line.get("isActive"),
                "isDeleted": reporting_line.get("isDeleted"),
                "reportingSite": reporting_site,
            }

        if page_info.get("hasNextPage"):
            cursor = page_info.get("endCursor")
        else:
            break

    return list(reporting_lines.values())

def filter_reporting_lines_by_site(reporting_lines, site_external_id):
    filtered_lines = [
        line for line in reporting_lines
        if line.get("reportingSite") and line["reportingSite"].get("externalId") == site_external_id
    ]
    
    return filtered_lines

def chunk_list(data, chunk_size):
    for i in range(0, len(data), chunk_size):
        yield data[i:i + chunk_size]


def deploy_functions_common(filter_function: Callable[[List[str]], List[str]]):
    logger.info("Fetching supported sites for oee functions")

    config_view_ext_id = "OEEReportingSiteConfiguration"
    view_space = env_variables.cognite.data_model_space
    config_views = client.data_modeling.views.retrieve(
        (view_space, config_view_ext_id), all_versions=False
    )
    
    if len(config_views) == 0:
        raise Exception(f"View {config_view_ext_id} not found in {view_space} space.")

    config_view = config_views[0].as_id()
    site_field_name = "reportingSite"
    query_name = "configs"

    configs_query = Query(
        with_={
            query_name: NodeResultSetExpression(
                filter=And(
                    HasData(views=[config_view]),
                    Exists(config_view.as_property_ref(site_field_name)),
                ),
                limit=10000,
            )
        },
        select={
            query_name: Select(
                [SourceSelector(source=config_view, properties=[site_field_name])],
                limit=10000,
            )
        },
    )
    configs = client.data_modeling.instances.query(configs_query)[query_name]

    supported_sites_ext_ids = filter_function([
        site for site in set(r.properties[config_view][site_field_name]["externalId"] for r in configs)
        if site not in IGNORE_SITES
    ])

    if len(supported_sites_ext_ids) == 0:
        raise Exception(
            f"No reporting sites found in {config_view_ext_id}. No oee function will be deployed."
        )
    
    reporting_lines = fetch_reporting_lines()
    
    for site_ext_id in supported_sites_ext_ids:
        site_code = site_ext_id.replace("STS-", "")
        function_name_base = f"OEE-EF-{site_code}"
        filtered_lines = filter_reporting_lines_by_site(reporting_lines, site_ext_id)

        # Separate RLN-NANCELLN1 from other lines
        nancell_line = [line for line in filtered_lines if line.get("externalId") == "RLN-NANCELLN1"]
        other_lines = [line for line in filtered_lines if line.get("externalId") != "RLN-NANCELLN1"]

        chunks = list(chunk_list(other_lines, 10))
        if nancell_line:
            chunks.append(nancell_line)

        for idx, chunk in enumerate(chunks):
            function_name = f"{function_name_base}-{idx+1}"
            only_lines = [line["externalId"] for line in chunk]

            func_data = FunctionConfiguration(
                functionName=function_name,
                path=["oee_function"],
                runtime="py311",
                parameters=[
                    ScheduleParameters(
                        scheduleName=f"oee_function_{site_code}_schedule_{idx+1}",
                        cronn=os.getenv("CRON_EXPRESSION") or "*/5 * * * *",
                        data={
                            "site_external_id": site_ext_id,
                            "only_lines": only_lines
                        },
                    )
                ],
            )

            try:
                logger.info(f"Starting {function_name} deploy with {len(only_lines)} reporting lines")
                _deploy_function(func_data=func_data)
            except Exception as e:
                logger.warn(f"Error when deploying {function_name} function: {e}")
                logger.info(f"Skipping {function_name} function deploy.")
                continue

def deploy_oee_functions(sites: List[str]) -> List[str]:
    sites_to_exclude= CELANESE_IT_SITES
    sites_to_deploy = [site for site in sites if site not in sites_to_exclude]
    logger.info(f"Deploying OEE functions for sites: {sites_to_deploy}")
    return sites_to_deploy
    

def deploy_celanese_oee_functions(sites: List[str]) -> List[str]:
    sites_to_include = CELANESE_IT_SITES
    sites_to_deploy = [site for site in sites if site in sites_to_include]
    logger.info(f"Deploying Celanese OEE functions for sites: {sites_to_deploy}")
    return sites_to_deploy


def deploy_function(function_name: str):
    logger.info("Finding function configuration")
    functions_str = open(
        os.path.join(current_path, "core", "functions-configurations.json")
    )
    type_adapter = TypeAdapter(List[FunctionConfiguration])
    functions = type_adapter.validate_json(functions_str.read())

    func_data: FunctionConfiguration = next(
        filter(lambda func: func.function_name == function_name, functions), None
    )

    if func_data is None:
        raise Exception(f"The function {function_name} was not found.")

    _deploy_function(func_data=func_data)


def _deploy_function(func_data: FunctionConfiguration):
    function_name = func_data.function_name
    logger.info("Checking function variables")

    function_location = os.path.join(current_path, "..", *func_data.path)
    logger.info(f"Function location: {function_location}")
    env_vars = load_variables(func_data.function_name)

    try:
        logger.info("Deleting existent function")
        client.functions.delete(external_id=function_name)
        logger.info("Existing function deleted")
    except Exception as e:
        logger.info(f"Function {function_name} doens't exist. Nothing was deleted.")
        print(e)

    logger.info(
        f"Loading function files from path {function_location} and uploading to Cognite as a File"
    )
    file_id = zip_and_upload_folder(
        cognite_client=client,
        data_set_id=env_variables.cognite.data_set_id,
        folder=function_location,
        name=function_name,
        external_id=function_name,
    )

    logger.info(f"Creating {function_name} Function in {env_variables.cognite.project}")
    func = client.functions.create(
        name=function_name,
        external_id=function_name,
        file_id=file_id,
        runtime=func_data.runtime,
        env_vars=env_vars,
    )

    for s in func_data.parameters:
        logger.info(f"Creating schedule {s.schedule_name}-schedule")
        logger.info(f"Data: {s.data}")

        client.functions.schedules.create(
            name=s.schedule_name,
            cron_expression=s.cronn,
            client_credentials={
                "client_id": env_vars["AUTH_CLIENT_ID"],
                "client_secret": env_vars["AUTH_SECRET"],
            },
            function_id=func.id,
            data=s.data,
        )

    logger.info("Function created")
    # TODO: implement a "smart check" such that it does not wait for only one function deploy
    # status_check(func)


def deploy(function_name: str):
    logger.info(f"Starting deployment for function {function_name}")

    if os.getenv("LOG_ENV_VARS") == "1":
        auth_vars_as_dict = env_variables.auth.model_dump()
        auth_vars_as_dict.pop("cicd_secret", None)
        auth_vars_as_dict.pop("secret", None)
        cognite_vars_as_dict = env_variables.cognite.model_dump()
        logger.info(f"auth_vars: {auth_vars_as_dict}")
        logger.info(f"cognite_vars: {cognite_vars_as_dict}")

    match function_name:
        case "oee_function":
            deploy_functions_common(deploy_oee_functions)
            return
        case "celanese_oee_function":
            deploy_functions_common(deploy_celanese_oee_functions)
            return
        case "all":
            deploy_functions_common(deploy_oee_functions)
            deploy_functions_common(deploy_celanese_oee_functions)
            return
        case _:
            return


    deploy_function(function_name=function_name)