from datetime import time
from typing import Any

import numpy as np
import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.base_service import BaseService
from app.utils.msdp_utils import get_msdp_value as utils_get_msdp_value
from app.utils.product_matching_utils import create_product_match_filter


class SheRdpEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self.base_service = BaseService()

        self._day_data = None
        self._rlt_key = "rlt"
        self._rlt_no_demand_key = "rlt_no_demand"
        self._pc_type = "Batch"

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "3d": self.identify_events_typeIIId,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
            "4c": self.identify_events_typeIVc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - (ProdStatus1 = 0 & TotalFeed = 0
        # & (BatchID = Batch number without Trial or ProdStatus4 = Batch number without Trial)) OR
        # (ProdStatus3 = 0 & ProdStatus5 = 0)
        data = data.assign(
            event1a_start=(
                ((data["ProductionLineStatus1"] == 0) & (data["TotalFeed"] == 0)
                & (~(data["BatchID"].str.contains('Trial')) | ~(data["ProductionLineStatus4"].str.contains('Trial')))) |
                ((data["ProductionLineStatus3"] == 0) & (data["ProductionLineStatus5"] == 0))
            )
        )

        # event trigger end - (ProdStatus1 = 1 & TotalFeed > 0) or
        # BatchID = Batch number with Trial or ProdStatus4 = Batch number with Trial
        data = data.assign(
            event1a_end=(
              (
                (data["ProductionLineStatus1"] != 0) & (data["TotalFeed"] > 0)
                & ((data["ProductionLineStatus1"].shift(1) == 0) | (data["TotalFeed"].shift(1) <= 0))
              ) | (data['BatchID'].str.contains('Trial'))
               |  (data['ProductionLineStatus4'].str.contains('Trial'))
            )
        )

        data = data.assign(
            event1a_start=(
               (data["event1a_start"] == True)
               & (data["event1a_start"].shift(1) != True)
            )
        )

        data = data.assign(
            event1a_end=(
               (data["event1a_end"] == True)
               & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - (ProdStatus1 goes from 0 to 1) & (TotalFeed goes from 0 to > 0)
        data = data.assign(
            event2a_start=(
                ((data["ProductionLineStatus1"] != 0) & (data["ProductionLineStatus1"].shift(1) == 0))
                & (data["TotalFeed"] > 0)
            )
        )

        # event trigger end - (ProdStatus3 = 1 | ProdStatus5 = 1)
        data = data.assign(
            event2a_end=(
                ((data["ProductionLineStatus3"] != 0) & (data["ProductionLineStatus3"].shift(1) == 0))
                | ((data["ProductionLineStatus5"]  != 0) & (data["ProductionLineStatus5"].shift(1) == 0))
            )
        )

        data = data.assign(
            event2a_start=(
               (data["event2a_start"] == True)
               & (data["event2a_start"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - (ProdStatus3 = 1 & ProdStatus4 = Batch number with Trial)
        # | (ProdStatus5 = 1 & BatchID = Batch number with Trial)
        data = data.assign(
            event3a_start=(
                ((data["ProductionLineStatus3"] == 1) & (data["ProductionLineStatus4"].str.contains('Trial')))
                | ((data["ProductionLineStatus5"] == 1) & (data["BatchID"].str.contains('Trial')))
            )
        )

        # event trigger end - (ProdStatus3 = 0 & ProdStatus4 = Batch number without Trial)
        # | (ProdStatus5 = 0 & BatchID = Batch number without Trial)
        data = data.assign(
            event3a_end=(
                ((data["ProductionLineStatus3"] == 0) & (~data["ProductionLineStatus4"].str.contains('Trial')))
                | ((data["ProductionLineStatus5"] == 0) & (~data["BatchID"].str.contains('Trial')))
            )
        )

        data = data.assign(
            event3a_start=(
               (data["event3a_start"] == True)
               & (data["event3a_start"].shift(1) != True)
            )
        )

        data = data.assign(
            event3a_end=(
               (data["event3a_end"] == True)
               & (data["event3a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIIIb(self):
        pass

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIc event
        :rtype: pd.DataFrame
        """

        # event trigger start - (ProdStatus1 = 0 & TotalFeed = 0 &
        # (BatchID = Batch number with Trial or ProdStatus4 = Batch number with Trial)) OR
        # (ProdStatus3 = 0 & ProdStatus5 = 0
        # & (BatchID = Batch number with Trial or ProdStatus4 = Batch number with Trial))
        data = data.assign(
            event3c_start=(
                ((data["ProductionLineStatus1"] == 0) & (data["TotalFeed"] == 0)
                & ((data["BatchID"].str.contains('Trial')) | (data["ProductionLineStatus4"].str.contains('Trial')))) |
                ((data["ProductionLineStatus3"] == 0) & (data["ProductionLineStatus5"] == 0)
                & ((data["BatchID"].str.contains('Trial')) | (data["ProductionLineStatus4"].str.contains('Trial'))))
            )
        )

        # event trigger end - ProdStatus3 = 1 & ProdStatus5 = 1 & (BatchID = BatchID of start event)
        # or BatchID = Batch number without Trial or ProdStatus4 = Batch number without Trial
        data = data.assign(
            event3c_end=(
                (
                    (data["ProductionLineStatus3"] != 0)
                    & (data["ProductionLineStatus5"] != 0)
                    & (data["event3c_start"].shift(1) == True)
                )
                | (
                    (data["ProductionLineStatus3"] != 0)
                    & (data["ProductionLineStatus5"] != 0)
                    & ((data["ProductionLineStatus3"].shift(1) == 0) | (data["ProductionLineStatus5"].shift(1) == 0))
                    & ( (~data["BatchID"].str.contains('Trial')) | (~data["ProductionLineStatus4"].str.contains('Trial')))
                )
                | (
                    (~data["BatchID"].str.contains('Trial'))
                    & (data["BatchID"] != data["BatchID"].shift(1))
                )
                | (
                    (~data["ProductionLineStatus4"].str.contains('Trial'))
                    & (data["ProductionLineStatus4"] != data["ProductionLineStatus4"].shift(1))
                )
            )
        )

        # fix event trigger start
        data = data.assign(
            event3c_start=(
                (data["event3c_start"] == True)
                & (data["event3c_start"].shift(1) != True)
            )
        )

        # filter only true booleans to get the correct events
        data = data[
            (data["event3c_start"] == True) | (data["event3c_end"] == True)
        ]

        data = data.assign(
            event3c_start=(
                (data["event3c_start"] == True)
                & (data["BatchID"] == data["BatchID"].shift(-1))
            )
        )

        data = data.assign(
            event3c_end=(
                (data["event3c_end"] == True)
                & (data["BatchID"] == data["BatchID"].shift(1))
            )
        )

        return data

    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIId

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIc event
        :rtype: pd.DataFrame
        """

        # event trigger start - (ProdStatus1 = 0 & ProdStatus2 = 0 &
        # (BatchID = Batch number with Trial or ProdStatus4 = Batch number with Trial)) OR
        # (ProdStatus3 = 0 & ProdStatus5 = 0
        # & (BatchID = Batch number with Trial or ProdStatus4 = Batch number with Trial))
        data = data.assign(
            event3d_start=(
                ((data["ProductionLineStatus1"] == 0) & (data["TotalFeed"] == 0)
                & ((data["BatchID"].str.contains('Trial')) | (data["ProductionLineStatus4"].str.contains('Trial')))) |
                ((data["ProductionLineStatus3"] == 0) & (data["ProductionLineStatus5"] == 0)
                & ((data["BatchID"].str.contains('Trial')) | (data["ProductionLineStatus4"].str.contains('Trial'))))
            )
        )

        # event trigger end - ProdStatus3 = 1 & ProdStatus5 = 1 & (BatchID = BatchID of start event)
        # or BatchID = Batch number without Trial or ProdStatus4 = Batch number without Trial
        data = data.assign(
            event3d_end=(
                (
                    (data["ProductionLineStatus3"] != 0)
                    & (data["ProductionLineStatus5"] != 0)
                    & (data["event3d_start"].shift(1) == True)
                )
                | (
                    (data["ProductionLineStatus3"] != 0)
                    & (data["ProductionLineStatus5"] != 0)
                    & ((data["ProductionLineStatus3"].shift(1) == 0) | (data["ProductionLineStatus5"].shift(1) == 0))
                    & ( (~data["BatchID"].str.contains('Trial')) | (~data["ProductionLineStatus4"].str.contains('Trial')))
                )
                | (
                    (~data["BatchID"].str.contains('Trial'))
                    & (data["BatchID"] != data["BatchID"].shift(1))
                )
                | (
                    (~data["ProductionLineStatus4"].str.contains('Trial'))
                    & (data["ProductionLineStatus4"] != data["ProductionLineStatus4"].shift(1))
                )
            )
        )

        # fix event trigger start
        data = data.assign(
            event3d_start=(
                (data["event3d_start"] == True)
                & (data["event3d_start"].shift(1) != True)
            )
        )

        # filter only true booleans to get the correct events
        data = data[
            (data["event3d_start"] == True) | (data["event3d_end"] == True)
        ]

        data = data.assign(
            event3d_start=(
                (data["event3d_start"] == True)
                & (data["BatchID"] != data["BatchID"].shift(-1))
            )
        )

        data = data.assign(
            event3d_end=(
                (data["event3d_end"] == True)
                & (data["BatchID"] != data["BatchID"].shift(1))
            )
        )

        return data

    def identify_events_typeIVa(self, data: pd.DataFrame, **args):
        """
        identifies events of type IIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        event1a_losses = self.create_dataframe_loss_1a_event(data)
        event2a_losses = self.create_dataframe_loss_2a_event(data)
        event3a_losses = self.create_dataframe_loss_3a_event(data)

        day_data = self.__create_day_data(data, event1a_losses, event2a_losses, event3a_losses)

        if day_data.empty:
            return day_data


        day_data = day_data[day_data[self._rlt_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIVb(self, data: pd.DataFrame, **args):

        event1a_losses = self.create_dataframe_loss_1a_event(data)
        event2a_losses = self.create_dataframe_loss_2a_event(data)
        event3a_losses = self.create_dataframe_loss_3a_event(data)

        day_data = self.__create_day_data(data, event1a_losses, event2a_losses, event3a_losses)

        if day_data.empty:
            return day_data

        day_data = day_data[(day_data[self._rlt_key] < 0) & (day_data[self._rlt_key] != -np.inf)]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIVc(self, data: pd.DataFrame, **args):

        event1a_losses = self.create_dataframe_loss_1a_event(data)
        event2a_losses = self.create_dataframe_loss_2a_event(data)
        event3a_losses = self.create_dataframe_loss_3a_event(data)

        day_data = self.__create_day_data(data, event1a_losses, event2a_losses, event3a_losses)

        if day_data.empty:
            return day_data


        day_data = day_data[day_data[self._rlt_no_demand_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_no_demand_key]

        return day_data

    def __create_day_data(self, data: pd.DataFrame, event_loss1a: pd.DataFrame,
                      event_loss2a: pd.DataFrame, event_loss3a: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 4a, 4b and 4c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        if self._day_data is not None:
            return self._day_data.copy()

        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None if first_timestamp.time() == mid_night_time else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            ((start_cutoff is not None) & (data["index"].dt.date > start_cutoff))
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        total_produced_key = "material_produced"

        data[total_produced_key] = (
            data["TotalFeed"]/1000
        )

        data['is_below_0_key'] = (
            (data["TotalFeed"] <= 0)
        )

        data['is_above_0_key'] = (
            (data["TotalFeed"] > 0)
        )

        data['trial_key_prodstatus4'] = (
            (data["ProductionLineStatus4"].str.contains('Trial'))
        )

        data['trial_key_batchid'] = (
            (data["BatchID"].str.contains('Trial'))
        )

        running_time_key = "running_time"

        data[running_time_key] = 0

        sec_to_hour_factor = 1 / 3600

        data.loc[data['is_above_0_key'], running_time_key] = (
            data.loc[data['is_above_0_key'], "dt"] * sec_to_hour_factor
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "ProductDescription"], dropna=False
        ).agg(
            {
                'is_below_0_key': "all",
                'is_above_0_key': "all",
                'trial_key_prodstatus4': "all",
                'trial_key_batchid': "all",
                total_produced_key: "first",
                running_time_key: "sum",
            }
        )

        data = data.reset_index().rename(columns = {'index': 'timestamp'})

        if not event_loss1a.empty:
          data = pd.merge(data, event_loss1a, left_on='timestamp', right_on='event1a_date_loss', how='left')
          data['loss_duration_seconds_1a'] = data['loss_duration_seconds_1a'].fillna(0)
          data[running_time_key] = 86400 - (data['loss_duration_seconds_1a'])

        if not event_loss2a.empty:
          data = pd.merge(data, event_loss2a, left_on='timestamp', right_on='event2a_date_loss', how='left')
          data['loss_duration_seconds_2a'] = data['loss_duration_seconds_2a'].fillna(0)
          data[running_time_key] = data[running_time_key] - (data['loss_duration_seconds_2a'])

        if not event_loss3a.empty:
          data = pd.merge(data, event_loss3a, left_on='timestamp', right_on='event3a_date_loss', how='left')
          data['loss_duration_seconds_3a'] = data['loss_duration_seconds_3a'].fillna(0)
          data[running_time_key] = data[running_time_key] - (data['loss_duration_seconds_3a'])

        # timestamps can be repeated for multiple products (product tag's value during the hour)
        # this gets unique timestamps based on products with the highest total_produced value for that hour
        data['material_produced'] = data['material_produced'].fillna(0)
        data = data.loc[data.groupby("timestamp")["material_produced"].idxmax()]

        data_to_get_products_from = data.copy().drop(
            labels=[col for col in data.columns if col not in ["timestamp", "ProductDescription"]],
            axis=1,
        )

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        filter_msdp = (
            self._msdp["reportingUnitExternalId"] == self._reporting_unit_external_id
        )

        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_key = "msdp"
        scheduled_rate_key = "scheduledRate"

        per_day_to_per_hour_factor = 1 / 24

        data[[msdp_key, scheduled_rate_key]] = (
            data.apply(
                self.get_msdp_value,
                msdp_data=msdp_data,
                data_aux=[msdp_key, scheduled_rate_key],
                axis=1,
                result_type="expand",
            )
            * per_day_to_per_hour_factor
        )

        rlt_key = self._rlt_key
        rlt_no_demand_key = self._rlt_no_demand_key

        tolerance = 0.001

        rlt_schema = [
            (  # 4a
                "4a",
                lambda df: ((df['is_below_0_key']) | (df['trial_key_prodstatus4']) | (df['trial_key_batchid'])),
                {rlt_key: lambda _: 0},
            ),
            (
                "4b",
                lambda df: (df[total_produced_key] - df[msdp_key]).abs()
                < tolerance,  # produced = msdp
                {rlt_key: lambda _: 0},
            ),
            (
                "4c1",
                lambda df: ((df[total_produced_key] - df[msdp_key]) > tolerance)
                & (
                    df['is_above_0_key']
                ),  # produced > msdp & total feed is above 0 during hour
                {
                    rlt_key: lambda df: (df[msdp_key] - df[total_produced_key])
                    / df[msdp_key]
                },
            ),
            (
                "4c2",
                lambda df: ((df[total_produced_key] - df[msdp_key]) > tolerance)
                & (
                    df['is_above_0_key'] == False
                ),  # produced > msdp & total feed is not above 0 during hour
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] * df[running_time_key]) - df[total_produced_key]
                    )
                    / df[msdp_key]
                },
            ),
            (
                "4d1",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]) > tolerance)
                & ((df[scheduled_rate_key] - df[total_produced_key]) > tolerance)
                & (
                    df['is_above_0_key']
                ),  # msdp > scheduled & scheduled > Material produced & is above 0 during hour
                {
                    rlt_key: lambda df: (
                        df[scheduled_rate_key] - df[total_produced_key]
                    )
                    / df[scheduled_rate_key],
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df[scheduled_rate_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "4d2",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]) > tolerance)
                & ((df[scheduled_rate_key] - df[total_produced_key]) > tolerance)
                & (
                    df['is_above_0_key'] == False
                ),  # msdp > scheduled & scheduled > Material produced & is not above 0 during hour
                {
                    rlt_key: lambda df: (
                        (df[scheduled_rate_key] * df[running_time_key])
                        - df[total_produced_key]
                    )
                    / df[scheduled_rate_key],
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * df[running_time_key])
                        - (df[scheduled_rate_key] * df[running_time_key])
                    )
                    / df[msdp_key],
                },
            ),
            (
                "4e1",
                lambda df: ((df[msdp_key] - df[total_produced_key]) > tolerance)
                & ((df[total_produced_key] - df[scheduled_rate_key]) > tolerance)
                & (
                    df['is_above_0_key']
                ),  # msdp > Material produced & Material produced > scheduled & is above 0 during hour
                {
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df[total_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "4e2",
                lambda df: ((df[msdp_key] - df[total_produced_key]) > tolerance)
                & ((df[total_produced_key] - df[scheduled_rate_key]) > tolerance)
                & (
                    df['is_above_0_key'] == False
                ),  # msdp > Material produced & Material produced > scheduled & is not above 0 during hour
                {
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * df[running_time_key]) - df[total_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "4f1",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]).abs() > tolerance)
                & ((df[scheduled_rate_key] - df[total_produced_key]) > tolerance)
                & (
                    df['is_above_0_key']
                ),  # msdp = Material produced & scheduled > Material produced & is above 0 during hour
                {
                    rlt_key: lambda df: (df[msdp_key] - df[total_produced_key])
                    / df[msdp_key],
                },
            ),
            (
                "4f2",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]).abs() > tolerance)
                & ((df[scheduled_rate_key] - df[total_produced_key]) > tolerance)
                & (
                    df['is_above_0_key'] == False
                ),  # msdp = Material produced & scheduled > Material produced & is not above 0 during hour
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] * df[running_time_key]) - df[total_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "4g1",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]) > tolerance)
                & ((df[scheduled_rate_key] - df[total_produced_key]).abs() > tolerance)
                ,  # msdp > scheduled & scheduled = Material produced
                {
                    rlt_key: lambda df: (df[msdp_key] - df[total_produced_key])
                    / df[msdp_key],
                },
            ),
        ]

        data[rlt_key] = pd.Series(dtype="float")
        data[rlt_no_demand_key] = pd.Series(dtype="float")

        condition_key = "rlt_condition"
        data[condition_key] = pd.NA

        for (
            rlt_condition_code,
            get_filter_mask,
            target_col_to_calc_func_map,
        ) in rlt_schema:
            filter_mask = (
                get_filter_mask(data)
                & data[rlt_key].isna()
                & data[rlt_no_demand_key].isna()
            )
            for target_col, calc_func in target_col_to_calc_func_map.items():
                data.loc[filter_mask, target_col] = calc_func(data.loc[filter_mask])
                data.loc[filter_mask, condition_key] = rlt_condition_code

        data[rlt_key] = data[rlt_key].fillna(0)
        data[rlt_no_demand_key] = data[rlt_no_demand_key].fillna(0)

        data = (
            data.groupby(
                pd.Grouper(key="timestamp", freq="D"),
            )
            .agg(
                {
                    msdp_key: "sum",
                    scheduled_rate_key: "sum",
                    total_produced_key: "first",
                    rlt_key: "sum",
                    rlt_no_demand_key: "sum",
                    running_time_key: "sum",
                },
            )
            .reset_index()
        )

        # no events should be generated if total_produced <= 0
        data = data[data[total_produced_key] > 0]

        data["start_time"] = data["timestamp"]
        data["end_time"] = data["timestamp"] + pd.Timedelta(days=1)

        if data.shape[0] == 0:
            self._day_data = data
            return data.copy()

        # get products reported by tag on start_time
        data = data.merge(
            data_to_get_products_from,
            how="left",
            left_on="start_time",
            right_on="timestamp",
            suffixes=(None, "_to_exclude"),
        )

        # data.loc[(~data["Product"].isin(product_list)), "Product"] = pd.NA

        # get msdp and scheduled rate on start_time
        data["Year"] = data["start_time"].dt.year
        data["Month"] = data["start_time"].dt.month
        data["Day"] = data["start_time"].dt.day
        data[[msdp_key, scheduled_rate_key]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=[msdp_key, scheduled_rate_key],
            axis=1,
            result_type="expand",
        )

        msdp_eq_scheduled_rate_eq_total_produced = (
            ((data[total_produced_key] - data[msdp_key]).abs() < tolerance)
            & ((data[total_produced_key] - data[scheduled_rate_key]).abs() < tolerance)
            & ((data[msdp_key] - data[scheduled_rate_key]).abs() < tolerance)
        )

        # no events should be generated if msdp = scheduled rate = total_produced
        data = data[~msdp_eq_scheduled_rate_eq_total_produced]

        data = data.rename(columns={"timestamp": "index"}).drop(columns=["ProductDescription"])

        hours_to_seconds_factor = 3600
        data[rlt_key] = data[rlt_key] * hours_to_seconds_factor
        data[rlt_no_demand_key] = data[rlt_no_demand_key] * hours_to_seconds_factor

        self._day_data = data

        return data.copy()

    def get_msdp_value(
        self,
        row: pd.Series,
        msdp_data: pd.DataFrame,
        data_aux: str | list[str],
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        product = row["ProductDescription"]
        if pd.isna(product):
            return pd.array([0] * len(data_aux))

        # Use the product_matching_utils to create filter mask
        combined_mask = create_product_match_filter(msdp_data, product)
        
        if not combined_mask.any():
            product_filter = msdp_data["productGroup"] == ""
        else:
            product_filter = combined_mask
        
        # Use the msdp_utils to get MSDP value
        return utils_get_msdp_value(row, msdp_data, data_aux, product_filter)

    def create_dataframe_loss_1a_event(
          self, data: pd.DataFrame
      ) -> pd.DataFrame:

        # SHE RDP demands creating events 1a to calculate running time
        df_losses_1a = self.identify_events_typeIa(data)

        df_losses_1a.dropna(
            subset=[f"event1a_start", f"event1a_end"],
            inplace=True,
        )

        df_losses_1a.set_index("index", inplace=True)

        df_losses_1a = self.base_service.create_event_dataframe(df_losses_1a, '1a', self._pc_type)

        if df_losses_1a.empty:
          return df_losses_1a

        shift_frapom = ['2024-01-01 07:30:00', '2024-01-01 19:30:00', '2024-01-01 00:00:00']
        df_losses_1a = self._identify_working_shifts_transitions(df_losses_1a, '1a', shift_frapom)

        df_losses_1a = self.base_service.calculate_time_duration(df_losses_1a, 'event1a_start', 'event1a_end')

        df_losses_1a['event1a_date_loss'] = df_losses_1a['event1a_start'].dt.normalize()

        df_losses_1a.drop(columns = ['event1a_start', 'event1a_end', 'event_definition'], inplace = True)

        df_losses_1a.rename(columns = {'total_duration_seconds': 'loss_duration_seconds_1a'}, inplace = True)

        df_losses_1a = df_losses_1a.groupby('event1a_date_loss', as_index=False).agg({'loss_duration_seconds_1a': 'sum'})

        return df_losses_1a

    def create_dataframe_loss_2a_event(
          self, data: pd.DataFrame
      ) -> pd.DataFrame:

        # SHE RDP demands creating events 2a to calculate running time
        df_losses_2a = self.identify_events_typeIIa(data)

        df_losses_2a.dropna(
            subset=[f"event2a_start", f"event2a_end"],
            inplace=True,
        )

        df_losses_2a.set_index("index", inplace=True)

        df_losses_2a = self.base_service.create_event_dataframe(df_losses_2a, '2a', self._pc_type)

        if df_losses_2a.empty:
          return df_losses_2a

        shift_frapom = ['2024-01-01 07:30:00', '2024-01-01 19:30:00', '2024-01-01 00:00:00']
        df_losses_2a = self._identify_working_shifts_transitions(df_losses_2a, '2a', shift_frapom)

        df_losses_2a = self.base_service.calculate_time_duration(df_losses_2a, 'event2a_start', 'event2a_end')

        df_losses_2a['event2a_date_loss'] = df_losses_2a['event2a_start'].dt.normalize()

        df_losses_2a.drop(columns = ['event2a_start', 'event2a_end', 'event_definition'], inplace = True)

        df_losses_2a.rename(columns = {'total_duration_seconds': 'loss_duration_seconds_2a'}, inplace = True)

        df_losses_2a = df_losses_2a.groupby('event2a_date_loss', as_index=False).agg({'loss_duration_seconds_2a': 'sum'})

        return df_losses_2a

    def create_dataframe_loss_3a_event(
          self, data: pd.DataFrame
      ) -> pd.DataFrame:

        # SHE RDP demands creating events 3a to calculate running time
        df_losses_3a = self.identify_events_typeIIIa(data)

        df_losses_3a.dropna(
            subset=[f"event3a_start", f"event3a_end"],
            inplace=True,
        )

        df_losses_3a.set_index("index", inplace=True)

        df_losses_3a = self.base_service.create_event_dataframe(df_losses_3a, '3a', self._pc_type)

        if df_losses_3a.empty:
          return df_losses_3a

        shift_frapom = ['2024-01-01 07:30:00', '2024-01-01 19:30:00', '2024-01-01 00:00:00']
        df_losses_3a = self._identify_working_shifts_transitions(df_losses_3a, '3a', shift_frapom)

        df_losses_3a = self.base_service.calculate_time_duration(df_losses_3a, 'event3a_start', 'event3a_end')

        df_losses_3a['event3a_date_loss'] = df_losses_3a['event3a_start'].dt.normalize()

        df_losses_3a.drop(columns = ['event3a_start', 'event3a_end', 'event_definition'], inplace = True)

        df_losses_3a.rename(columns = {'total_duration_seconds': 'loss_duration_seconds_3a'}, inplace = True)

        df_losses_3a = df_losses_3a.groupby('event3a_date_loss', as_index=False).agg({'loss_duration_seconds_3a': 'sum'})

        return df_losses_3a

    def _split_intervals(
        self, row: pd.DataFrame, event_definition: str, shifts: list[str]
    ) -> list:
        """
        splits the event frames into intervals based on the business rules defined. An event should end (and another should start) when:
        :param row: row containing the start time and the end times
        :type row: pd.DataFrame
        :param event_definition: definition of the event we are splitting
        :type event_definition: str
        :return: list of the events within the original events
        :rtype: list
        """

        # get variables to start split analysis
        intervals = []
        start = row[f"event{event_definition}_start"]
        end = row[f"event{event_definition}_end"]
        current_time = start

        # iterate through each of the timespans within the
        # original time frame

        shifts_times: list[pd.Timestamp] = [
            pd.Timestamp(shift) for shift in shifts
        ]

        while current_time < end:
            # build a candidate list to be the next time
            candidates = []

            for shift_time in shifts_times:
                entry = current_time.replace(
                    hour=shift_time.hour,
                    minute=shift_time.minute,
                    second=0,
                    microsecond=000000,
                )  # type: ignore
                if shift_time.hour == 0 and shift_time.minute == 0:
                    entry = entry + pd.DateOffset(days=1)
                candidates.append(entry)
            candidates.append(end)

            # find the closest timestamp and ensure it is newer than
            # the present timestamp
            next_time = min(candidates)
            is_next_time_lower = next_time <= current_time

            while is_next_time_lower:
                candidates.remove(next_time)
                next_time = min(candidates)
                is_next_time_lower = next_time <= current_time

            # append to the list a tuple with the timestamp of start, the
            # selected end and the status code. Update the current time
            to_append = [current_time, next_time]
            for col in row.index:
                if col not in [
                    f"event{event_definition}_start",
                    f"event{event_definition}_end",
                ]:
                    to_append.append(row[col])
            intervals.append(tuple(to_append))
            current_time = next_time

        return intervals

    def _identify_working_shifts_transitions(
        self, data: pd.DataFrame, event_definition: str, shifts: list[str]
    ) -> pd.DataFrame:
        """
        identifies working shifts transitions during events

        :param data: events data with start and end times
        :type data: pd.DataFrame
        :param event_definition: definition of the event we are splitting
        :type event_definition: str
        :return: event data with working shifts transitions
        :rtype: pd.DataFrame
        """

        # split intervals according to business rules
        new_rows = data.apply(
            self._split_intervals,
            event_definition=event_definition,
            shifts=shifts,
            axis=1,
        ).explode()

        # create data frame
        events_data = pd.DataFrame(new_rows.tolist(), columns=data.columns)

        return events_data


