from typing import Any, Optional

from pydantic import Field

from .node import Node


class Material(Node):
    name: Optional[str] = Field(default=None, alias="name")
    space: Optional[str] = Field(default=None, alias="space")
    externalId: Optional[str] = Field(default=None, alias="externalId")
    description: Optional[str] = Field(default=None, alias="description")
    
    @classmethod
    def from_cognite_response(cls, item: dict[str, Any]) -> "Material":
        return Material(
            externalId=item["externalId"],
            space=item["space"],
            name=item["name"],
        )
