from datetime import datetime
from typing import Optional

import pandas as pd
import pytz
from app.models.event_hierarchy_configuration import (
    EventHierarchyConfiguration,
)
from app.models.hourly_data import HourlyData
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
    InstanceSort,
    Node,
    ViewId,
)
from cognite.client.data_classes.data_modeling.query import (
    NodeResultSetExpression,
    Query,
    Select,
    SourceSelector,
)
from cognite.client.data_classes.filters import And, Prefix, Range
from dateutil.relativedelta import relativedelta

from .view_repository import ViewRepository


class HourlyDataRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        view_repository: ViewRepository,
    ) -> None:
        self._cognite_client = cognite_client
        self._view_repository = view_repository

    def create_events(self, events: list[HourlyData]) -> None:
        if not events:
            return
        view = self._get_view_id()

        nodes = [event.convert_to_cognite_node(view) for event in events]

        paginated_nodes = [
            nodes[1000 * i : 1000 * (i + 1)] for i in range(int(len(nodes) / 1000) + 1)
        ]

        for entries in paginated_nodes:
            self._cognite_client.data_modeling.instances.apply(nodes=entries)

    def delete_events(self, space: str) -> None:
        while True:
            instances = self._cognite_client.data_modeling.instances.list(
                space=space, limit=5000, sources=self._get_view_id()
            )
            if not instances:
                break
            self._cognite_client.data_modeling.instances.delete(
                nodes=instances.as_ids()
            )

    def _build_query(
        self,
        view: ViewId,
        date_key: str,
        external_ids: dict[str, Optional[datetime]],
    ):
        property_ref = view.as_property_ref(date_key)

        search_nodes = {
            external_id: NodeResultSetExpression(
                filter=(
                    Prefix(["node", "externalId"], external_id)
                    if not last_datetime
                    else And(
                        Prefix(["node", "externalId"], external_id),
                        Range(
                            property_ref,
                            lt=last_datetime.replace(microsecond=0).isoformat(),
                        ),
                    )
                ),
                sort=[
                    InstanceSort(
                        property_ref,
                        direction="descending",
                    )
                ],
                limit=1,
            )
            for external_id, last_datetime in external_ids.items()
        }
        return Query(
            with_=search_nodes,  # type: ignore
            select={
                node: Select(
                    [SourceSelector(view, [date_key])],
                )
                for node in search_nodes.keys()
            },
        )

    def _get_view_id(self):
        return self._view_repository.get_view_id("OEE_HourlyData")
