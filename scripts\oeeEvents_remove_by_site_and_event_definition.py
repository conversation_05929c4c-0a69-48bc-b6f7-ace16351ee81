import logging
import os
import sys
import pandas as pd
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables

"""
    ATENÇÃO: Este script irá deletar todos os eventos com um determinado prefixo e para um site especifico.
    isto se fez necessário pois os eventos de Excess MDR para o site de FORLI (STS-FOR) foram criados erroneamente;
"""


# Definindo o caminho do script para encontrar os pacotes corretamente
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

# Importando as classes e repositórios necessários
from oee_function.app.infra.logger_adapter import get_logger
from oee_function.app.repositories.view_repository import ViewRepository
from oee_function.app.repositories.OEEEvent_repository import OEEEventRepository  # Sua classe personalizada

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_cognite_client(variables: EnvVariables):
    """Cria uma instância do cliente Cognite usando as variáveis de ambiente."""
    return CogniteClientFactory.create(variables)

def create_repositories(variables: EnvVariables):
    """Inicializa os repositórios necessários para acessar os dados."""
    cognite_client = create_cognite_client(variables)
    data_model_id = DataModelId(
        variables.cognite.data_model_space,
        variables.cognite.data_model_external_id,
        variables.cognite.data_model_version,
    )
    return {
        "oee_event": OEEEventRepository(
            cognite_client,
            data_model_id,
            view_repository=ViewRepository(cognite_client, data_model_id),
        ),
    }

def get_all_oee_event_data(variables: EnvVariables, site_external_id: str, event_prefix: str = "Ex") -> pd.DataFrame:
    """Executa a query para buscar todos os dados de eventos OEE e retorna como DataFrame."""
    repositories = create_repositories(variables)
    oee_event_repository = repositories.get("oee_event")
    if oee_event_repository is None:
        logger.error("OEEEventRepository não encontrado.")
        return pd.DataFrame()

    # Busca todos os dados usando o novo método de paginação
    data = oee_event_repository.get_oee_event_data_as_dataframe(site_external_id, event_prefix)
    return data

def delete_oee_events(variables: EnvVariables, oee_event_data: pd.DataFrame) -> None:
    """Exclui eventos OEE listados em oee_event_data."""
    if oee_event_data.empty:
        logger.info("Nenhum evento para deletar.")
        return

    repositories = create_repositories(variables)
    oee_event_repository = repositories.get("oee_event")

    for _, row in oee_event_data.iterrows():
        external_id = row["externalId"]
        space = row["space"]
        try:
            oee_event_repository.delete_event(external_id, space)
            logger.info(f"Evento com externalId '{external_id}' e space '{space}' deletado com sucesso. ({_})")
        except Exception as e:
            logger.error(f"Erro ao deletar o evento com externalId '{external_id}' e space '{space}': {e}")

def run():
    """
        ATENÇÃO: Este script irá deletar todos os eventos com um determinado prefixo e para um site especifico.
        isto se fez necessário pois os eventos de Excess MDR para o site de FORLI (STS-FOR) foram criados erroneamente;
    """
    variables = EnvVariables()
    instance_space = variables.cognite.default_data_model_instances_space

    # Defina o site e prefixo do evento para a query
    site_external_id = "STS-FOR"  # Exemplo de ID do site
    event_prefix = "Ex"

    # Executa a consulta e exibe o resultado
    oee_event_data = get_all_oee_event_data(variables, site_external_id, event_prefix)
    
    if oee_event_data.empty:
        logger.info("Nenhum dado encontrado para os parâmetros fornecidos.")
    else:
        logger.info(f"Dados de eventos OEE encontrados:\n{oee_event_data}")

        # Chama a função para deletar os eventos encontrados
        delete_oee_events(variables, oee_event_data)

if __name__ == "__main__":
    run()
