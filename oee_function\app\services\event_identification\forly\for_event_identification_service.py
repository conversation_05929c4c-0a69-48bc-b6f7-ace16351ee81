from typing import Any

import pandas as pd

from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.hourly_data_compounding import (
    HourlyDataCompounding,
)


class ForCompoundingEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
    ) -> None:
        super().__init__()
        if mdr is not None:
            self._valid_prod_ids = list(mdr.productId.unique())
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start -  ProductionLineStatus = 'STOPPED' (0) and Batch = Batch ID without a letter T
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] > 0)
                & (data["BatchID"].astype(str).str.startswith("000"))
            )
        )
        # event trigger end   - ProductionLineStatus == 'RUNNING' (0) or Product = Product ID that is NOT ON list
        data = data.assign(
            event1a_end=(
                (
                    (data["ProductionLineStatus"] == 0)
                    & (data["ProductionLineStatus"].shift(1) > 0)
                )
                | (~data["BatchID"].astype(str).str.startswith("000"))
            )
        )
        
        # fix event trigger start
        data = data.assign(
            event1a_start=(
                data["event1a_start"]
                & (~data["event1a_start"].shift(1).fillna(False))
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIa event
        :rtype: pd.DataFrame
        """

        # first form of event start - ProductionLineStatus = 'RUNNING' (1) and Batch = Batch ID with letter T
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus"] == 0)
                & (~data["BatchID"].astype(str).str.startswith("000"))
                & (~data["BatchID"].isna())
            )
        )

        # event end - ProductionLineStatus = 'STOPPED' (0) or Batch = Batch ID without a letter T
        data = data.assign(
            event2a_end=(
                (data["ProductionLineStatus"] > 0)
                | (data["BatchID"].astype(str).str.startswith("000"))
            )
        )
        
        data = data.assign(
            event2a_start=(
                data["event2a_start"]
                & (~data["event2a_start"].shift(1).fillna(False))
            )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIc

        Regra: Batch ID at end of the Event == Batch ID at start of the Event
        Then a Not Running During Product Trial Event is generated

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIc event
        :rtype: pd.DataFrame
        """

        # event start - ProductionLineStatus > 0 (STOPPED) and Batch = Batch ID with letter T (Trial Product)
        data = data.assign(
            event2c_start=(
                (data["ProductionLineStatus"] > 0)
                & (~data["BatchID"].astype(str).str.startswith("000"))
            )
        )
        
        # fix event trigger start - remove consecutive duplicates while maintaining correct conditions
        data = data.assign(
            event2c_start=(
                data["event2c_start"]
                & (~data["event2c_start"].shift(1).fillna(False))
            )
        )

        # Guardar Product ID e Batch ID do start para validação
        data = data.assign(
            _batch_id_at_start=data["BatchID"].where(data["event2c_start"])
        )
        # Preencher valores forward para manter Product ID e Batch ID do start em todas as linhas até o end
        data["_batch_id_at_start"] = data["_batch_id_at_start"].ffill()

        # event end - ProductionLineStatus = 0 (RUNNING) 
        # OR Batch ID does not report a Trial Product)
        data = data.assign(
            event2c_end=(
                (data["ProductionLineStatus"] == 0)
                | (data["BatchID"].astype(str).str.startswith("000"))
            )
        )

        # Filtrar apenas linhas com start ou end para otimização
        data = data[
            data["event2c_start"] | data["event2c_end"]
        ]

        # Validação específica para evento 2c: Batch ID no end == Batch ID no start
        # Verifica que o BatchID no start é igual ao próximo end
        data = data.assign(
            event2c_start=(
                data["event2c_start"]
                & (data["BatchID"] == data["BatchID"].shift(-1))
            )
        )

        # Validação específica para evento 2c: Batch ID no end == Batch ID no start
        # Verifica que o BatchID no end é igual ao start do evento
        data = data.assign(
            event2c_end=(
                data["event2c_end"]
                & (data["BatchID"] == data["_batch_id_at_start"])
            )
        )

        # Limpar colunas auxiliares
        data = data.drop(columns=["_batch_id_at_start"], errors="ignore")

        return data

    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IId

        Regra: Batch ID at end of the Event != Batch ID at start of the Event
        Then a Not Running Event is generated

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IId event
        :rtype: pd.DataFrame
        """

        # event start - ProductionLineStatus > 0 (STOPPED) and Batch = Batch ID with letter T (Trial Product)
        # Igual ao evento 2c
        data = data.assign(
            event2d_start=(
                (data["ProductionLineStatus"] > 0)
                & (~data["BatchID"].astype(str).str.startswith("000"))
            )
        )

        # fix event trigger start - remove consecutive duplicates while maintaining correct conditions
        data = data.assign(
            event2d_start=(
                data["event2d_start"]
                & (~data["event2d_start"].shift(1).fillna(False))
            )
        )

        # Guardar Batch ID do start para validação
        data = data.assign(
            _batch_id_at_start=data["BatchID"].where(data["event2d_start"])
        )
        # Preencher valores forward para manter Product ID e Batch ID do start em todas as linhas até o end
        data["_batch_id_at_start"] = data["_batch_id_at_start"].ffill()

        # event end - ProductionLineStatus = 0 (RUNNING) 
        # OR Batch ID does not report a Trial Product)
        # Igual ao evento 2c
        data = data.assign(
            event2d_end=(
                (data["ProductionLineStatus"] == 0)
                | (data["BatchID"].astype(str).str.startswith("000"))
            )
        )

        # Filtrar apenas linhas com start ou end para otimização
        data = data[
            data["event2d_start"] | data["event2d_end"]
        ]

        # Validação específica para evento 2d: Batch ID no end != Batch ID no start
        # Verifica que o BatchID no start é diferente do próximo end
        data = data.assign(
            event2d_start=(
                data["event2d_start"]
                & data["BatchID"].notna()
                & data["BatchID"].shift(-1).notna()
                & (data["BatchID"] != data["BatchID"].shift(-1))
            )
        )

        # Validação específica para evento 2d: Batch ID no end != Batch ID no start
        # Verifica que o BatchID no end é diferente do start do evento
        data = data.assign(
            event2d_end=(
                data["event2d_end"]
                & data["BatchID"].notna()
                & data["_batch_id_at_start"].notna()
                & (data["BatchID"] != data["_batch_id_at_start"])
            )
        )

        # Limpar colunas auxiliares
        data = data.drop(columns=["_batch_id_at_start"], errors="ignore")

        return data

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self):
        pass
