import pandas as pd


class NarProductDescriptionMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            0: "ZERO",
            1: "RAYONIER",
            2: "BUCKEYE",
            3: "AMELIANIER",
            4: "TEMBEC",
            5: "BORREGAARD",
            6: "BAHIA",
            7: "BORREGAARD",
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})
