import pandas as pd


class WilProductMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            202: "FO 0202B6 SF3001 NATURAL",
            203: "FO 0203B6 SF3001 NATURAL",
            205: "FO 0205B4 SF3001 NATURAL",
            214: "FO 0214B1 SF3001 NATURAL",
            300: "FO 0300B0 SF3001 NATURAL",
            309: "FO 0309B4 SF3001 NATURAL",
            320: "FO 0320B0 SF3001 NATURAL",
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})
