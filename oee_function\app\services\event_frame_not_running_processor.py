from datetime import <PERSON><PERSON><PERSON>
from typing import Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration


class EventFrameNotRunningProcessor:
    def __init__(self) -> None:
        self._duration_not_running = 3600

    def apply_not_running_rule(
        self,
        data: pd.DataFrame,
        configurations: ReportingSiteConfiguration,
        reporting_line: str,
    ) -> pd.DataFrame:
        not_running_config = configurations.get_not_running_configuration(
            reporting_line
        )

        if not not_running_config:
            return data

        for not_running in not_running_config:
            from_event, to_event = not_running

            condition_not_running = (
                data["total_duration_seconds"] >= self._duration_not_running
            )

            data.loc[
                (
                    (data["event_definition"] == from_event)
                    & condition_not_running
                ),
                "event_definition",
            ] = to_event

        return data
