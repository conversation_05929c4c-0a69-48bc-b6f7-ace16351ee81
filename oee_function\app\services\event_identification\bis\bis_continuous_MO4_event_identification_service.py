from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.rate_loss_time_continuous import RateLossTimeContinuous
from app.utils.constants import Constants as const
from app.utils.uom_conversion import kg_to_mt
import app.utils.event_detection_utils as ed_utils

class BisMO4EventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        
        self._total_produced = pd.Series(dtype=float)
        self._not_running = pd.Series(dtype=bool)
        
        self._rlt = RateLossTimeContinuous(
            reporting_line_external_id=reporting_line_external_id,
            msdp=msdp
        )
        
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc
        }

        self._day_data = None

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        data.drop(columns=["NetProduction"], inplace=True)
        # Event trigger start
        # ProductionLineStatus5 < 6700 and ProductionLineStatus6 < 6700 and ProductionLineStatus7 < 6700 and ProductionLineStatus8 < 6700
        data = data.assign(
            event1a_start=self.not_running_condition_MO4(data)
        )

        # Event trigger end
        # ProductionLineStatus5 > 6700 for 5min or ProductionLineStatus6 > 6700 for 5min or ProductionLineStatus7 > 6700 for 5min or ProductionLineStatus8 > 6700 for 5min
        data = data.assign(
            event1a_end=self.starting_up_condition_MO4(data)
        )

        data = data.assign(
            event1a_start = (
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) == False)
            )
        )
        
        data.reset_index(inplace=True, drop=False)

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        Identifies events type IIa

        """
        
        data.drop(columns=["NetProduction"], inplace=True)
        
        # Event trigger start
        # Only trigger Starting Up after a Not Running, so as to avoid false positives due to data fluctuations
        data["not_running"] = self.not_running_condition_MO4(data)
        
        # ProductionLineStatus5 > 6700 for 5min or ProductionLineStatus6 > 6700 for 5min or ProductionLineStatus7 > 6700 for 5min or ProductionLineStatus8 > 6700 for 5min
        data = data.assign(
            event2a_start=self.starting_up_condition_MO4(data)
        )

        # Event trigger end
        # ProductionLineStatus1 >= 2500 or ProductionLineStatus2 >= 2500 or ProductionLineStatus3 >= 2500 or ProductionLineStatus4 >= 2500 
        event2a_end = (
            ((data["ProductionLineStatus1"].shift(1) < 2500) & (data["ProductionLineStatus1"] >= 2500)) |
            ((data["ProductionLineStatus2"].shift(1) < 2500) & (data["ProductionLineStatus2"] >= 2500)) |
            ((data["ProductionLineStatus3"].shift(1) < 2500) & (data["ProductionLineStatus3"] >= 2500)) |
            ((data["ProductionLineStatus4"].shift(1) < 2500) & (data["ProductionLineStatus4"] >= 2500))
        )

        data = data.loc[data["not_running"] | data["event2a_start"]]

        data = data.assign(
            event2a_start=(
                data["event2a_start"] & data["not_running"].shift(1)
            )
        )

        event2a_end = event2a_end.to_frame("event2a_end")
        event2a_end = event2a_end.loc[event2a_end["event2a_end"]]
        data = pd.concat([data, event2a_end], axis=1).fillna(False)

        data.reset_index(inplace=True, drop=False)

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    
    def identify_events_typeIIIa(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "first", [const.MSDP, const.SCHEDULED_RATE], 1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT_NO_DEMAND] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT_NO_DEMAND]

        return day_data
    
    def identify_events_typeIIIb(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "first", [const.MSDP, const.SCHEDULED_RATE], 1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] > 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data
    
    def identify_events_typeIIIc(self, data: pd.DataFrame, **args):
        data[const.TOTAL_PRODUCED] = self.calculate_net_production_fn(data)
        data[const.RUNNING_TIME] = self.not_running_fn(data)

        day_data = self._rlt.create_day_data(data, "first", [const.MSDP, const.SCHEDULED_RATE], 1)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[const.RLT] < 0]

        day_data["total_duration_seconds"] = day_data[const.RLT]

        return day_data
    
    def calculate_net_production_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._total_produced.empty:
            return self._total_produced
        
        net_production = kg_to_mt(df["NetProduction"])
        
        self._total_produced = net_production

        return net_production
    
    def not_running_fn(self, df: pd.DataFrame) -> pd.Series:
        if not self._not_running.empty:
            return self._not_running
        
        # Work with copy to avoid modifying original
        df_work = df.copy()
        
        df_work["not_running_start"] = self.not_running_condition_MO4(df_work)
        df_work["not_running_start"] = (
            (df_work["not_running_start"] == True)
            & (df_work["not_running_start"].shift(1) == False)
        )
        df_work["not_running_end"] = self.starting_up_condition_MO4(df_work)
        
        df_work["starting_up_start"] = df_work["not_running_end"]
        df_work["starting_up_end"] = (
            ((df_work["ProductionLineStatus1"].shift(1) < 2500) & (df_work["ProductionLineStatus1"] >= 2500)) |
            ((df_work["ProductionLineStatus2"].shift(1) < 2500) & (df_work["ProductionLineStatus2"] >= 2500)) |
            ((df_work["ProductionLineStatus3"].shift(1) < 2500) & (df_work["ProductionLineStatus3"] >= 2500)) |
            ((df_work["ProductionLineStatus4"].shift(1) < 2500) & (df_work["ProductionLineStatus4"] >= 2500))
        )

        # Filter to only include relevant rows for processing
        mask = df_work["not_running_start"] | df_work["starting_up_start"]
        df_filtered = df_work.loc[mask].copy()
        
        # Ensure starting_up only triggers after not_running
        df_filtered["starting_up_start"] = (
            df_filtered["starting_up_start"] & df_filtered["not_running_start"].shift(1).fillna(False)
        )
        
        # Update the original dataframe with the corrected starting_up_start values
        df_work.loc[mask, "starting_up_start"] = df_filtered["starting_up_start"]

        df_work.reset_index(inplace=True, drop=False)
        df_work = self.__process_event_conditions(df_work)

        not_running = df_work["not_running"] | df_work["starting_up"]
        
        self._not_running = not_running
        
        return not_running
    
    def not_running_condition_MO4(self, data: pd.DataFrame) -> pd.Series:
        not_running = (
            (data["ProductionLineStatus5"] < 6700)
            & (data["ProductionLineStatus6"] < 6700)
            & (data["ProductionLineStatus7"] < 6700)
            & (data["ProductionLineStatus8"] < 6700)
        )

        return not_running
    
    def starting_up_condition_MO4(self, data: pd.DataFrame) -> pd.Series:
        starting_up = (
            (
                ed_utils.detect_sustained_transition(
                    df=data,
                    col="ProductionLineStatus5",
                    threshold=6700,
                    duration="5min",
                    condition="gt"
                )
            ) | (
                ed_utils.detect_sustained_transition(
                    df=data,
                    col="ProductionLineStatus6",
                    threshold=6700,
                    duration="5min",
                    condition="gt"
                )
            ) | (
                ed_utils.detect_sustained_transition(
                    df=data,
                    col="ProductionLineStatus7",
                    threshold=6700,
                    duration="5min",
                    condition="gt"
                )
            ) | (
                ed_utils.detect_sustained_transition(
                    df=data,
                    col="ProductionLineStatus8",
                    threshold=6700,
                    duration="5min",
                    condition="gt"
                )
            )
        )

        return starting_up
    
    def __process_event_conditions(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Processes event conditions for 'not_running'.

        :param data: DataFrame containing the data
        """
        # Initialize columns
        data["not_running"] = False
        data["starting_up"] = False

        # Pair and update 'not_running' condition
        ed_utils.pair_start_end_indexes(data, "not_running_start", "not_running_end", "not_running", True)
        ed_utils.pair_start_end_indexes(data, "starting_up_start", "starting_up_end", "starting_up", True)

        return data