from typing import Any
from app.models.loss_category import LossCategory
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from .view_repository import ViewRepository
from ..utils.graphql import generate_query, query_all


class LossCategoryRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        view_repository: ViewRepository,
        data_model_id: DataModelId,
    ) -> None:
        self._cognite_client = cognite_client
        self._view_repository = view_repository
        self._data_model_id = data_model_id
        self._list_name = "listOEELossCategory"

    def get_loss_categories(
        self, filter: dict[str, Any] | None = None
    ) -> list[LossCategory]:
        """
        Returns the loss categories that are included in a given filter.

        :param: desired filter to query loss categories.
        :return: list of Loss Category
        :rtype: list[LossCategory]
        """

        selected_items = """
            externalId
            space
            name
            isActive
        """

        query_result = query_all(
            client=self._cognite_client,
            data_model_id=self._data_model_id,
            list_name=self._list_name,
            query=generate_query(self._list_name, selected_items),
            filter=filter,
        )

        return [LossCategory.from_cognite_response(result) for result in query_result]

    def get_active_loss_categories(self) -> list[LossCategory]:
        """
        Returns all Loss Categories flagged as active.

        :return: list of Loss Category
        :rtype: list[LossCategory]
        """
        filter = {"isActive": {"eq": True}}
        return self.get_loss_categories(filter)
