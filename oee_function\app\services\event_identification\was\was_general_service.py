from typing import Optional

import pandas as pd

from .was_epce_event_identification_service import WasEpceSettings
from .was_fill_event_identification_service import WASFILSettings
from .was_scd_event_identification_service import WASSCDSettings
from .was_mpw_cmp_event_identification_service import WasMpwSettings


class WashingtonGeneralService:
    """
    Service for general operations related to Washington event identification.
    """

    def __init__(self, reporting_line_external_id: str, mdr: pd.DataFrame):
        self.reporting_line_external_id = reporting_line_external_id
        self.was_fil_settings = WASFILSettings(reporting_line_external_id)
        self.was_mpw_settings = WasMpwSettings(reporting_line_external_id, mdr)
        self.was_scd_settings = WASSCDSettings(reporting_line_external_id, mdr)
        self.was_epce_settings = WasEpceSettings(reporting_line_external_id, mdr)

    def prepare_hourly_data(
        self, data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Prepares hourly data for the given reporting line.
        
        :param data: DataFrame containing the time series data.
        :param reporting_line_external_id: External ID of the reporting line.
        :return: DataFrame with hourly data prepared.
        """
        
        if self.reporting_line_external_id.startswith("RLN-WASFIL"):  # WASHINGTON FILLAMENTS
            data = self.was_fil_settings.prepare_hourly_data(data)
            data = self.was_fil_settings.create_total_feed_column(data)
            
        if self.reporting_line_external_id.startswith("RLN-WASSCD"): # WASHINGTON SCD
            data = self.was_scd_settings.prepare_hourly_data(data)

        if self.reporting_line_external_id.startswith("RLN-WASMPW"):  # WASHINGTON MPW
            data = self.was_mpw_settings.prepare_hourly_data(data)
        
        if self.reporting_line_external_id.startswith("RLN-WASEPCC"):  # WASHINGTON EPCE
            data = self.was_epce_settings.prepare_hourly_data(data)
        
        return data
