from typing import Any, Dict, <PERSON><PERSON>

import pandas as pd
from app.utils.rlt_utils import RLT_DEFINITIONS

from ..infra.logger_adapter import get_logger

log = get_logger()


# PT-BR: Limite em segundos para considerar um evento RLT como "baixa duração"
# Eventos com duração entre -900 e 900 segundos são considerados de baixa duração
# EN: Threshold in seconds to consider an RLT event as "short duration"
# Events with duration between -900 and 900 seconds are considered short duration
RLT_SHORT_DURATION_THRESHOLD_SECONDS = 900  # 15 minutes

# PT-BR: Categorização padrão para eventos RLT de baixa duração
# EN: Default categorization for short duration RLT events
CONTINUOUS_RLT_SHORT_DURATION_CATEGORIZATION: Dict[str, Any] = {
    "subcat_level1": "Process and Technology",
    "subcat_level2": "Normal Process Variability - Comment Required",
    "event_code": "Rate Loss",
    "metric_code": "Performance",
}

# PT-BR: Tipos de processo (Batch, Continuous, Compounding)
# EN: Process types (Batch, Continuous, Compounding)
PC_TYPE_BATCH: Tuple[str, ...] = ("Batch",)
PC_TYPE_CONTINUOUS: Tuple[str, ...] = ("Continuous", "Continuous 2 Products")
PC_TYPE_COMPOUNDING: Tuple[str, ...] = ("Compounding",)


class RltShortDurationService:
    """
    PT-BR: Serviço responsável por categorizar e auto-assinar eventos RLT (Rate Loss Time) de baixa duração.
    Eventos RLT com duração entre -900 e 900 segundos são tratados de forma especial.

    EN: Service responsible for categorizing and auto-assigning short duration RLT (Rate Loss Time) events.
    RLT events with duration between -900 and 900 seconds are treated specially.
    """

    def __init__(self, event_frames_df: pd.DataFrame, pc_type: str) -> None:
        """
        PT-BR: Inicializa o serviço de eventos RLT de baixa duração.
        EN: Initializes the short duration RLT events service.

        :param event_frames_df: DataFrame contendo os eventos a serem processados
        :type event_frames_df: pd.DataFrame
        """
        self._event_frames_df = event_frames_df
        self._pc_type = pc_type

    def _get_short_duration_rlt_mask(self) -> pd.Series:
        """
        PT-BR: Retorna uma máscara booleana identificando eventos RLT de baixa duração.
        Eventos com duração entre -900 e 900 segundos são considerados de baixa duração.
        EN: Returns a boolean mask identifying short duration RLT events.
        Events with duration between -900 and 900 seconds are considered short duration.

        :return: Máscara booleana para eventos RLT com duração entre -900 e 900 segundos
        :rtype: pd.Series
        """
        is_rlt_event = self._event_frames_df["def"].isin(RLT_DEFINITIONS)
        duration = self._event_frames_df["total_duration_seconds"]
        is_short_duration = (
            duration >= -RLT_SHORT_DURATION_THRESHOLD_SECONDS
        ) & (duration <= RLT_SHORT_DURATION_THRESHOLD_SECONDS)
        return is_rlt_event & is_short_duration

    def categorize_short_duration_rlt(self) -> pd.DataFrame:
        """
        PT-BR: Categoriza eventos RLT de baixa duração com categorização padrão.
        Eventos RLT com duração entre -900 e 900 segundos serão categorizados com:
        - subcat_level1: Process and Technology
        - subcat_level2: Normal Process Variability - Comment Required
        - event_code: Rate Loss
        - metric_code: Performance

        EN: Categorizes short duration RLT events with default categorization.
        RLT events with duration between -900 and 900 seconds will be categorized with:
        - subcat_level1: Process and Technology
        - subcat_level2: Normal Process Variability - Comment Required
        - event_code: Rate Loss
        - metric_code: Performance

        :return: DataFrame com os eventos RLT de baixa duração categorizados
        :rtype: pd.DataFrame
        """
        log.info("Categorizing short duration RLT events")

        if self._event_frames_df.empty:
            log.info("Empty dataframe provided, skipping RLT categorization")
            return self._event_frames_df

        required_columns = ["def", "total_duration_seconds"]
        missing_columns = [
            col
            for col in required_columns
            if col not in self._event_frames_df.columns
        ]

        if missing_columns:
            log.warning(
                f"Missing required columns {missing_columns} for RLT categorization; skipping"
            )
            return self._event_frames_df

        result_df = self._event_frames_df.copy()
        short_duration_rlt_mask = self._get_short_duration_rlt_mask()

        if not short_duration_rlt_mask.any():
            log.info("No short duration RLT events found for categorization")
            return result_df

        if self._pc_type in PC_TYPE_CONTINUOUS:
            categorization = CONTINUOUS_RLT_SHORT_DURATION_CATEGORIZATION
        else:
            return result_df

        for column, value in categorization.items():
            if column in result_df.columns:
                result_df.loc[short_duration_rlt_mask, column] = value

        categorized_count = short_duration_rlt_mask.sum()
        log.info(
            f"Categorized {categorized_count} short duration RLT events "
            f"(duration between -{RLT_SHORT_DURATION_THRESHOLD_SECONDS} and {RLT_SHORT_DURATION_THRESHOLD_SECONDS} seconds)"
        )

        return result_df

    def auto_assign_short_duration_rlt(self) -> pd.DataFrame:
        """
        PT-BR: Auto-assina eventos RLT de baixa duração alterando o status para "Assigned".
        Eventos RLT com duração entre -900 e 900 segundos terão seu status alterado automaticamente.

        EN: Auto-assigns short duration RLT events by changing status to "Assigned".
        RLT events with duration between -900 and 900 seconds will have their status changed automatically.

        :return: DataFrame com os eventos RLT de baixa duração auto-assinados
        :rtype: pd.DataFrame
        """
        log.info("Auto-assigning short duration RLT events")

        if self._pc_type not in PC_TYPE_CONTINUOUS:
            log.info("Skipping RLT auto-assignment for non-continuous process")
            return self._event_frames_df

        if self._event_frames_df.empty:
            log.info("Empty dataframe provided, skipping RLT auto-assignment")
            return self._event_frames_df

        required_columns = ["def", "total_duration_seconds", "status"]
        missing_columns = [
            col
            for col in required_columns
            if col not in self._event_frames_df.columns
        ]

        if missing_columns:
            log.warning(
                f"Missing required columns {missing_columns} for RLT auto-assignment; skipping"
            )
            return self._event_frames_df

        result_df = self._event_frames_df.copy()
        short_duration_rlt_mask = self._get_short_duration_rlt_mask()

        if not short_duration_rlt_mask.any():
            log.info("No short duration RLT events found for auto-assignment")
            return result_df

        result_df.loc[short_duration_rlt_mask, "RLT_SHORT_DURATION"] = True

        assigned_count = short_duration_rlt_mask.sum()
        log.info(
            f"Auto-assigned {assigned_count} short duration RLT events "
            f"(duration between -{RLT_SHORT_DURATION_THRESHOLD_SECONDS} and {RLT_SHORT_DURATION_THRESHOLD_SECONDS} seconds)"
        )

        return result_df
