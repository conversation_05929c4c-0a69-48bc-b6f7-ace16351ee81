import pandas as pd


class WinProductionLineStatusMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
            0: "Running",
            1: "Unknown Crash",
            2: "Operator Error",
            3: "Die Pressure",
            4: "Lost Ends",
            5: "Bad Fiber",
            6: "Not Scheduled",
            7: "PM",
            8: "Plant Shutdown",
            9: "Turnaround",
            10: "ManPower",
            11: "Raw Materials Unavailable",
            12: "Splice-Out",
            15: "Line Control Logic",
            16: "Site Electrical",
            21: "Tension Bars",
            22: "Tension Bars",
            23: "Creel Rack",
            24: "C<PERSON>l Rack",
            31: "<PERSON><PERSON><PERSON>",
            32: "<PERSON><PERSON><PERSON>",
            33: "<PERSON><PERSON><PERSON>",
            34: "<PERSON><PERSON><PERSON>",
            41: "Die Waves or Leaks",
            42: "Die Heaters",
            43: "Die Waves or Leaks",
            44: "Extruder",
            45: "Extruder",
            51: "Water Bath",
            52: "Reform Rolls",
            53: "Water Bath",
            61: "<PERSON><PERSON><PERSON>",
            62: "<PERSON>uller",
            63: "Puller",
            64: "Pelletizer",
            65: "Pelletizer",
            66: "Pelletizer",
            67: "Puller",
            71: "Raw Material Convey",
            72: "Daybin/Dryer Convey",
            73: "Dust Collection",
            74: "Screener",
            81: "Line Control Logic",
            82: "Winder",
            83: "Winder",
            84: "Accumulator",
            85: "Accumulator",
            91: "Quality",
            92: "Scheduled Mantainence",
            93: "Oppurtunity Maintenance",
            94: "Weather",
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        return data.replace({time_series: self._mapping_dict})
    
