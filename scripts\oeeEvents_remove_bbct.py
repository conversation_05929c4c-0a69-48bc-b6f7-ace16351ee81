import json
import logging
import os
import sys
from datetime import datetime

import pandas as pd
from cognite.client.data_classes.data_modeling import NodeId
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables

"""
    ATENÇÃO: Este script irá localizar e deletar BBCT específicas com base nas linhas fornecidas.
"""

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from oee_function.app.infra.logger_adapter import get_logger
from oee_function.app.repositories.bbct_repository import BbctRepository
from oee_function.app.repositories.view_repository import ViewRepository

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

LINES = [
    "RLN-FRACNV101",
    "RLN-FRACNV102",
    "RLN-FRACNV103"
] # External IDs das linhas

def require_current_day():
    """
    Solicita ao usuário que insira o dia atual para liberar a execução do script.
    """
    today = datetime.now().strftime("%d")
    print("\nAtenção: Este processo é irreversivel para continuar, digite o dia atual (somente número): ")
    
    user_input = input("Dia atual: ").strip()
    
    if user_input != today:
        logger.error("Dia inserido incorreto. Execução abortada.")
        sys.exit(1)  # Encerra o programa com código de erro

    logger.info("Dia confirmado corretamente. Continuando execução.")

def create_cognite_client(variables: EnvVariables):
    """Cria uma instância do cliente Cognite usando as variáveis de ambiente."""
    return CogniteClientFactory.create(variables)

def create_repositories(variables: EnvVariables):
    """Inicializa os repositórios necessários para acessar os dados."""
    cognite_client = create_cognite_client(variables)
    data_model_id = DataModelId(
        variables.cognite.data_model_space,
        variables.cognite.data_model_external_id,
        variables.cognite.data_model_version,
    )
    return {
        "oee_bbct": BbctRepository(
            cognite_client,
            data_model_id,
            view_repository=ViewRepository(cognite_client, data_model_id),
        ),
    }


def build_dynamic_query(lines):
    """Constrói a query dinâmica para buscar BBCT com base nos filtros fornecidos."""
    filters = []
    if lines:
        filters.append(f'{{ refReportingLine: {{ externalId: {{in:{json.dumps(lines)}}}}}}}')
    
    filters_str = ", ".join(filters)
    query = f"""
        query MyQuery($after: String) {{
            listOEEBBCT(
                first: 1000
                filter: {{ and: [{filters_str}]}}
                after: $after
            )
            {{
                items {{
                    externalId
                    space
                    refReportingLine {{
                        externalId
                    }}
                }}
                pageInfo {{
                    endCursor
                    hasNextPage
                }}
            }}
        }}
    """
    return query

def get_filtered_oee_bbct(variables: EnvVariables) -> pd.DataFrame:
    """Busca BBCT que atendem aos filtros fornecidos."""
    repositories = create_repositories(variables)
    oee_bbct_repository = repositories.get("oee_bbct")
    if oee_bbct_repository is None:
        logger.error("OEEBBCT não encontrado.")
        return pd.DataFrame()
    
    # Constrói a query com base nos filtros fornecidos
    query = build_dynamic_query(LINES)

    # Executa a query e retorna os resultados como DataFrame
    bbct = oee_bbct_repository.list_all(query=query)
    if not bbct:
        return pd.DataFrame()

    return pd.DataFrame(bbct)


def delete_oee_bbct_in_batch(variables: EnvVariables, oee_bbct_data: pd.DataFrame, batch_size: int = 100) -> None:
    """Exclui BBCT listadas em oee_bbct_data em lotes."""
    if oee_bbct_data.empty:
        logger.info("Nenhuma BBCT para deletar.")
        return

    repositories = create_repositories(variables)
    oee_bbct_repository = repositories.get("oee_bbct")

    # Prepara a lista de NodeIds
    nodes_to_delete = [
        NodeId(external_id=row["externalId"], space=row["space"])
        for _, row in oee_bbct_data.iterrows()
    ]


    try:
        for i in range(0, len(nodes_to_delete), batch_size):
            batch = nodes_to_delete[i:i + batch_size]
            oee_bbct_repository._cognite_client.data_modeling.instances.delete(nodes=batch)
            logger.info(f"Lote {i//batch_size + 1}: {len(batch)} BBCT deletadas com sucesso.")
    except Exception as e:
        logger.error(f"Erro ao deletar lote: {e}")

def run():
    """Localiza e exclui BBCT com base nos filtros definidos."""
    
    require_current_day()
    
    variables = EnvVariables()
    
    # Busca BBCT com os filtros fornecidos
    logger.info("Buscando BBCT que atendem aos filtros...")
    oee_bbct_data = get_filtered_oee_bbct(variables)
    
    if oee_bbct_data.empty:
        logger.info("Nenhuma BBCT encontrada com os filtros fornecidos.")
    else:
        logger.info(f"Encontradas {len(oee_bbct_data)} BBCT:")
        
        # Executa a deleção
        delete_oee_bbct_in_batch(variables, oee_bbct_data, batch_size=100)
        
        logger.info("Script concluído com sucesso!")

if __name__ == "__main__":
    run()
    
