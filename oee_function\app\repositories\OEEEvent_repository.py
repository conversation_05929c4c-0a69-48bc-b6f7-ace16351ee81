from typing import Any, List, Optional

import pandas as pd
from cognite.client import Cognite<PERSON>lient
from cognite.client.data_classes.data_modeling import NodeId
from cognite.client.data_classes.data_modeling.data_models import DataModelId

from app.models.oee_event import OEEEvent
from .view_repository import ViewRepository


class OEEEventRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        data_model_id: DataModelId,
        view_repository: Optional[ViewRepository] = None,
    ) -> None:
        self._cognite_client = cognite_client
        self._data_model_id = data_model_id
        self._view_repository = view_repository

    def query_all_oee_events(self, site_external_id: str, event_prefix: str = "Ex") -> List[dict]:
        all_results = []
        cursor = None

        while True:
            query_result = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                self.build_paginated_query(),
                {"siteExternalId": site_external_id, "eventPrefix": event_prefix, "after": cursor},
            )["listOEEEvent"]

            items = query_result["items"]
            all_results.extend(
                {"externalId": entry["externalId"], "space": entry["space"]}
                for entry in items
                if entry.get("externalId") and entry.get("space")
            )

            page_info = query_result["pageInfo"]
            if not page_info["hasNextPage"]:
                break

            cursor = page_info["endCursor"]

        return all_results

    def get_oee_event_data_as_dataframe(
        self, site_external_id: str, event_prefix: str = "Ex"
    ) -> pd.DataFrame:
        data = self.query_all_oee_events(site_external_id, event_prefix)

        if not data:
            return pd.DataFrame()  

        return pd.DataFrame(data)
    
    def query_to_dataframe(
        self, query: str, variables: dict[str, Any] | None = None
    ) -> pd.DataFrame:
        data = self.list_all(query=query, variables=variables)

        if not data:
            return pd.DataFrame() 

        return pd.DataFrame(data)
    
    def list_all(self, query: str, variables: dict[str, Any] | None = None) -> List[dict]:
        all_results = []
        cursor = None
        variables = variables or {} 
        

        while True:
            variables["after"] = cursor

            
            query_result = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                query,
                variables,
            )["listOEEEvent"]

            items = query_result["items"]
            all_results.extend(
              {
                  "externalId": entry["externalId"],
                  "space": entry["space"],
                  "eventDefinition": entry.get("eventDefinition"),
                  "refSite": entry.get("refSite", {}).get("externalId"),
                  "refUnit": entry.get("refUnit", {}).get("externalId"),
                  "refReportingLine": entry.get("refReportingLine", {}).get("externalId"),
                  
                  "refOEEEventDetail": [
                      {
                          "externalId": detail.get("externalId"),
                          "space": detail.get("space"),
                      }
                      for detail in entry.get("refOEEEventDetail", {}).get("items", [])
                      if detail.get("externalId") and detail.get("space")
                  ]
                  
                  
              }
              for entry in items
              if entry.get("externalId") and entry.get("space")
            )

            page_info = query_result["pageInfo"]
            if not page_info["hasNextPage"]:
                break

            cursor = page_info["endCursor"]

        return all_results

    def build_paginated_query(self):
        return """
        query MyQuery($siteExternalId: ID!, $eventPrefix: String!, $after: String) {
          listOEEEvent(
            first: 1000
            filter: {
              and: [
                { refSite: { externalId: { eq: $siteExternalId } } },
                { eventDefinition: { prefix: $eventPrefix } }
              ]
            }
            after: $after
          ) {
            items {
              externalId
              space
            }
            pageInfo {
              endCursor
              hasNextPage
            }
          }
        }
        """
        
    def query_all_oee_events_from_units(self, units_external_ids: list[dict[str, dict[str, str]]], eventsDefinition: str) -> List[dict]:
        all_results = []
        cursor = None

        while True:
            query_result = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                self.build_paginated_units_query(),
                {"units": units_external_ids, "eventsDefinition": eventsDefinition, "after": cursor},
            )["listOEEEvent"]

            items = query_result["items"]
            
            all_results.extend(items)
            
            page_info = query_result["pageInfo"]
            
            if not page_info["hasNextPage"]:
                break

            cursor = page_info["endCursor"]

        return all_results
        
    
    def build_paginated_units_query(self):
        return """
        query MyQuery($units: [_ListReportingUnitFilter!]!, $eventsDefinition: [String!], $after: String) {
          listOEEEvent(
            first: 1000
            filter: {
              and: [
                { refUnit: { or: $units } },
                { eventDefinition: { in: $eventsDefinition } }
              ]
            }
            after: $after
          ) {
            items {
              externalId
              space
            }
            pageInfo {
              endCursor
              hasNextPage
            }
          }
        }
        """

    def create_oee_events(self, events: List[dict[str, Any]]) -> None:
        if not events:
            return

        if not self._view_repository:
            raise ValueError("Missing View Repository when instantiating the OEEEvent Repository.")

        view = self._view_repository.get_view_id("OEEEvent")
        events_to_create = []
        for event in events:
          
          newEvent = OEEEvent(
            external_id=event["externalId"],
            space=event["space"],
            batch=None,
            refOEEProduct=None,
          )

          events_to_create.append(
              newEvent.convert_to_cognite_node(view_id=view, exclude_none=False, keys_to_exclude=["external_id"])
          )

        paginated_nodes = [events_to_create[1000 * i : 1000 * (i + 1)] for i in range((len(events_to_create) // 1000) + 1)]

        for entries in paginated_nodes:
            self._cognite_client.data_modeling.instances.apply(nodes=entries)

    def convert_to_cognite_node(self, event: dict[str, Any], view: str) -> dict:
        return {
            "view": view,
            "externalId": event["externalId"],
            "space": event["space"]
            # Adicione mais campos conforme necessário
        }


    def delete_event(self, external_id: str, space: str) -> None:
        node_id = NodeId(external_id=external_id, space=space)

        try:
            self._cognite_client.data_modeling.instances.delete(nodes=[node_id])
            print(f"Evento com externalId '{external_id}' e space '{space}' deletado com sucesso.")
        except Exception as e:
            print(f"Erro ao deletar o evento com externalId '{external_id}' e space '{space}': {e}")


def query_all_oee_events_from_units(self, units_external_ids: list[dict[str, dict[str, str]]], eventsDefinition: str) -> List[dict]:
        all_results = []
        cursor = None

        while True:
            query_result = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                self.build_paginated_units_query(),
                {"units": units_external_ids, "eventsDefinition": eventsDefinition, "after": cursor},
            )["listOEEEvent"]

            items = query_result["items"]
            all_results.extend(items)

            page_info = query_result["pageInfo"]
            if not page_info["hasNextPage"]:
                break

            cursor = page_info["endCursor"]

            break

        return all_results


def query_all_oee_events_from_units(self, units_external_ids: list[dict[str, dict[str, str]]], eventsDefinition: str) -> List[dict]:
        all_results = []
        cursor = None

        while True:
            query_result = self._cognite_client.data_modeling.graphql.query(
                self._data_model_id,
                self.build_paginated_units_query(),
                {"units": units_external_ids, "eventsDefinition": eventsDefinition, "after": cursor},
            )["listOEEEvent"]

            items = query_result["items"]
            all_results.extend(items)

            page_info = query_result["pageInfo"]
            if not page_info["hasNextPage"]:
                break

            cursor = page_info["endCursor"]

            break

        return all_results


def build_paginated_units_query(self):
        return """
        query MyQuery($units: [_ListReportingUnitFilter!]!, $eventsDefinition: [String!], $after: String) {
          listOEEEvent(
            first: 1000
            filter: {
              and: [
                { refUnit: { or: units } },
                { eventDefinition: { in: $eventsDefinition } }
              ]
            }
            after: $after
          ) {
            items {
              externalId
              space
            }
            pageInfo {
              endCursor
              hasNextPage
            }
          }
        }
        """