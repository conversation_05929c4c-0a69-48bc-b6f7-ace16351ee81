from datetime import <PERSON><PERSON><PERSON>
from typing import Optional, Set

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration


# Set of event definitions that are considered Minor Stops
MINOR_STOP_DEFINITIONS = {"Minor Stops", "Minor Stop", "Minor Stop During Product Trial", "Producing Waste", "Batch Idle"}

# Default duration for Minor Stops in minutes
DEFAULT_MINOR_STOP_DURATION_MINUTES = 15

class EventFrameMinorStopProcessor:
    def __init__(self, duration_in_minutes: Optional[int] = None) -> None:
        self._duration_in_seconds = (duration_in_minutes or DEFAULT_MINOR_STOP_DURATION_MINUTES) * 60

    def apply_minor_stop_rule(
        self,
        data: pd.DataFrame,
        configurations: ReportingSiteConfiguration,
        reporting_line: str,
    ) -> pd.DataFrame:
        minor_stop_config = configurations.get_minor_stop_configuration(reporting_line)

        if not minor_stop_config:
            return data

        # Get events type for the reporting line
        filtered_events = [
            item for item in configurations.events_hierarchy
            if item.reporting_line.external_id == reporting_line
        ]

        # Create a dictionary {event_hierarchy: {event_definition, minor_stop, new_minor_stop}}
        event_hierarchy_map = {
            item.event_hierarchy: {
                "event_definition": item.event_definition,
                "minor_stop": item.minor_stop,
                "new_minor_stop": item.new_minor_stop,
            }
            for item in filtered_events
        }

        # Create a set with events qualified as Minor Stops, based on the configuration
        minor_stop_events = {
            item.event_hierarchy
            for item in filtered_events
            if item.minor_stop is True 
            and item.new_minor_stop in event_hierarchy_map
            and event_hierarchy_map[item.new_minor_stop]["event_definition"] in MINOR_STOP_DEFINITIONS
        }

        data["start_time"] = pd.to_datetime(data["start_time"], errors="coerce")
        data["end_time"] = pd.to_datetime(data["end_time"], errors="coerce")

        # If there are no events qualified as Minor Stops, return the data as is
        if not minor_stop_events:
            print(f"No Minor Stop events for line '{reporting_line}'.")
            return data

        condition: pd.Series[bool] = pd.Series([False] * (data.shape[0] + 1))
        has_overlap_condition: pd.Series[bool] = pd.Series([False] * (data.shape[0] + 1))

        for shift in configurations.shifts:
            _, end_date_shifts = self._create_dates_from_shift(data["start_time"], shift)

            condition_entry = (data["end_time"] < end_date_shifts) & (
                data["total_duration_seconds"] <= self._duration_in_seconds
            )
            has_overlap_condition_entry = (
                end_date_shifts.dt.time == data["start_time"].dt.time
            ) | (end_date_shifts.dt.time == data["end_time"].dt.time)

            condition = condition | condition_entry
            has_overlap_condition = has_overlap_condition | has_overlap_condition_entry

        for from_event, to_event in minor_stop_config:
            if from_event not in minor_stop_events:
                continue  #  Jump to the next iteration

            if "isOpened" not in data.columns:
                data["isOpened"] = False

            data.loc[
                (data["event_definition"] == from_event)
                & (condition)
                & (~has_overlap_condition)
                & (data["isOpened"] == False),
                "event_definition",
            ] = to_event

        return data

    def _create_dates_from_shift(self, timestamps: pd.Series, shift: str):
        shift_timestamp = pd.Timestamp(shift)
        start_date_list: list[pd.Timestamp] = []
        end_date_list: list[pd.Timestamp] = []

        for timestamp in timestamps.tolist():
            ts = shift_timestamp.replace(
                year=timestamp.year,
                month=timestamp.month,
                day=timestamp.day,
                tzinfo=timestamp.tz,
            )
            if (
                shift_timestamp.hour == 0
                and shift_timestamp.minute == 0
                and shift_timestamp.second == 0
            ):
                ts = ts + timedelta(days=1)

            start_date_list.append(ts - timedelta(minutes=DEFAULT_MINOR_STOP_DURATION_MINUTES))
            end_date_list.append(ts)

        return (pd.Series(start_date_list), pd.Series(end_date_list))